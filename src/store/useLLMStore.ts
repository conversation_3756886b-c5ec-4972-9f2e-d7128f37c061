import { defineStore } from 'pinia';
import { computed } from 'vue';
// import { UserInfo } from '@/types'

const llmUserStore = defineStore('@@web_llmUser_edu', {
  state: () => {
    return {
      user: {
        token: '',
      },
    };
  },

  actions: {
    setUserToken(payload: any) {
      // console.log(payload, '>>>>');
      this.user.token = payload;
    },
  },

  persist: true,
});

export default function useLLMUser() {
  const uis = llmUserStore();

  return computed({
    get: () => uis.user || {},

    set(val) {
      uis.setUserToken(val);
    },
  });
}
