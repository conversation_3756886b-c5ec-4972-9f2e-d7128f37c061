import { $http } from '@tanzerfe/http';
import { api } from '@/api';
// 登录
export function Login(query: any) {
  const url = api.getUrl(api.type.server, api.name.login.login, query);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}
export function Address(query: any) {
  const url = api.getUrl(api.type.server, api.name.interface.address, query);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}
