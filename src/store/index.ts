/*
 * @Author: xginger <EMAIL>
 * @Date: 2025-06-03 14:10:38
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-06-09 12:11:53
 * @FilePath: \ehs-org-alloc-mgr\src\store\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineStore } from 'pinia';
import pkg from '../../package.json';
import { Login } from './featchData';

export const useStore = defineStore(`${pkg.name}-store`, {
  persist: true,
  state: (): any => ({
    userInfo: {
      userToken: '',
      menuDataList: [],
      code: '',
      firstRouter: '',
      orgCode: '',
      userName: '',
      zhId: '',
      zhName: '',
      zhLogo: '',
      unitId: '',
      zhPlatformUrl: '',
      userTelphone: '',
      zhLogoUrl: '',
      logoPicUrl: '',
      iconPicUrl: '',
    },
  }),
  getters: {
    // 登录状态
    isLogin(state) {
      return Boolean(state.userInfo.userToken);
    },
  },
  actions: {
    async login(obj: any, callback: any) {
      const res: any = await Login(obj);

      if (res.code == 200) {
        // this.userInfo = res.data;
        this.userInfo.id = res.data.id;
        this.userInfo.logoPicUrl = res.data.logoPicUrl;
        this.userInfo.iconPicUrl = res.data.iconPicUrl;
        this.userInfo.orgCode = res.data.orgCode;
        this.userInfo.code = res.code;
        this.userInfo.userToken = res.data.token;
        this.userInfo.zhLogoUrl = res.data.zhLogoUrl;
        this.userInfo.userName = res.data.userName;
        this.userInfo.zhId = res.data.zhId;
        this.userInfo.zhPlatformUrl = res.data.zhPlatformUrl;
        this.userInfo.userTelphone = res.data.userTelphone;
        this.userInfo.zhName = res.data.zhName;
        this.userInfo.zhLogo = res.data.zhLogo;
        this.userInfo.unitId = res.data.unitId;
        this.userInfo.menuDataList = res.data.resourceVoList.map(
          (item: any) => {
            const newItem = {
              label: item.resName,
              key: item.id,
              icon: item.resIconUrl,
              routeName: item.resUrl,
              childrens:
                item.childrens.length > 0
                  ? item.childrens.map((child: any) => {
                      return {
                        label: child.resName,
                        key: child.id,
                        icon: child.resIconUrl,
                        routeName: child.resUrl,
                      };
                    })
                  : undefined,
            };
            return newItem;
          }
        );
        console.log(this.userInfo.menuDataList, 'menuDataList');
        this.userInfo.firstRouter = this.userInfo.menuDataList[0].routeName;

        if (this.isLogin) {
          callback(this.userInfo.firstRouter);
        }
      } else {
        return;
      }
    },
    reset() {
      this.$reset();
    },
  },
});
export { default as useLLMStore } from './useLLMStore';
