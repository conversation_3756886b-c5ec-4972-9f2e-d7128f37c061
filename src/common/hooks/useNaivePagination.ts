/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-15 09:18:46
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-19 11:29:08
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/common/hooks/useNaivePagination.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { reactive, defineComponent, h } from 'vue';
import { NPagination } from 'naive-ui';
import type { PaginationProps } from 'naive-ui';

export function useNaivePagination(queryHandle: (page: number, pageSize: number | undefined) => void, option?: any) {
  const pagination: PaginationProps = reactive({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    showSizePicker: true,
    // pageSizes: [10, 20, 30, 40],
    pageSizes: [10, 20, 50, 100],
    showQuickJumper: true,
    onUpdatePage: (page: number) => {
      pagination.page = page;
      queryHandle(page, pagination.pageSize);
    },
    onUpdatePageSize: (pageSize: number) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
      queryHandle(pagination.page, pageSize);
    },
    prefix({ itemCount }: any) {
      return `共 ${itemCount} 条`;
    },
    ...option,
  });
  function updateTotal(total: number) {
    pagination.itemCount = total;
  }
  function resetUpdate() {
    pagination.pageSize = option?.pageSize || 10;
    pagination.page = 1;
    queryHandle(pagination.page, pagination.pageSize);
  }
  return {
    pagination,
    updateTotal,
    resetUpdate,
  };
}

export function useNPaginationComp(pagination: any) {
  return defineComponent(() => {
    return () => {
      return h(NPagination, {
        ...pagination,
      });
    };
  });
}
