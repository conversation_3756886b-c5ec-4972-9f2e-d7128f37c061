/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-15 09:18:46
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-19 11:29:08
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/common/hooks/useNaivePagination.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { reactive, defineComponent, h } from 'vue';
import { NPagination } from 'naive-ui';
import type { PaginationProps } from 'naive-ui';

export function useNaivePagination1(queryHandle: (page: number, pageSize: number | undefined) => void, option?: any) {
  const pagination1: PaginationProps = reactive({
    page: 1,
    pageSize: 5,
    itemCount: 0,
    showSizePicker: true,
    // pageSizes: [10, 20, 30, 40],
    pageSizes: [5, 10, 20, 50],
    showQuickJumper: true,
    onUpdatePage: (page: number) => {
      pagination1.page = page;
      queryHandle(page, pagination1.pageSize);
    },
    onUpdatePageSize: (pageSize: number) => {
      pagination1.pageSize = pageSize;
      pagination1.page = 1;
      queryHandle(pagination1.page, pageSize);
    },
    prefix({ itemCount }: any) {
      return `共 ${itemCount} 条`;
    },
    ...option,
  });
  function updateTotal1(total: number) {
    pagination1.itemCount = total;
  }
  function resetUpdate() {
    pagination1.pageSize = option?.pageSize || 5;
    pagination1.page = 1;
    queryHandle(pagination1.page, pagination1.pageSize);
  }
  return {
    pagination1,
    updateTotal1,
    resetUpdate,
  };
}

export function useNPaginationComp(pagination1: any) {
  return defineComponent(() => {
    return () => {
      return h(NPagination, {
        ...pagination1,
      });
    };
  });
}
