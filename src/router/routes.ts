/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-15 17:54:17
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-18 15:48:55
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/router/routes.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import MainLayout from '@/layouts/MainLayout.vue';
import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/center/index',
  },
  {
    path: '/digitalSpace',
    name: 'digitalSpace',
    component: () => import('@/views/digitalSpace/index.vue'),
  },
  {
    path: '/center/index',
    name: 'center',
    component: () => import('@/views/center/center.vue'),
  },
  {
    path: '/',
    name: 'topRoute',
    component: MainLayout,
    children: [
      {
        path: '/home',
        name: 'home',
        component: () => import('@/views/home/<USER>'),
      },
      {
        path: '/personManage',
        name: 'personManage',
        component: () => import('@/views/personManage/index.vue'),
      },
      {
        path: '/certificateManagement',
        name: 'certificateManagement',
        component: () => import('@/views/certificateManagement/index.vue'),
      },
      {
        path: '/expertdatabase',
        name: 'expertDatabase',
        component: () => import('@/views/expertDatabase/index.vue'),
      },
      {
        path: '/committeeSecuitymange',
        name: 'committeeSecuityMange',
        component: () => import('@/views/committeeSecurityMange/index.vue'),
      },
      {
        path: '/securityResponsibilities',
        name: 'securityResponsibilities',
        component: () => import('@/views/securityResponsibilities/index.vue'),
      },
      {
        path: '/securitymeetings',
        name: 'securityMeetings',
        component: () => import('@/views/securityMeetings/index.vue'),
      },
      {
        path: '/rewarpunishmentrecord',
        name: 'rewarpunishmentrecord',
        component: () => import('@/views/rewarpunishmentrecord/index.vue'),
      },
      {
        path: '/paramConfig',
        name: 'paramConfig',
        component: () => import('@/views/paramConfig/awConfig/index.vue'),
      },
      {
        path: '/dutyEvaluation',
        name: 'dutyEvaluation',
        component: () => import('@/views/dutyEvaluation/index.vue'),
      },
    ],
  },
];

export default routes;
