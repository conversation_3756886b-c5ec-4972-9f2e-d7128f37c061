/*
 * @Author: xginger <EMAIL>
 * @Date: 2025-06-09 12:07:26
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-06-19 09:50:40
 * @FilePath: \ehs-org-alloc-mgr\src\router\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createRouter, createWebHashHistory, useRoute, useRouter } from 'vue-router';
import routes from './routes';
import { useStore } from '@/store';
import yanchangImg from '@/assets/iconImg/yanchang.ico';
import angangImg from '@/assets/iconImg/angang.ico';
import wychImg from '@/assets/iconImg/wych2.ico';
import zhongdanongImg from '@/assets/iconImg/zhongdanong.ico';

import { Address } from '@/views/center/featchData';

const Router = createRouter({
  scrollBehavior: () => ({
    left: 0,
    top: 0,
  }),
  routes,
  history: createWebHashHistory(),
});
async function ticketCheckFromUmToSupervise() {
  const store = useStore();
  const route = useRoute();
  const router: any = useRouter();
  setFavicon(store.userInfo);
  const userInfo = store.userInfo;
  console.log(userInfo, 'userInfo');

  // 只在有token且未登录时执行登录
  if (route.query.token && !store.isLogin) {
    console.log('开始登录流程 >>>>>');
    const { token, sysCode } = route.query;
    // 确保token是字符串类型
    const tokenStr = Array.isArray(token) ? token[0] : (token as string);

    if (tokenStr) {
      return new Promise<void>((resolve) => {
        store.login({ sysCode: 'org_person_web', code: tokenStr }, (routerName: any) => {
          Address()
            .then((res: any) => {
              window.sessionStorage.setItem('ehs-org-alloc-mgr-address', res.data);
              window.sessionStorage.setItem('ehs-org-alloc-mgr-token', tokenStr);
              console.log('登录成功，用户信息已保存到store');
            })
            .finally(() => {
              resolve(); // 确保登录流程完成后再继续
            });
        });
      });
    }
  }
  return Promise.resolve(); // 如果没有token或已登录，直接返回
}
function setFavicon(userInfo: any) {
  const icon: any = userInfo.iconPicUrl;
  // userInfo.zhLogo === 'yanchang' ? yanchangImg : angangImg;
  // if (userInfo.zhLogo === 'yanchang') {
  //   icon = yanchangImg;
  // } else if (userInfo.zhLogo === 'changhang') {
  //   icon = wychImg;
  // } else if (userInfo.zhLogo === 'zhongdanong') {
  //   icon = zhongdanongImg;
  // } else if (userInfo.zhLogo === 'angang') {
  //   icon = angangImg;
  // } else {
  //   icon = userInfo.zhLogoUrl + `${userInfo.zhLogo}.ico`;
  // }

  // console.log(icon);
  let title = '';
  if (userInfo.zhLogo === 'yanchang') {
    title = '安全组织与人员配备';
  } else if (userInfo.zhLogo === 'changhang') {
    title = '安全组织与人员配备';
  } else {
    title = '安全组织与人员配备';
  }
  // userInfo.zhLogo === 'yanchang' ? '安全组织与人员配备' : '安全组织与人员配备';
  // 创建新的 link 标签
  const newLink: any = document.getElementById('icon');
  const newTitle: any = document.getElementById('title');
  newLink.href = icon;
  document.title = title;
}
Router.beforeEach(async (to, from, next) => {
  await ticketCheckFromUmToSupervise();
  next();
});

export default Router;
