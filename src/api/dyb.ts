/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-15 23:14:25
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-18 20:48:12
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/api/dyb.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * 此文件接口为示例代码需要，仅供参考，后期需删除
 */
export default {
  interface: {
    getTreeData: '/common/org/tree', //组织树结构
    getTreeDataPerson: '/personManage/getPersonManageOrgUser', //组织树人员
    getPersonManageList: '/personManage/getPersonManagePageList', //人员管理列表
    addPersonManage: '/personManage/addPersonManage', // 新增人员
    deletePersonManage: '/personManage/removePersonManage', // 移除人员
    deleteCommitteeById: '/safety/committee/delete', // 删除安委会
    addConfig: '/safety/level/add', // 新增配置
    getConfigList: '/safety/level/list', // 获取配置列表
    getConfigDetail: '/safety/level/getInfo', // 获取配置详情
    editConfig: '/safety/level/edit', // 编辑配置
    delConfig: '/safety/level/delete', // 删除配置
    getSignInStaffByTaskId: '/api/task/user/getSignInStaffByTaskId',
  },
  file: {
    uploadFile: '/file/uploadfile', // 上传文件
  },
  server: {
    getMeetingSignList: '/meeting/getMeetingSignList', // 获取签到表
  },
};
