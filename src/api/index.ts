/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-15 22:03:23
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-17 16:35:38
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/api/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { stringify } from 'querystringify';
import { merge } from 'lodash-es';
// api file
import zyx from './zyx';
import jzy from './jzy';
import dyb from './dyb';
import sxd from './sxd';
import csh from './csh';
import zyl from './zyl';
import certificateManagement from './certificateManagement';
import hy from './hy';

export const api = {
  type: {
    nil: '',
    file: 'file-server', // 文件上传服务
    demo: 'resource-server', // demo服务-供参考用，新的服务请在下面接着写
    server: 'bw-clnt-org-person-service',
    platform: 'ehs-clnt-platform-service',
  },
  name: merge(zyx, jzy, dyb, certificateManagement, sxd, csh, zyl, hy),

  /**
   * 组装请求地址
   * @param serviceType
   * @param apiName
   * @param query
   */
  getUrl(serviceType: string, apiName: string, query?: any): string {
    const url = window.$SYS_CFG.apiBaseURL;

    const paramsStr = query ? `?${stringify(query)}` : '';
    const _apiName = apiName.indexOf('/') === 0 ? apiName : '/' + apiName;
    // alert(_apiName);
    const _serviceType = serviceType ? '/' + serviceType : '';

    // alert(`${url}${_serviceType}${_apiName}${paramsStr}`);

    return `${url}${_serviceType}${_apiName}${paramsStr}`;
  },
};
