/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-19 16:12:10
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-21 11:01:00
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/api/certificateManagement.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 证书管理apiURL

export default {
  certificateManagement: {
    queryCertificateList: '/certificate/queryCertificateList', // 多条件查询证书列表
    addCertificate: '/certificate/addCertificate', // 新增证书
    deleteCertificate: '/certificate/deleteCertificate', // 删除证书
    updateCertificate: '/certificate/updateCertificate', // 修改证书
    getCertificateTypeList: '/certificate/type/getCertificateTypeList', // 获取证书类型列表
    deleteCertificateType: '/certificate/type/delCertificateTypeById', // 删除证书类型
    addCertificateType: '/certificate/type/addCertificateType', // 新增证书类型
    editCertificateType: '/certificate/type/editCertificateType', // 修改证书类型
    certificateReviewList: '/certificateReviewRecord/queryCertificateReviewRecordList', //证书复审记录列表
    deleteCertificateReviewRecord: '/certificateReviewRecord/deleteCertificateReviewRecord', //删除证书复审记录
    addCertificateReviewRecord: '/certificateReviewRecord/addCertificateReviewRecord', //新增证书复审记录
    getTreeData: '/common/org/tree', //组织树结构
    // getTreeDataPerson: '/personManage/getPersonManageOrgUser', //组织树人员
    getTreeDataPerson: '/common/org/users',

    // 奖惩记录配置
    getRewardPunishmentTypeList: '/rewardPunishmentType/list', //列表
    getRewardPunishmentTypeDetail: '/rewardPunishmentType/getInfo', //详情
    rewardPunishmentTypeEdit: '/rewardPunishmentType/edit', //编辑
    rewardPunishmentTypeDelete: '/rewardPunishmentType/delete', //删除
    rewardPunishmentTypeAdd: '/rewardPunishmentType/add', //新增

    // 奖惩记录管理
    recordList: '/record/list', //列表
    getRecordInfo: '/record/getInfo', //详情
    recordEdit: '/record/edit', //编辑
    recordDelete: '/record/delete', //删除
    recordAdd: '/record/add', //新增
  },
};
