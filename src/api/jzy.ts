/**
 * 此文件接口为示例代码需要，仅供参考，后期需删除
 */
export default {
  interface: {
    getExpertList: '/expert/queryExpertList', //获取专家库列表
    getExpertPersonManageList: '/expert/getPersonManageOrgUser', //获取专家库组织树人员
    delExpertList: '/expert/delExpert', //删除专家库
    addExpertList: '/expert/addExpert', //新增专家库
    getSafeTyList: '/safety/committee/getSafetyCommitteeList', //获取安委会管理列表
    addSafeTyList: '/safety/committee/addSafetyCommittee', //新增安委会]
    detailSafeTyList: '/safety/committee/getSafetyCommitteeById', //安委会详情
    getMemberList: '/safety/committee/getMembersByCommitteeId', //查询安委会人员列表
    editSafeTyList: '/safety/committee/editSafetyCommittee', //编辑安委会
    getResponsibilityList: '/responsibility/list', //获取会议职责列表
    delResponsibilityList: '/responsibility/delete', //安全职责删除
    addResponsibilityList: '/responsibility/add', // 新增会议职责
    editResponsibilityList: '/responsibility/update', //修改会议职责
    getResponsibilityDetail: '/responsibility/detail', //获取会议职责详情
    getMettingList: '/meeting/list', //获取安全会议列表
    delMettingList: '/meeting/delete', //删除安全会议列表
    getCommitteeList: '/safety/committee/getOrgUserPage', //获取机构用户列表
    addMeetingList: '/meeting/add', //新增安全会议
    editMeetingList: '/meeting/update', //修改安全会议
    getMeetingDetail: '/meeting/detail', //安全会议详情
    delSafetyPerson: '/safety/committee/removerPerson', //根据id删除安委会成员
    getPersonnelSafetyHistory:
      '/personnelSafetyHistory/queryPersonnelSafetyHistoryBaseInfo', //获取人员持证情况基础信息
    getPersonHistoryList: '/personnelSafetyHistory/queryPersonnelSafetyHistory', //获取人员持证情况
    getPersonRecord: '/personnelSafetyHistory/training/record/baseInfo', //获取人员管理安全履历培训记录
    getPersonRecordList:
      '/personnelSafetyHistory/training/record/queryTaskPageByUserId', //获取培训记录表格数据
    getRoutineNum: '/routineInspection/getPerilByUserNum', //获取日常检查统计用户隐患数量
    getRoutineList: '/routineInspection/list', //获取日常检查报个数据
    getRegionalManagementList:
      '/personnelSafetyHistory/queryRegionalManagementList', //管控职责-管控区域
    getRiskIdentificationListByResponsibler:
      '/personnelSafetyHistory/queryRiskIdentificationListByResponsibler', //管控职责-风险管控清单
    getStatisticsByResponsible:
      '/personnelSafetyHistory/statisticsByResponsible', //管控职责-风险管控统计
    getForeignKeyRegionalList:
      '/personnelSafetyHistory/queryForeignKeyRegionalList', //获取管控区域负责人所管控区域的楼层信息
    getOperationBasePageList:
      '/personnelSafetyHistory/queryOperationBasePageList', //获取安全作业记录
    getOperationComplianceStatisticsList:
      '/personnelSafetyHistory/getOperationComplianceStatistics',
    getAccidentStatisticsList: '/personnelSafetyHistory/getAccidentStatistics', //获取事故记录统计项
    getAccidentRecordList: '/personnelSafetyHistory/getAccidentRecord', //获取事故记录表
    gelevelList: '/personnelSafetyHistory/levelList', //获取隐患级别
    getlevelStatisticsList: '/personnelSafetyHistory/levelStatistics', //获取隐患等级
    getStatisticsList: '/personnelSafetyHistory/statistics', //获取隐患状态
    getPageEventList: '/personnelSafetyHistory/pageEvent', //获取隐患列表
    getis0verdueDayList: '/personnelSafetyHistory/isOverdueDay', //获取隐患是否超期
    address: '/common/file/getFilePath', //图片显示
    queryResultByDimension: '/evaluateresult/queryResultByDimension',
    queryEvaluateResultDetail: '/evaluateresult/queryEvaluateResultDetail',
    analyseSumUp: '/evaluateresult/analyseSumUp',
    tyqwTalking: '/bmChat/talking',
    serviceLogin: '/login/serviceLogin',
    modelCalculation: '/personnelSafetyHistory/modelCalculation',
    meetingDel: '/meeting/delete', // 安全会议删除
    mettingEnd: '/meeting/endMeeting', // 结束会议
    batchDelete: '/meeting/batchDelete',
  },

  file: {
    uploadFile2: '/common/file/upload', // 上传文件
  },
  login: {
    login: '/user/login', // 登陆
  },
};
