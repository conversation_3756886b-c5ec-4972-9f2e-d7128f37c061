@charset "UTF-8";
:root {
  color-scheme: light;
  --header-height: 64px;
  --com-primary-color: #527cff;
  --com-border: unset;
  --com-border-radius: 4px;
  --com-container-bg: #eef7ff;
  --com-container-shadow: unset;
  --com-box-bg: #eef7ff;
  --com-box-shadow: unset;
}

/* style */
body {
  /* min-width: 1920px; */
  background: #c8d5ff;
  color: #000;
}

.n-layout {
  background-color: #252843 !important;
}

.n-menu .n-menu-item {
  height: 48px !important;
}

.com-header {
  background-color: #252843 !important;
  color: #fff;
  width: 100%;
  line-height: 64px;
  height: var(--header-height);
  padding: 0 14px;
}

/* 容器背景 */
.com-container-bg, .com-container, .com-table-container, .com-table-filter, .com-table-filter-nb, .com-table-filter-blr {
  background: var(--com-container-bg);
}

.com-container-border, .com-container, .com-table-container, .com-table-filter, .com-table-filter-nb, .com-table-filter-blr {
  border: var(--com-border);
  border-radius: var(--com-border-radius);
  box-shadow: var(--com-container-shadow);
}

/* 通用容器 */
.n-tree-node-switcher {
  margin-left: -10px !important;
  margin-right: 5px !important;
  padding-top: 2px !important;
}

/* 基础容器尺寸、颜色 private */
._container-base, .com-table-wrap {
  position: relative;
  height: calc(100vh - var(--header-height) - 24px - 24px);
  margin: 24px 24px 0;
  color: #000;
  overflow: hidden;
}

.com-table {
  --n-th-color: #bbccf3 !important;
  --n-th-text-color: #222222 !important;
  --n-td-text-color: #222222 !important;
  --n-item-text-color: #606266 !important;
  --n-td-color-striped: rgba(223, 238, 252, 0.99) !important;
  --n-td-color: #eef7ff !important;
  --n-border-color: #c5cbd6 !important;
  --n-td-color-hover: #eef7f8 !important;
}

.com-table .n-pagination {
  color: #606266;
}

/* 表格容器外层 */
.com-table-wrap {
  display: grid;
  grid-template-rows: auto 1fr;
}

/* 表格容器 */
.com-table-container {
  padding: 24px 24px 16px;
}

.com-table-filter {
  padding: 24px;
}

.com-table-filter-nb {
  border-radius: unset;
  padding: 24px;
}

.com-table-filter-blr {
  border-top-left-radius: unset;
  border-top-right-radius: unset;
  padding: 24px;
}

/* Box盒子（比container小的模块） */
.com-box-bg, .com-box {
  background: var(--com-box-bg);
}

.com-box-border, .com-box {
  border: var(--com-border);
  border-radius: var(--com-border-radius);
  box-shadow: var(--com-box-shadow);
}

/* com-g-x-x -> */
.com-g-row-aa {
  display: grid;
  grid-template-rows: auto auto;
}

.com-g-row-a1 {
  display: grid;
  grid-template-rows: auto 1fr;
}

.com-g-row-aa1 {
  display: grid;
  grid-template-rows: auto auto 1fr;
}

.com-g-col-a1 {
  display: grid;
  grid-template-columns: auto 1fr;
}

.com-g-col-1a {
  display: grid;
  grid-template-columns: 1fr auto;
}

/* com-g-x-x  <- */
.com-action-button {
  --n-text-color: var(--com-primary-color) !important;
}

.com-del-button {
  padding-left: 20px !important;
  padding-right: 20px !important;
  height: 32px !important;
  background: rgba(250, 81, 81, 0.1) !important;
  border: 1px solid #fa5151 !important;
  border-radius: 5px !important;
  color: #fa5151 !important;
}

.com-del2-button {
  padding-left: 20px !important;
  padding-right: 20px !important;
  height: 32px !important;
  background: rgba(82, 124, 255, 0.1) !important;
  border: 1px solid #527cff !important;
  border-radius: 5px !important;
  color: #527cff !important;
}

.com-del-button-small {
  width: 40px !important;
  height: 18px !important;
  background: rgba(250, 81, 81, 0.1) !important;
  border: 1px solid #fa5151 !important;
  border-radius: 5px !important;
  color: #fa5151 !important;
  font-size: 12px !important;
}

.com-edit-button {
  padding-left: 10px !important;
  padding-right: 10px !important;
  height: 32px !important;
  background: rgba(82, 124, 255, 0.1) !important;
  border: 1px solid #527cff !important;
  border-radius: 5px !important;
  color: #527cff !important;
}

.com-empty {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  justify-items: center;
}

.n-menu-item .n-menu-item-content {
  padding: 0 18px !important;
}

.jzy-person {
  width: 32% !important;
  height: 32px !important;
  background: rgba(82, 124, 255, 0.1) !important;
  border: 1px solid #527cff !important;
  border-radius: 5px !important;
  color: #527cff !important;
}

.jzy-input {
  width: 60px !important;
}

.jzy-select2 {
  width: 80px !important;
}

.jzy-input2 {
  width: 60px !important;
  margin-top: 20px !important;
}

.n-checkbox.n-checkbox--disabled .n-checkbox-box .n-checkbox-box__border {
  background: #c2c0c0 !important;
}

.flex-row .n-form-item-feedback__line {
  padding-left: 7% !important;
}

.n-upload-file-list .n-upload-file .n-upload-file-info {
  position: relative !important;
  padding-top: 6px !important;
  padding-bottom: 6px !important;
  display: flex !important;
  flex-wrap: nowrap !important;
  width: 400px !important;
}

.n-upload-file-list .n-upload-file .n-upload-file-info .n-upload-file-info__name span {
  display: inline-block !important;
  width: 300px !important;
  white-space: nowrap !important;
  /* 防止换行 */
  overflow: hidden !important;
  /* 隐藏超出部分 */
  text-overflow: ellipsis !important;
  /* 使用省略号表示被隐藏的文本 */
}

.n-upload-file-list .n-upload-file .n-upload-file-info .n-upload-file-info__name a {
  width: 300px !important;
  white-space: nowrap !important;
  /* 防止换行 */
  overflow: hidden !important;
  /* 隐藏超出部分 */
  text-overflow: ellipsis !important;
  /* 使用省略号表示被隐藏的文本 */
}

.n-menu-item-content--selected:after {
  content: "" !important;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  /* 上边 */
  border-bottom: 8px solid transparent;
  /* 下边 */
  border-right: 8px solid #c8d5ff;
  /* 右边显示为红色, 形成箭头 */
  position: absolute !important;
  right: 0;
}

.n-menu-item-content--selected {
  position: relative !important;
}

.n-button--primary-type {
  background-color: #527cff !important;
}

.n-button--tertiary-type {
  background-color: #f4f4f4 !important;
}

.com-table-filter .n-button {
  width: 88px !important;
  border-radius: 4px !important;
}

.com-table-container .n-button {
  border-radius: 4px !important;
}

.n-layout-sider-scroll-container {
  background: #252843 !important;
  color: #fff !important;
}

._content_179ui_87 {
  min-height: var(--min-height) !important;
}

.n-layout {
  min-height: var(--min-height) !important;
}

.n-menu-item {
  font-size: 16px !important;
}

.n-menu-item-content-header {
  color: #fff !important;
}

.n-menu-item-content__icon .n-icon {
  color: #000;
}

.jzy-table {
  height: 600px !important;
}

.jzy-title {
  height: 42px;
  line-height: 42px;
  background-color: #252842;
}

.jzy-title img {
  margin-left: 12px;
}

.jzy-button {
  margin-left: 14px !important;
  margin-top: 10px !important;
  margin-bottom: 5px !important;
}

.n-menu-item-content {
  padding-left: 14px !important;
}

.jzy-select {
  width: 100px !important;
}

/* overwrite naiveUI style -> */
/* 悬浮提示框宽度 */
.n-popover {
  @apply max-w-[600px];
}

/* 侧边弹窗背景虚化 */
.n-drawer {
  backdrop-filter: blur(3px);
}

.com-menu {
  --n-arrow-color-child-active: #fff !important;
  --n-item-text-color-hover: #fff !important;
  --n-arrow-color-hover: #fff !important;
  --n-item-icon-color-child-active-hover: #fff !important;
  --n-arrow-color-child-active-hover: #fff !important;
}

/* <- */
