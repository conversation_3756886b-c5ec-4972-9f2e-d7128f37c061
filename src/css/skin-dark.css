@charset "UTF-8";
:root {
  color-scheme: dark;
  --header-height: 64px;
  --com-primary-color: #252843;
  --com-border: 1px solid #1581ef;
  --com-border-radius: 4px;
  --com-container-bg: linear-gradient(45deg, rgba(2, 11, 20, 0.1) 0%, rgba(2, 11, 20, 0.98) 30%, rgba(2, 11, 20, 0.9) 100%);
  --com-container-shadow: inset 0 0 5px rgba(22, 144, 248, 0.75), 0 0 10px rgba(21, 132, 195, 0.38);
  --com-box-bg: rgba(5, 25, 49, 0.9);
  --com-box-shadow: inset 0 0 15px rgba(21, 132, 195, 0.75);
}

/* style */
body {
  background: #011425;
  color: #fff;
}

.com-header {
  background: var(--com-primary-color);
  color: #fff;
  width: 100%;
  height: var(--header-height);
  padding: 0 12px;
}

/* 容器背景 */
.com-container-bg, .com-container, .com-table-container, .com-table-filter, .com-table-filter-nb, .com-table-filter-blr {
  background: var(--com-container-bg);
}

.com-container-border, .com-container, .com-table-container, .com-table-filter, .com-table-filter-nb, .com-table-filter-blr {
  border: var(--com-border);
  border-radius: var(--com-border-radius);
  box-shadow: var(--com-container-shadow);
}

/* 通用容器 */
/* 基础容器尺寸、颜色 private */
._container-base, .com-table-wrap {
  position: relative;
  height: calc(100vh - var(--header-height));
  color: #fff;
  overflow: hidden;
}

/* 表格容器外层 */
.com-table-wrap {
  display: grid;
  grid-template-rows: auto 1fr;
}

/* 表格容器 */
.com-table-container {
  padding: 24px 24px 16px;
}

.com-table-filter {
  padding: 24px;
}

.com-table-filter-nb {
  border-radius: unset;
  padding: 24px;
}

.com-table-filter-blr {
  border-top-left-radius: unset;
  border-top-right-radius: unset;
  padding: 24px;
}

/* Box盒子（比container小的模块） */
.com-box-bg, .com-box {
  background: var(--com-box-bg);
}

.com-box-border, .com-box {
  border: var(--com-border);
  border-radius: var(--com-border-radius);
  box-shadow: var(--com-box-shadow);
}

.com-action-button {
  --n-font-size: 16px !important;
  --n-text-color: #00fefe !important;
}

.com-empty {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  justify-items: center;
}

/* 通用数据行样式 */
.com-row-item-wrap {
  border: 1px solid;
  border-image: linear-gradient(90deg, transparent 0%, rgba(0, 175, 237, 0.28) 50%, transparent 100%) 1 1 1 1;
  background: linear-gradient(90deg, rgba(32, 213, 246, 0) 0%, rgba(12, 150, 207, 0.1) 60%, rgba(32, 213, 246, 0) 100%);
  border-right: 0;
  border-left: 0;
  padding: 3px 0;
}

.com-row-item-wrap.com-row-item-striped:nth-child(odd) {
  background: none;
  border: none;
}

/* overwrite naiveUI style -> */
.n-data-table .n-data-table-thead {
  background: linear-gradient(0deg, #0d385b 0%, #154f79 100%) !important;
  font-size: 16px;
}

.n-data-table .n-data-table-thead .n-data-table-tr {
  background: unset !important;
}

/* overwrite naive style */
.n-date-panel, .n-time-picker-panel, .n-cascader-menu, .n-base-select-menu {
  border: 1px solid #16c5f8;
  box-shadow: 0 0 15px rgba(21, 132, 195, 0.75) !important;
}

/* 悬浮提示框宽度 */
.n-popover {
  @apply max-w-[600px];
}

/* 侧边弹窗背景虚化 */
.n-drawer {
  backdrop-filter: blur(3px);
}

/* 菜单 */
.n-menu-item:first-child,
.n-menu-item:last-child {
  margin-bottom: 5px;
}

.n-dropdown-option {
  padding: 2px 0;
}

/* <- */
