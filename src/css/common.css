@charset "UTF-8";
/**
 * 公共类
 */
body {
  font-size: 14px;
}

.com-autofill-dark-none input:-webkit-autofill,
.com-autofill-dark-none input:-webkit-autofill:hover,
.com-autofill-dark-none input:-webkit-autofill:focus,
.com-autofill-dark-none textarea:-webkit-autofill,
.com-autofill-dark-none textarea:-webkit-autofill:hover,
.com-autofill-dark-none textarea:-webkit-autofill:focus,
.com-autofill-dark-none select:-webkit-autofill,
.com-autofill-dark-none select:-webkit-autofill:hover,
.com-autofill-dark-none select:-webkit-autofill:focus {
  -webkit-background-clip: text;
  -webkit-text-fill-color: rgba(255, 255, 255, 0.9);
  color-scheme: dark;
}

.com-autofill-light-none input:-webkit-autofill,
.com-autofill-light-none input:-webkit-autofill:hover,
.com-autofill-light-none input:-webkit-autofill:focus,
.com-autofill-light-none textarea:-webkit-autofill,
.com-autofill-light-none textarea:-webkit-autofill:hover,
.com-autofill-light-none textarea:-webkit-autofill:focus,
.com-autofill-light-none select:-webkit-autofill,
.com-autofill-light-none select:-webkit-autofill:hover,
.com-autofill-light-none select:-webkit-autofill:focus {
  -webkit-background-clip: text;
  -webkit-text-fill-color: rgba(0, 0, 0, 0.9);
  color-scheme: light;
}

/* com-g-x-x -> */
.com-g-row-aa {
  display: grid;
  grid-template-rows: auto auto;
}

.com-g-row-a1 {
  display: grid;
  grid-template-rows: auto 1fr;
}

.com-g-row-aa1 {
  display: grid;
  grid-template-rows: auto auto 1fr;
}

.com-g-row-full {
  grid-template-rows: 100%;
}

.com-g-col-a1 {
  display: grid;
  grid-template-columns: auto 1fr;
}

.com-g-col-1a {
  display: grid;
  grid-template-columns: 1fr auto;
}

.com-g-col-full {
  grid-template-columns: 100%;
}

/* com-g-x-x  <- */
.com-empty {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  justify-items: center;
}
