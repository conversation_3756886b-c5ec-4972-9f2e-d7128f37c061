/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-03 18:23:52
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-07-08 17:25:36
 * @FilePath: /angang-edu-web/src/config/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const isPro = import.meta.env.PROD;
let base_host = window.location.origin; // 测试环境
// let base_host="http://*************:39980"
const serviceName =
  window.location.host !== 'agjp.tanzervas.com' ? '' : '/aqsc/v1'; // 公司环境
const socketIpUrl =
  window.location.host !== 'agjp.tanzervas.com'
    ? 'ws://*************:8001/zhuzhou-dev/'
    : 'wss://agjp.tanzervas.com/train/';

if (isPro) {
  base_host = '';
}
const config = {
  socketServer: socketIpUrl,

  isOpenFrosua: false, // 是否开启消息埋点  仅招商云环境需要放开

  isFrosuaPro: isPro ? 'prod' : 'dev',

  isProduction: window.location.host !== 'https://agjp.tanzervas.com/',
  // TODO 本地环境待修改 图片用域名`${window.location.origin}/`
  downloadFileUrl:
    window.location.host === 'agjp.tanzervas.com'
      ? 'https://agjp.tanzervas.com/aqsc/v1/'
      : `${window.location.origin}/`, // 下载

  USER_IFNO_NAMESPACE: '@@supervise-web_userInfo',

  base_host,

  base_url:
    window.location.host !== 'agjp.tanzervas.com'
      ? serviceName + '/api/v3/train-server'
      : serviceName + '/api/v1/train-server',

  image_url: '/',

  icon_url:
    window.location.host === 'agjp.tanzervas.com'
      ? 'https://agjp.tanzervas.com/aqsc/v1/img1/systemLogo/'
      : 'https://test-bw.gsafetycloud.com/aqsc/img1/systemLogo/',

  update_file:
    window.location.host !== 'agjp.tanzervas.com'
      ? serviceName + '/api/v3/edu-file-server'
      : serviceName + '/api/v1/edu-file-server',

  local_url:
    window.location.host === 'agjp.tanzervas.com'
      ? serviceName
      : window.location.origin,

  chat_service:
    window.location.host !== 'agjp.tanzervas.com'
      ? serviceName + '/api/v3/bw-chat-api-internal-service'
      : serviceName + '/api/v1/bw-chat-api-internal-service',

  platform_service:
    window.location.host !== 'agjp.tanzervas.com'
      ? serviceName + '/api/v3/bw-clnt-org-person-service'
      : serviceName + '/api/v1/bw-clnt-org-person-service',

  root_dir: 'train',
};
export default config;
