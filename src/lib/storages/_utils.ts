/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-20 13:50:49
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-20 22:04:11
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/lib/storages/_utils.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import dayjs from 'dayjs';

export const map = (object: object, callback: (x: string) => void): void => {
  try {
    Object.keys(object).forEach(callback);
  } catch (error) {
    console.log(error);
  }
};

export const isNil = <T>(item: T) => item === null || item === undefined;

export const isPrimitive = (a: any) => {
  const type = typeof a;
  return type === 'string' || type === 'number' || type === 'boolean' || isNil(a);
};

export function dateStringToTimestamp(dateString: string): number {
  // 使用 dayjs 解析日期字符串
  const date = dayjs(dateString, 'YYYY-MM-DD');
  // 转换为时间戳（毫秒）
  const timestamp = date.valueOf();
  // 返回时间戳
  return timestamp;
}

export function timestampToDate(timestamp: number): string {
  // 使用 dayjs 将时间戳转换为日期对象
  const date = dayjs(timestamp);
  // 格式化日期对象为数值
  const dateNumber = date.format('YYYY-MM-DD');
  // 返回日期字符串
  return dateNumber;
}
