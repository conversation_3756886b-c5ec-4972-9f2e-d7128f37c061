/**
 * 校验文本框 （始终返回布尔值）
 * @param value
 * @return boolean
 */

export function onlyAllowNumber(value: string) {
  return !value || /^\d+$/.test(value);
}

/**
 * 数字+小数
 * @param value
 * @param digit 限制小数位数
 * @return boolean
 */
export function decimal(value: string, digit = 4) {
  // return !value || /^\d*(?:\.\d{0,4})?$/.test(value); // 限制最多4位小数
  const re = new RegExp(`^\\d*(?:\\.\\d{0,${digit}\})?$`);
  return !value || re.test(value);
}

export function trim(value: string) {
  return !value.startsWith(' ') && !value.endsWith(' ');
}
