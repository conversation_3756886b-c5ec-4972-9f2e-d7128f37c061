import ycIcon from '/icon/angang.ico';
import agIcon from '/icon/yanchang.ico';

export function setFavicon(ui: any) {
  const icon = ui.zhLogo === 'yanchang' ? ycIcon : agIcon;

  // 创建新的 link 标签
  const newLink = document.createElement('link');
  newLink.rel = 'icon';
  newLink.type = 'image/x-icon';
  newLink.href = icon;
  // 获取并移除原有的 favicon link
  const oldLinks = document.querySelector('link[rel="icon"]');
  if (oldLinks) oldLinks.parentNode?.removeChild(oldLinks);
  // 将新创建的 link 插入到 head 中
  document.head.appendChild(newLink);
}
