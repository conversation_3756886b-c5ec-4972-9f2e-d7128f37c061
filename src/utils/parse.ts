/**
 * 数字或字符串转整数
 * @param val
 * @param fill
 */
export function parseInteger(val: string | number, fill = null) {
  const n = parseInt(val + '');

  return Number.isNaN(n) ? fill : n;
}

/**
 * 百分号转小数
 * @param input
 */
export function parsePercentage(input: string) {
  // 如果输入包含百分号，将其转换为小数
  if (input.includes('%')) {
    const value = parseFloat(input.replace('%', ''));
    return isNaN(value) ? null : value / 100;
  }

  // 否则尝试直接解析为数字
  const value = parseFloat(input);
  return isNaN(value) ? null : value;
}

/**
 * 小数转百分比
 * @param value
 */
export function formatPercentage(value: number | null) {
  if (value === null) return '';

  // 如果是小数（小于1），显示为百分比
  if (value < 1 && value > 0) {
    return `${(value * 100).toFixed(0)}%`;
  }

  // 否则显示为普通数字
  return value.toString();
}
