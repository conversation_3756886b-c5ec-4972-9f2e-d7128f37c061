export const removeEmptyChildren = (array: any, childrenKey = 'children') => {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };

    // 检查是否存在children，并且是一个数组
    if (newItem[childrenKey] && Array.isArray(newItem[childrenKey])) {
      newItem[childrenKey] = removeEmptyChildren(newItem[childrenKey]); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem[childrenKey].length === 0) {
        delete newItem[childrenKey];
      }
    }

    return newItem; // 返回新对象
  });
};
