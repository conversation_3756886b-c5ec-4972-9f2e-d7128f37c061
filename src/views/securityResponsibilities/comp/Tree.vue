<template>
  <div>
    <!-- 采用menu的形式 可以自由选择-->
    <!-- <n-menu :options="menuOptions" :default-expanded-keys="defaultExpandedKeys" accordion default-value="test1" /> -->
    <!-- 采用表单树的形式 -->
    <div calss="head">安全职责</div>
    <n-tree
      block-line
      :data="dataList"
      key-field="key"
      label-field="label"
      children-field="children"
      :node-props="nodeProps"
    />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, h, ref } from 'vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import type { MenuOption } from 'naive-ui';
import { Business, People, Person } from '@vicons/ionicons5';
import { BagOutline as BagOutlineIcon, FishOutline as FishIcon, PawOutline as PawIcon } from '@vicons/ionicons5';
import type { Component } from 'vue';
import { NIcon, TreeOption } from 'naive-ui';
function renderIcon(icon: Component) {
  return () => h(NIcon, null, { default: () => h(icon) });
}
const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(false);
const defaultExpandedKeys = ['test'];
const dataList = ref<any[]>([
  {
    label: '测试有限公司',
    key: 1,
    prefix: () =>
      h(
        NIcon,
        { size: '20', color: '#409eff' },
        {
          default: () => h(Business),
        }
      ),
    children: [
      {
        label: '管理部',
        key: 11,
        prefix: () =>
          h(
            NIcon,
            { size: '20', color: '#409eff' },
            {
              default: () => h(People),
            }
          ),
      },
      {
        label: '生产部',
        key: 12,
        prefix: () =>
          h(
            NIcon,
            { size: '20', color: '#409eff' },
            {
              default: () => h(People),
            }
          ),
      },
      {
        label: 'ESH部',
        key: 13,
        prefix: () =>
          h(
            NIcon,
            { size: '20', color: '#409eff' },
            {
              default: () => h(People),
            }
          ),
        children: [
          {
            label: '安全',
            key: 131,
            prefix: () =>
              h(
                NIcon,
                { size: '20', color: '#409eff' },
                {
                  default: () => h(Person),
                }
              ),
          },
          {
            label: '设备',
            key: 132,
            prefix: () =>
              h(
                NIcon,
                { size: '20', color: '#409eff' },
                {
                  default: () => h(Person),
                }
              ),
          },
        ],
      },
    ],
  },
]);
const menuOptions: MenuOption[] = [
  {
    label: '熊掌',
    key: 'test',
    icon: renderIcon(PawIcon),
    children: [
      {
        label: '保护野生动物',
        key: 'test1',
      },
      {
        label: '从我做起',
        key: 'test2',
      },
    ],
  },
  {
    label: '两个都要',
    key: 'both',
    icon: renderIcon(BagOutlineIcon),
    children: [
      {
        label: '鱼和熊掌不可兼得',
        key: 'can-not',
      },
    ],
  },
];

const nodeProps = ({ option }: { option: TreeOption }) => {
  return {
    onClick() {
      // 这里写点击组组织后的逻辑
      console.log(`[Click] ${option.label}`);
    },
  };
};

onMounted(() => {});

defineOptions({ name: 'certificateManagementFilter' });
</script>

<style module lang="scss"></style>
