<template>
  <ComDrawerA
    :autoFocus="false"
    :footerPaddingBottom="25"
    :maskClosable="false"
    :show-action="isEdit || isADD"
    @handle-negative="handleClose"
    @handle-positive="handleSubmit"
    class="!w-[550px]"
  >
    <Edit
      ref="editRef"
      :handleClose="handleClose"
      :getData="getData"
      :id="id"
      v-if="isEdit"
      :treeId="treeId"
      :treeCode="treeCode"
      @submitted="handleSubmitted"
    />
    <Add
      ref="addRef"
      :getData="getData"
      :handleClose="handleClose"
      :code="codee"
      v-if="isADD"
      @submitted="handleSubmitted"
      :treeId="treeId"
      :treeCode="treeCode"
    />
  </ComDrawerA>
</template>

<script lang="ts" setup>
import ComDrawerA from '@/components/drawer/ComDrawerA.vue';
import { computed, inject, Ref, ref, useAttrs } from 'vue';
import { ACTION, PROVIDE_KEY } from '../../constant';
import type { IActionData } from '../../type';
import Add from './Add.vue';
import Edit from './Edit.vue';
const props: any = defineProps({
  getData: {
    type: Function,
  },
  treeCode: {
    type: String,
  },
  treeId: {
    type: String,
  },
});
const emits = defineEmits(['action']);
const attrs = useAttrs();
const show = computed(() => !!attrs.show);
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);
const isADD = computed(() => currentAction.value.action === ACTION.ADD);
const editRef = ref();
const addRef = ref();
function handleSubmit() {
  if (currentAction.value.action === ACTION.ADD) {
    addRef.value?.handleSubmit();
  } else {
    editRef.value?.handleSubmit();
  }
}
const codee = ref('');
const getLevelPath = (code: string) => {
  codee.value = code;
};
function handleSubmitted() {
  emits('action', { action: ACTION.SEARCH });
  handleClose();
}
const id = ref('');
const getEditFormData = async (idd: string) => {
  // let res = await detailSafyData(idd);
  id.value = idd;
};
function handleClose() {
  emits('update:show' as any, false); // any-屏蔽组件内透传的emit类型
}
defineExpose({
  getLevelPath,
  getEditFormData,
});
defineOptions({ name: 'CommitteeSecurityMangeAside' });
</script>

<style module lang="scss"></style>
