<template>
  <div :class="$style.wrap">
    <n-spin :show="loading" :delay="500">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="140"
        require-mark-placement="left"
      >
        <n-form-item label="安全职责名称" path="name">
          <n-input
            show-count
            v-model:value="formData.name"
            clearable
            maxlength="30"
            placeholder="请输入安全职责名称"
          />
        </n-form-item>
        <!-- <n-form-item label="安委会级别" path="safetyCommitteeLevelId">
          <n-select v-model:value="formData.safetyCommitteeLevelId" :options="categoryOpt" label-field="levelName"
            value-field="id" placeholder="请选择安委会级别" />
        </n-form-item> -->
        <n-form-item label="年度" path="time">
          <n-select
            v-model:value="formData.time"
            :options="yearOption"
            label-field="year"
            value-field="id"
            placeholder="请选择年度"
          />
        </n-form-item>
        <n-form-item
          label="发起方"
          ref="launchDeptNameRef"
          path="launchDeptName"
        >
          <n-input
            @click="xuan2"
            v-model:value="formData.launchDeptName"
            readonly="true"
            placeholder="请从组织架构选择单位或部门"
          />
          <n-button @click="xuan2()" style="margin-left: 10px">选择</n-button>
        </n-form-item>
        <!-- <n-form-item label="发起方安委会" path="launchSafetyCommitteeId">
          <n-select
            v-model:value="formData.launchSafetyCommitteeId"
            :options="launchCategoryOpt"
            label-field="name"
            value-field="id"
            placeholder="请选择发起方安委会"
            :disabled="!formData.launchDeptId"
          />
        </n-form-item> -->
        <n-form-item
          label="发起方责任人"
          ref="launchUserNameRef"
          path="launchUserName"
        >
          <n-input
            @click="xuan"
            v-model:value="formData.launchUserName"
            readonly="true"
            placeholder=" 请从组织架构选择人员"
          />
          <n-button @click="xuan()" style="margin-left: 10px">选择</n-button>
        </n-form-item>
        <n-form-item label="责任方" path="deptName" ref="deptNameRef">
          <n-input
            v-model:value="formData.deptName"
            @click="xuan3"
            readonly="true"
            placeholder=" 请从组织架构选择单位或部门"
          />
          <n-button @click="xuan3()" style="margin-left: 10px">选择</n-button>
        </n-form-item>
        <!-- <n-form-item label="责任方安委会" path="responsibleSafetyCommitteeId">
          <n-select
            v-model:value="formData.responsibleSafetyCommitteeId"
            :options="deptCategoryOpt"
            label-field="name"
            value-field="id"
            placeholder="请选择责任方安委会"
            :disabled="!formData.deptId"
          />
        </n-form-item> -->
        <n-form-item label="责任方责任人" ref="fuzeRef" path="fuze">
          <n-input
            v-model:value="formData.fuze"
            readonly="true"
            @click="xuan1"
            placeholder=" 请从组织架构选择人员"
          />
          <n-button @click="xuan1()" style="margin-left: 10px">选择</n-button>
        </n-form-item>
        <n-form-item style="margin-left: 16%">
          <file-upload
            accept=".docx, .pdf,.doc"
            :data="fileData"
            @update="handleUpdate"
            @removee="handleRemovee"
            :max="3"
          ></file-upload>
        </n-form-item>
      </n-form>
    </n-spin>
    <!-- <selectUser
      ref="selectUserRef"
      v-model:showModal="isShowAside"
      @success="getData1"
      @close="isShowAside = false"
      :checkbox="radio"
    ></selectUser> -->
    <selectUser
      ref="selectUserRef"
      :parentOrgCode="treeId"
      @getPersonManageData="getTreeDataPerson1"
      v-model:show="isShowAside"
      @close="isShowAside = false"
      :userConfigFeid="true"
      @selectUserData="handleAdd"
    >
    </selectUser>
    <selectUserRadio
      ref="selectUserRadioRef"
      v-model:showModal="isShowAside3"
      @success="getData3"
      @close="isShowAside3 = false"
      :treeId="treeId"
    ></selectUserRadio>
    <selectTree
      :dataList="treeData"
      v-model:showModal="isShowAside2"
      @success="getData2"
      @close="isShowAside2 = false"
    >
    </selectTree>
  </div>
</template>

<script setup lang="ts">
// import selectUser1 from '../select-user.vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import selectUserRadio from '@/components/select-user/Select-user-radio.vue';
import selectUser from '@/components/select-user/Select-user.vue';
import { getSafeTyData } from '@/views/committeeSecurityMange/fetchData';
import { DataTableColumns, FormRules, NButton } from 'naive-ui';
import {
  addResponsibilityData,
  getOrgTrees,
  getPersonManageList,
} from '../../fetchData';
import selectTree from '../select-tree.vue';
// import { ICategory } from '../../type';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { FileUpload } from '@/components/upload';
import { IUploadRes } from '@/components/upload/type';
import { useStore } from '@/store';
import mittBus from '@/utils/mittBus';
import { useMessage } from 'naive-ui';
import { h, onMounted, ref, VNode } from 'vue';
import { ACTION_LABEL } from '../../constant';
import { cols } from './columns';
const treeData = ref([]);
const store = useStore();
const message = useMessage();
const selectUserRef: any = ref(null);
const isShowAside = ref(false);
const isShowAside2 = ref(false);
const isShowAside3 = ref(false);
const [loading, run] = useAutoLoading(false);
const [loading1, search] = useAutoLoading(false);
const fileData = ref<any[]>([]);
const columns = ref<DataTableColumns>([]);
const props: any = defineProps({
  code: {
    type: String,
  },
  getData: {
    type: Function,
  },
  handleClose: {
    type: Function,
  },
  treeCode: {
    type: String,
  },
  treeId: {
    type: String,
  },
});

const launchCategoryOpt = ref([]);
const deptCategoryOpt = ref([]);

// 获取安委会级别列表
function getSecurityCommissionType(
  unitId: string,
  levelCode: string,
  id: string,
  type: 'launch' | 'dept'
) {
  let params = {
    pageNo: -1,
    pageSize: 1000,
    unitId,
    levelCode,
    id,
    type: '2',
  };
  search(getSafeTyData(params)).then((res: any) => {
    if (res.code != 200) return;
    if (type === 'launch') {
      launchCategoryOpt.value = res.data.rows;
    } else {
      deptCategoryOpt.value = res.data.rows;
    }
  });
}
function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };

    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }

    return newItem; // 返回新对象
  });
}
const orgCode = ref(store.userInfo.unitId);
async function getTreeDataPerson1(params: any) {
  let res = await getPersonManageList({ ...params, type: 1 });

  selectUserRef.value.renderTable(res);
}
//获取树结构数据
function QueryOrgTrees() {
  const params = {
    orgCode: store.userInfo.unitId,
    needChildUnit: '1',
    needself: '1',
  };
  search(getOrgTrees(params)).then((res: any) => {
    if (res.code != 200) return;
    const _RES: any = removeEmptyChildren(res.data);
    treeData.value = _RES;
  });
}
onMounted(() => {
  QueryOrgTrees();
});
const strr = ref('');
const launchDeptNameRef: any = ref('');
const deptNameRef: any = ref('');
const getData2 = (str: any) => {
  if (strr.value == '发起方') {
    formData.value.launchDeptName = str.text;
    formData.value.launchDeptId = str.id;
    // 清空发起方安委会级别
    formData.value.launchSafetyCommitteeId = null;
    isShowAside2.value = false;
    launchDeptNameRef.value.restoreValidation();
    // 获取发起方安委会级别列表
    getSecurityCommissionType(str.id, props.treeCode, str.id, 'launch');
  } else {
    formData.value.deptName = str.text;
    formData.value.levelPath = str.levelCode;
    formData.value.deptId = str.id;
    // 清空责任方安委会级别
    formData.value.responsibleSafetyCommitteeId = null;
    isShowAside2.value = false;
    deptNameRef.value.restoreValidation();
    // 获取责任方安委会级别列表
    getSecurityCommissionType(str.id, props.treeCode, str.id, 'dept');
  }
};
// 获取当前年份
const currentYear = new Date().getFullYear();

// 计算上一年和下一年的年份
const previousYear = currentYear - 1;
const nextYear = currentYear + 1;
const yearOption: any = ref([
  { year: `${previousYear}`, id: `${previousYear}` },
  { year: `${currentYear}`, id: `${currentYear}` },
  { year: `${nextYear}`, id: `${nextYear}` },
]);
const formData: any = ref({
  fuze: '',
  userNameList: [],
  attachments: [],
  launchUserId: '',
  deptId: '',
  levelPath: '',
  launchSafetyCommitteeId: null,
  responsibleSafetyCommitteeId: null,
});
const rules: FormRules = {
  name: [
    {
      required: true,
      message: '请输入安全职责名称',
      trigger: ['blur', 'input'],
    },
  ],
  time: { required: true, message: '请选择年度', trigger: ['blur', 'change'] },
  safetyCommitteeLevelId: {
    required: true,
    message: '请选择安委会级别',
    trigger: ['blur', 'change'],
  },
  launchDeptName: [
    {
      required: true,
      message: '请选择负责单位/部门',
      trigger: ['blur', 'change'],
    },
  ],
  launchUserName: {
    required: true,
    message: '请选择发起方责任人',
    trigger: ['blur', 'input'],
  },
  deptName: {
    required: true,
    message: '请选择责任方',
    trigger: ['blur', 'input'],
  },
  fuze: {
    required: true,
    message: '请选择责任方责任人',
    trigger: ['blur', 'input'],
  },
  launchSafetyCommitteeId: {
    required: true,
    message: '请选择发起方安委会',
    trigger: ['blur', 'change'],
  },
  responsibleSafetyCommitteeId: {
    required: true,
    message: '请选择责任方安委会',
    trigger: ['blur', 'change'],
  },
};
// function getData() {
//   const id = actionData.value.id;
//   if (id) {
//     run(getDetail(id)).then((res) => {
//       const jurStr = res.data?.jurisdiction || '';
//       jurChecked.value = jurStr ? jurStr.split(',') : [];
//     });
//   }
// }
const emits = defineEmits(['action', 'submitted']);
const fuzeRef: any = ref(null);
const handleAdd = (arr: any) => {
  formData.value.userNameList = arr.map((item: any) => {
    item.userId = item.userId ? item.userId : item.id;
    return item;
  });
  formData.value.fuze = formData.value.userNameList
    .map((user: any) => user.userName)
    .join('、');
  fuzeRef.value.restoreValidation();
};
const launchUserNameRef: any = ref(null);
const getData3 = (arr: any) => {
  // console.log(arr, '>>>');
  formData.value.launchUserName = arr[0].userName;
  formData.value.launchUserId = arr[0].id;
  formData.value.launchPersonUnitName = arr[0].unitName;
  formData.value.launchPersonUnitId = arr[0].unitId;
  formData.value.launchPersonDeptId = arr[0].deptId;
  userList.value = [
    {
      id: arr[0].id,
      unitId: arr[0].unitId,
      deptId: arr[0].deptId,
      userName: arr[0].userName,
    },
  ];
  isShowAside3.value = false;
  launchUserNameRef.value.restoreValidation();
};
const strr2 = ref('');
// 打开选人弹框
const radio = ref('');
const selectUserRadioRef = ref(null);
const userList: any = ref([]);
const xuan = () => {
  isShowAside3.value = true;

  // selectUserRef.value.getTreeAndTable();
  // userList.value = [
  //   {
  //     userId: formData.value.launchUserId,
  //     unitId: formData.value.launchPersonUnitId,
  //     deptId: formData.value.launchPersonDeptId,
  //   },
  // ];
  selectUserRadioRef.value.getList(userList.value);
  selectUserRadioRef.value.getTabData();
};
const xuan1 = () => {
  isShowAside.value = true;
  selectUserRef.value.getList(formData.value.userNameList);
  // selectUserRef.value.getTreeAndTable();
};

// 打开选人弹框
const xuan2 = () => {
  isShowAside2.value = true;
  strr.value = '发起方';
  QueryOrgTrees();
};
const xuan3 = () => {
  isShowAside2.value = true;
  strr.value = '负责方';
  QueryOrgTrees();
};
function setColumns() {
  columns.value.push(...cols);

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    minWidth: 100,
    align: 'center',
    fixed: 'right',
    render(row) {
      return getActionBtn(row);
    },
  });
}
function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-del-button-small',
          onClick: () =>
            // emits('action', { action: ACTION.DELET, data: toRaw(row) }),
            handleDelete(row),
        },
        { default: () => ACTION_LABEL.DELETE }
      ),
    ],
  ];

  return useActionDivider(acList);
}
setColumns();
const formRef: any = ref(null);
function handleSubmit() {
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      formData.value.levelPath = props.treeCode;
      let res = await addResponsibilityData(formData.value);
      if (res.code == 200) {
        message.success('新增成功');
        mittBus.emit('addUpdate', 1);
        props.getData();
        props.handleClose();
      }
    } else {
      return;
    }
  });
  // formRef.value?.validate(async (errors) => {});
  // const params = {};
  // run(postUpdate(params)).then(() => {});
  // emits('submitted');
}
// 上传回调
function handleUpdate(res: IUploadRes[]) {
  if (!res || !res.length) return;

  const result = res
    .filter((item) => item !== null)
    .map((item) => ({ address: item }));
  formData.value.attachments = result;
}
function handleRemovee(res: any) {
  const result = formData.value.attachments.filter(
    (item: any) => res.name !== item.address
  );
  formData.value.attachments = result;
}

// 移除
function handleDelete(row: any) {
  $dialog.error({
    title: '删除检查大类',
    content: '删除后该大类下的所有小类及检查项也将同步删除，确定删除？',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    // onPositiveClick: async () => {
    //   await delInspectCategoryTree({ id: data.id, type: '0' });
    //   getData();
    // },
  });
}
// init
// getData();

defineExpose({
  handleSubmit,
});

defineOptions({ name: 'CommitteeSecurityMangeAdd' });
</script>

<style module lang="scss">
:deep(.h-full) {
  width: 100%;

  .n-data-table-th {
    font-size: 12px;
    color: red;
  }
}

.wrap {
  padding: 24px;
}

.checkbox-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  row-gap: 20px;
  padding: 0 10px;
  margin: 20px 0 30px;
  min-height: 107px;
}

.red {
  color: #a30014;
}
</style>
