import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',

    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '姓名',
    key: 'unitName',
    align: 'center',

    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '手机号',
    key: 'unitTypeName',
    align: 'center',

    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '单位',
    key: 'areaName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '部门',
    key: 'isIotName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '部门',
    key: 'jurisdictionName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '岗位',
    key: 'jurisdictionName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
];
