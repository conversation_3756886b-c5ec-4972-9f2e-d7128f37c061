<template>
  <div :class="$style.wrap">
    <n-spin :show="loading" :delay="500">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="110"
        require-mark-placement="left"
      >
        <n-form-item label="安全职责名称" path="name">
          <n-input
            v-model:value="formData.name"
            show-count
            clearable
            maxlength="30"
            placeholder="请输入安全职责名称"
          />
        </n-form-item>
        <!-- <n-form-item label="安委会级别" path="safetyCommitteeLevelId">
          <n-select placeholder="请选择安委会级别" v-model:value="formData.safetyCommitteeLevelId" :options="categoryOpt"
            label-field="levelName" value-field="id" />
        </n-form-item> -->
        <n-form-item label="年度" path="time">
          <n-select
            v-model:value="formData.time"
            :options="yearOption"
            label-field="year"
            value-field="id"
          />
        </n-form-item>
        <n-form-item
          label="发起方"
          ref="launchDeptNameRef"
          path="launchDeptName"
        >
          <n-input
            @click="xuan2"
            v-model:value="formData.launchDeptName"
            readonly="true"
            placeholder="请从组织架构选择单位或部门"
          />
          <n-button @click="xuan2()" style="margin-left: 10px">选择</n-button>
        </n-form-item>
        <!-- <n-form-item label="发起方安委会" path="launchSafetyCommitteeId">
          <n-select
            v-model:value="formData.launchSafetyCommitteeId"
            :options="launchCategoryOpt"
            label-field="name"
            value-field="id"
            placeholder="请选择发起方安委会"
            :disabled="!formData.launchDeptId"
          />
        </n-form-item> -->
        <n-form-item label="发起方责任人" path="launchUserName">
          <n-input
            @click="xuan"
            v-model:value="formData.launchUserName"
            readonly="true"
            placeholder=" 请从组织架构选人"
          />
          <n-button @click="xuan()" style="margin-left: 10px">选择</n-button>
        </n-form-item>
        <n-form-item label="责任方" path="deptName" ref="deptNameRef">
          <n-input
            v-model:value="formData.deptName"
            @click="xuan3"
            readonly="true"
            placeholder=" 请从组织架构选择单位或部门"
          />
          <n-button @click="xuan3()" style="margin-left: 10px">选择</n-button>
        </n-form-item>
        <!-- <n-form-item label="责任方安委会" path="responsibleSafetyCommitteeId">
          <n-select
            v-model:value="formData.responsibleSafetyCommitteeId"
            :options="deptCategoryOpt"
            label-field="name"
            value-field="id"
            placeholder="请选择责任方安委会"
            :disabled="!formData.deptId"
          />
        </n-form-item> -->
        <n-form-item label="责任方责任人" path="fuze">
          <n-input
            v-model:value="formData.fuze"
            readonly="true"
            @click="xuan1"
            placeholder=" 请从组织架构选人"
          />
          <n-button @click="xuan1()" style="margin-left: 10px">选择</n-button>
        </n-form-item>
        <n-form-item style="margin-left: 16%">
          <file-upload
            accept=".docx, .pdf,.doc"
            :data="fileData"
            @removee="handleRemove"
            @update="handleUpdate"
            :max="3"
          ></file-upload>
        </n-form-item>
      </n-form>
    </n-spin>
    <selectUser
      ref="selectUserRef"
      :parentOrgCode="orgCode"
      @getPersonManageData="getTreeDataPerson1"
      v-model:show="isShowAside"
      :title="actionLabel"
      @close="isShowAside = false"
      :userConfigFeid="true"
      @selectUserData="handleAdd"
    ></selectUser>
    <selectUserRadio
      ref="selectUserRadioRef"
      v-model:showModal="isShowAside3"
      @success="getData3"
      @close="isShowAside3 = false"
    ></selectUserRadio>
    <selectTree
      :dataList="treeData"
      v-model:showModal="isShowAside2"
      @success="getData2"
      @close="isShowAside2 = false"
    >
    </selectTree>
  </div>
</template>

<script setup lang="ts">
import selectUserRadio from '@/components/select-user/Select-user-radio.vue';
import selectUser from '@/components/select-user/Select-user.vue';
import { DataTableColumns, FormRules, NButton } from 'naive-ui';
import { computed, h, inject, onMounted, ref, Ref, VNode } from 'vue';
import { PROVIDE_KEY } from '../../constant';
import {
  detailSafyData,
  editResponsibilityData,
  getOrgTrees,
  getPersonManageList,
} from '../../fetchData';
import type { IActionData } from '../../type';
import selectTree from '../select-tree.vue';
// import { ICategory } from '../../type';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { FileUpload } from '@/components/upload';
import { IUploadRes } from '@/components/upload/type';
import { useStore } from '@/store';
import { getSafeTyData } from '@/views/committeeSecurityMange/fetchData';
import { useMessage } from 'naive-ui';
import { ACTION_LABEL } from '../../constant';
import { cols } from './columns';
const message = useMessage();
const store = useStore();
const selectUserRef: any = ref(null);
const isShowAside = ref(false);
const isShowAside2 = ref(false);
const isShowAside3 = ref(false);
const treeData = ref([]);
const formData: any = ref({
  fuze: '',
  userNameList: [],
  attachments: [],
  launchUserId: '',
  deptId: '',
  levelPath: '',
  launchSafetyCommitteeId: '',
  responsibleSafetyCommitteeId: '',
});
const rules: FormRules = {
  name: [
    {
      required: true,
      message: '请输入安全职责名称',
      trigger: ['blur', 'input'],
    },
  ],
  time: { required: true, message: '请选择年度', trigger: ['blur', 'change'] },
  safetyCommitteeLevelId: {
    required: true,
    message: '请选择安委会级别',
    trigger: ['blur', 'change'],
  },
  launchDeptName: [
    {
      required: true,
      message: '请选择负责单位/部门',
      trigger: ['blur', 'input'],
    },
  ],
  launchUserName: {
    required: true,
    message: '请选择发起方责任人',
    trigger: ['blur', 'input'],
  },
  deptName: {
    required: true,
    message: '请选择责任方',
    trigger: ['blur', 'input'],
  },
  fuze: {
    required: true,
    message: '请选择责任方责任人',
    trigger: ['blur', 'input'],
  },
  launchSafetyCommitteeId: {
    required: true,
    message: '请选择发起方安委会',
    trigger: ['blur', 'change'],
  },
  responsibleSafetyCommitteeId: {
    required: true,
    message: '请选择责任方安委会',
    trigger: ['blur', 'change'],
  },
};
const orgCode = ref(store.userInfo.unitId);
const launchCategoryOpt = ref([]);
const deptCategoryOpt = ref([]);
const launchDeptNameRef: any = ref('');
const deptNameRef: any = ref('');
async function getTreeDataPerson1(params: any) {
  let res = await getPersonManageList({ ...params, type: 1 });

  selectUserRef.value.renderTable(res);
}
const getData2 = (str: any) => {
  if (strr.value == '发起方') {
    formData.value.launchDeptName = str.text;
    formData.value.launchDeptId = str.id;
    formData.value.launchSafetyCommitteeId = null;
    isShowAside2.value = false;
    launchDeptNameRef.value.restoreValidation();
    getSecurityCommissionType(str.id, props.treeCode, str.id, 'launch');
  } else {
    formData.value.deptName = str.text;
    formData.value.levelPath = str.levelCode;
    formData.value.deptId = str.id;
    formData.value.responsibleSafetyCommitteeId = null;
    isShowAside2.value = false;
    deptNameRef.value.restoreValidation();
    getSecurityCommissionType(str.id, props.treeCode, str.id, 'dept');
  }
};
const getData3 = (arr: any) => {
  formData.value.launchUserName = arr[0].userName;
  formData.value.launchUserId = arr[0].id;
  formData.value.launchPersonUnitName = arr[0].unitName;
  formData.value.launchPersonUnitId = arr[0].unitId;
  formData.value.launchPersonDeptId = arr[0].deptId;
  userList.value = [
    {
      id: arr[0].id,
      unitId: arr[0].unitId,
      deptId: arr[0].deptId,
      userName: arr[0].userName,
    },
  ];
};
const handleAdd = (arr: any) => {
  formData.value.userNameList = arr.map((item: any) => {
    item.userId = item.userId ? item.userId : item.id;
    return item;
  });
  formData.value.fuze = formData.value.userNameList
    .map((user: any) => user.userName)
    .join('、');
};
const emits = defineEmits(['action', 'submitted']);
const columns = ref<DataTableColumns>([]);
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const actionData = computed(() => currentAction.value.data);
const [loading, run] = useAutoLoading(false);
const [loading1, search] = useAutoLoading(false);
const jurChecked = ref<string[]>([]);
const currentYear = new Date().getFullYear();
const previousYear = currentYear - 1;
const nextYear = currentYear + 1;
const yearOption: any = ref([
  { year: `${previousYear}`, id: `${previousYear}` },
  { year: `${currentYear}`, id: `${currentYear}` },
  { year: `${nextYear}`, id: `${nextYear}` },
]);
const props: any = defineProps({
  id: {
    type: String,
  },
  getData: {
    type: Function,
  },
  handleClose: {
    type: Function,
  },
  treeId: {
    type: String,
  },
  treeCode: {
    type: String,
  },
});
function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };

    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }

    return newItem; // 返回新对象
  });
}
//获取树结构数据
function QueryOrgTrees() {
  const params = {
    orgCode: store.userInfo.unitId,
    needChildUnit: '1',
    needself: '1',
  };
  search(getOrgTrees(params)).then((res: any) => {
    if (res.code != 200) return;
    const _RES: any = removeEmptyChildren(res.data);
    treeData.value = _RES;
  });
}
// 获取安委会级别列表
function getSecurityCommissionType(
  unitId: string,
  levelCode: string,
  id: string,
  type: 'launch' | 'dept'
) {
  let params = {
    pageNo: -1,
    pageSize: 1000,
    unitId,
    levelCode,
    id,
    type: '2',
  };
  search(getSafeTyData(params)).then((res: any) => {
    if (res.code != 200) return;
    if (type === 'launch') {
      launchCategoryOpt.value = res.data.rows;
    } else {
      deptCategoryOpt.value = res.data.rows;
    }
  });
}
onMounted(() => {
  QueryOrgTrees();
  if (props.treeCode && props.treeId) {
    getSecurityCommissionType(props.treeId, props.treeCode, '', 'launch');
  }
});
const levelPath = ref('');
const fileData = ref<any[]>([]);
function getDetailData() {
  const id = actionData.value.id;
  if (id) {
    run(detailSafyData(props.id)).then(async (res: any) => {
      if (res.code == 200) {
        formData.value = res.data;

        // 先获取发起方和责任方的安委会列表
        if (formData.value.launchDeptId) {
          await getSecurityCommissionType(
            formData.value.launchDeptId,
            props.treeCode,
            formData.value.launchDeptId,
            'launch'
          );
        }
        if (formData.value.deptId) {
          await getSecurityCommissionType(
            formData.value.deptId,
            props.treeCode,
            formData.value.deptId,
            'dept'
          );
        }

        // 设置安委会的值
        formData.value.launchSafetyCommitteeId =
          res.data.launchSafetyCommitteeId;
        formData.value.responsibleSafetyCommitteeId =
          res.data.responsibleSafetyCommitteeId;

        levelPath.value = formData.value.levelPath;
        formData.value.fuze = formData.value.userNameList
          .map((user: any) => user.userName)
          .join('、');

        if (formData.value.attachments == '') {
          formData.value.attachments = [];
        } else {
          fileData.value = res.data.attachments.map((item: any) => {
            const fileName = item.address.split('/').pop();
            return {
              ...item,
              name: fileName,
            };
          });
        }
      }
    });
  }
}
// 打开选人弹框
const userList: any = ref([]);
const selectUserRadioRef = ref(null);
const xuan = () => {
  userList.value = [
    {
      userId: formData.value.launchUserId,
      unitId: formData.value.launchPersonUnitId,
      deptId: formData.value.launchPersonDeptId,
      userName: formData.value.launchUserName,
    },
  ];
  isShowAside3.value = true;
  selectUserRadioRef.value.getList(userList.value);
  selectUserRadioRef.value.getTabData();
};
const xuan1 = () => {
  isShowAside.value = true;
  selectUserRef.value.getList(formData.value.userNameList);
};
const strr = ref('');
// 打开选人弹框
const xuan2 = () => {
  strr.value = '发起方';
  isShowAside2.value = true;
};
const xuan3 = () => {
  strr.value = '负责方';
  isShowAside2.value = true;
};
const formRef: any = ref(null);
function handleSubmit() {
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      // formData.value.levelPath = levelPath.value;
      if (formData.value.attachments == '') {
        formData.value.attachments = [];
        let res = await editResponsibilityData(formData.value);
        if (res.code == 200) {
          message.success('编辑成功');
          props.getData();
          props.handleClose();
        }
      } else if (
        formData.value.attachments.length > 0 ||
        formData.value.attachments.length === 0
      ) {
        let res = await editResponsibilityData(formData.value);
        if (res.code == 200) {
          message.success('编辑成功');
          props.getData();
          props.handleClose();
        }
      }
    } else {
      return;
    }
  });
}
// 删除回调
function handleRemove(data: any) {
  formData.value.attachments = formData.value.attachments.filter(
    (item: any) => {
      if (item.id !== data.id) {
        return item;
      }
    }
  );
}
// 上传回调
function handleUpdate(res: IUploadRes[]) {
  if (!res || !res.length) return;

  const result = res
    .filter((item) => item !== null)
    .map((item) => ({ address: item }));

  // 使用 concat 拼接到 attachments
  formData.value.attachments = formData.value.attachments.concat(result);

  // 过滤掉空的附件
  formData.value.attachments = formData.value.attachments.filter(
    (item) => item && item.address // 这里检查 item 是否有效
  );
  const uniqueAddresses = new Set();
  formData.value.attachments = formData.value.attachments.filter((item) => {
    const isDuplicate = uniqueAddresses.has(item.address);
    uniqueAddresses.add(item.address);
    return !isDuplicate; // 返回 false 以删除重复项
  });
}
function handleRemovee(res: any) {
  const result = formData.value.attachments.filter(
    (item: any) => res.name !== item.address
  );
  formData.value.attachments = result;
}

// 移除
function handleDelete(row: any) {
  $dialog.error({
    title: '删除检查大类',
    content: '删除后该大类下的所有小类及检查项也将同步删除，确定删除？',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    // onPositiveClick: async () => {
    //   await delInspectCategoryTree({ id: data.id, type: '0' });
    //   getData();
    // },
  });
}
function setColumns() {
  columns.value.push(...cols);

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    minWidth: 100,
    align: 'center',
    fixed: 'right',
    render(row) {
      return getActionBtn(row);
    },
  });
}
function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-del-button-small',
          onClick: () =>
            // emits('action', { action: ACTION.DELET, data: toRaw(row) }),
            handleDelete(row),
        },
        { default: () => ACTION_LABEL.DELETE }
      ),
    ],
  ];

  return useActionDivider(acList);
}
// init
getDetailData();
setColumns();
defineExpose({
  handleSubmit,
});

defineOptions({ name: 'CommitteeSecurityMangeEdit' });
</script>

<style module lang="scss">
.wrap {
  padding: 24px;
}

.checkbox-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  row-gap: 20px;
  padding: 0 10px;
  margin: 20px 0 30px;
  min-height: 107px;
}

.red {
  color: #a30014;
}
</style>
