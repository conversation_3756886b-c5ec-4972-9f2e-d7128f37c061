import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '责任书名称',
    key: 'name',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },

  {
    title: '年度',
    key: 'time',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  // {
  //   title: '类型',
  //   key: 'launchDeptTypeName',
  //   align: 'center',
  //   ellipsis: {
  //     tooltip: true,
  //   },
  // },
  {
    title: '责任方责任人',
    key: 'userName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '责任部门',
    key: 'deptName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
];
