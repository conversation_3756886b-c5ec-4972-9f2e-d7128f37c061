<template>
  <div class="box">
    <div class="head px-[24px]" style="display: flex; justify-content: space-between">
      <span></span>
      <button class="btn" @click="doHandle(ACTION.ADD)">新增</button>
    </div>
    <n-data-table
      class="h-full com-table"
      :flex-height="true"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :loading="loading"
      :pagination="pagination"
      :render-cell="useEmptyCell"
      :theme-overrides="themeOverrides"
    />
  </div>
</template>

<script lang="ts" setup>
import type { IPageData } from '../../type';
import { ACTION, ACTION_LABEL } from '../../constant';
import { cols } from '../../comp/table/columns';
import { Base64 } from 'js-base64';
import { DataTableColumns, NButton } from 'naive-ui';
import { h, ref, toRaw, VNode, onMounted, watch } from 'vue';
import { getTreeDataPerson, delResponsibility } from '../../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { IObj } from '@/types';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { trimObjNull } from '@/utils/obj.ts';
import { useStore } from '@/store';
import mittBus from '@/utils/mittBus';
const store = useStore();
const emits = defineEmits(['action']);
// 表格样式
const themeOverrides = {
  thColor: '#BBCCF3',
  thTextColor: '#222',
  tdColorStriped: '#dfeefc',
  // tdColorHover: 'rgba(18, 83, 123, 0.35)',
  tdColorStripedModal: 'red',
};
const props = defineProps({
  code: {
    type: String,
  },
});
const [loading, search] = useAutoLoading(true);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IPageData[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData);

let filterData: IObj<any> = {}; // 搜索条件
const newObj = ref({});
const levelCode = ref('');
mittBus.on('addUpdate', (v: any) => {
  pagination.page = v;
});
async function getTableData() {
  const params = {
    levelPath: levelCode.value ? levelCode.value : store.userInfo.unitId,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    type: 1,
  };
  search(getTreeDataPerson(params)).then((res: any) => {
    if (res.code == 200) {
      tableData.value = res.data.rows || [];
      updateTotal(res.data.total || 0);
    }
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

function setColumns() {
  columns.value.push(...cols);

  // 添加操作栏 action
  columns.value.push(
    {
      title: '附件',
      key: 'attachments',
      width: '100',
      align: 'center',
      render: (row: any) => {
        if (!row.attachments.length) {
          return '--';
        }
        // 假设 row.attachments 是一个包含附件信息的数组
        return row.attachments.map((attachment: any) => {
          // 根据附件类型生成相应的虚拟DOM元素
          const name = attachment.address.split('/');
          const fileName = name[name.length - 1];
          const type = fileName.split('.');
          const typeName = type[type.length - 1];
          if (typeName == 'doc' || typeName == 'docx') {
            console.log(window.$SYS_CFG.apiPreviewURL + attachment.address, 'word');
            var b64Encoded = Base64.encode(window.$SYS_CFG.apiPreviewURL + attachment.address);
            return h(
              'a',
              {
                href: window.$SYS_CFG.apiPreviewLink + encodeURIComponent(b64Encoded),
                title: fileName,
                target: '_blank',
                data: window.$SYS_CFG.apiPreviewURL + attachment.address,
                style: {
                  color: '#02a7f0',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                  overflow: 'hidden',
                  width: '120px',
                  display: 'inline-block',
                },
              },
              fileName
            );
          } else {
            console.log(window.$SYS_CFG.apiPreviewURL + attachment.address, 'pdf');
            return h(
              'a',
              {
                href: window.$SYS_CFG.apiPreviewURL + attachment.address,
                title: fileName,
                target: '_blank',
                data: window.$SYS_CFG.apiPreviewURL + attachment.address,
                style: {
                  color: '#02a7f0',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                  overflow: 'hidden',
                  width: '120px',
                  display: 'inline-block',
                },
              },
              fileName
            );
          }
        });
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 290,
      align: 'center',
      render(row) {
        return getActionBtn(row);
      },
    }
  );
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-edit-button ',
          onClick: () => emits('action', { action: ACTION.EDIT, data: toRaw(row) }),
          // handleDelete(row),
        },
        { default: () => '编辑' }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-del-button',
          onClick: () =>
            // emits('action', { action: ACTION.DELET, data: toRaw(row) }),
            handleDelete(row),
        },
        { default: () => '删除' }
      ),
    ],
  ];

  return useActionDivider(acList);
}
// 移除
function handleDelete(row: any) {
  $dialog.error({
    title: '删除',
    content: '确定删除吗?',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      await delResponsibility(row.id);
      if (tableData.value.length != 0 && tableData.value.length - 1 == 0 && pagination.page != 0) {
        pagination.page = pagination.page - 1;
      }
      getTableData();
    },
  });
}
// 初始化项目
function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: {},
  });
}
onMounted(() => {
  doHandle(ACTION.SEARCH);
});
// on created
setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
});
watch(
  () => props.code,
  (val) => {
    if (val != null) {
      levelCode.value = val;
      getTableData();
    }
  }
  // { immediate: true }
);
defineOptions({ name: 'ExpertDatabaseTable' });
</script>

<style scoped lang="scss">
.btn {
  margin-left: 88%;
  // margin-top: 15px;
  margin-bottom: 15px;
  width: 88px;
  height: 34px;
  background-color: #3e62eb;
  color: #fff;
  border-radius: 4px;
}
.btn:hover {
  background-color: #6889f7;
}
.n-button {
  width: 56px !important;
  height: 34px !important;
  border-radius: 3px !important;
}
</style>
