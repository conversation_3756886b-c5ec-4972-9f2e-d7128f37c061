import type { IDetail, IPageDataRes, OrgTree } from './type';
import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';

// 获取分页
export function pageData(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.jurisdictionPageList, query);
  return $http.get<IPageDataRes>(url, { data: { _cfg: { showTip: false } } });
}

// 获取详情
export function getDetail(id: string) {
  const url = api.getUrl(api.type.demo, api.name.demo.jurisdictionDetail, {
    id,
  });
  return $http.get<IDetail>(url, { data: { _cfg: { showTip: true } } });
}

// 更新
export function postUpdate(data: { id: string; jurisdiction: string }) {
  const url = api.getUrl(api.type.demo, api.name.demo.jurisdictionUpdate);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true }, ...data },
  });
}
// 获取树形结构
export function getOrgTrees(query: IObj<any>) {
  return $http.get<OrgTree>(api.getUrl(api.type.server, api.name.interface.getTreeData, query));
}

// 获取树结构列表
export function getTreeDataPerson(data: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getResponsibilityList, data);
  return $http.get<IDetail>(url, { data: { _cfg: { showTip: true } } });
}
// 获取树结构列表
export function getTreeDataPerson2(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getTreeDataPerson);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true }, ...params },
  });
}
// 删除安全职责列表
export function delResponsibility(id: string) {
  const url = api.getUrl(api.type.server, api.name.interface.delResponsibilityList, { id });
  return $http.delete<any>(url, {
    data: { _cfg: { showTip: true, showOkTip: false } },
  });
}
// 安全列表新增
export function addResponsibilityData(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.addResponsibilityList);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}
// 修改列表
export function editResponsibilityData(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.editResponsibilityList);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}
// 详情
export function detailSafyData(id: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getResponsibilityDetail, {
    id,
  });
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}
// 获取人员列表
export function getPersonManageList(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getExpertPersonManageList);
  return $http.post<IPageDataRes>(url, {
    data: { _cfg: { showTip: true }, ...params },
  });
}
