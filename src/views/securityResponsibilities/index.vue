<template>
  <div class="">
    <!-- <div class="head_title" style="margin-top: 10px; margin-bottom: 15px; margin-left: 5px">安全职责</div> -->
    <com-bread :data="breadData"></com-bread>
    <!-- h-[calc(100%-46px)] -->
    <div class="flex h-[calc(100%-45px)]">
      <transition name="slide-fade">
        <div class="h-[calc(100%)]" v-show="formVisible">
          <comTree
            class="tree"
            @treeChange="treeChange"
            :dataList="treeData"
            @action="actionFn"
          ></comTree>
        </div>
      </transition>

      <div
        class="!ml-[15px] bg-[#eef7ff]"
        style="border-radius: 5px; position: relative"
      >
        <img
          @click="formVisible = !formVisible"
          src="@/assets/open.png"
          style="
            position: absolute;
            left: -1%;
            top: 47.6%;
            width: 30px;
            cursor: pointer;
          "
        />
        <Table
          class="com-table-container h-[calc(100%-50px)]"
          :code="treeLevelCode"
          ref="tableCompRef"
          @action="actionFn"
        />
      </div>
    </div>
    <AsideComp
      :getData="getData"
      :treeId="treeId"
      v-model:show="isShowAside"
      :title="actionLabel"
      ref="asideCompRef"
      @action="actionFn"
      :treeCode="treeLevelCode"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, provide, Ref, ref, onMounted } from 'vue';
import { NIcon, TreeOption } from 'naive-ui';
import Table from './comp/table/Table.vue';
import AsideComp from './comp/aside/index.vue';
import comTree from './comTree.vue';
import {
  CaDataStructured as structure,
  CaEnterprise as unit,
} from '@kalimahapps/vue-icons';
import type { IActionData } from './type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { getOrgTrees, getTreeDataPerson } from './fetchData';
import { useStore } from '@/store';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from './constant';
const breadData: any[] = [{ name: '安全职责' }];
const store = useStore();
const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);
const isShowAside = ref(false);
const tableCompRef = ref();
const formVisible = ref(true);
const treeData = ref<any[]>([]);
const [loading, search] = useAutoLoading(true);
const unitId = ref('10000'); // 登录的orgcode
// 递归函数，使用map生成新的数组
function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };

    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }

    return newItem; // 返回新对象
  });
}

//获取树结构数据
function QueryOrgTrees() {
  const params = {
    orgCode: store.userInfo.unitId,
    needChildUnit: '1',
    needself: '1',
  };
  search(getOrgTrees(params)).then((res: any) => {
    if (res.code != 200) return;
    const _RES: any = removeEmptyChildren(res.data);
    treeData.value = _RES;
  });
}
const asideCompRef: any = ref(null);
const levelCode = ref('');
const newObj: any = ref({});
const getTableData = async (obj: any) => {
  newObj.value = obj;
  levelCode.value = newObj.value.levelPath;
  tableCompRef.value.getTableData();
  asideCompRef.value.getLevelPath(obj.levelPath);
};
onMounted(() => {
  QueryOrgTrees();
});
const getData = () => {
  tableCompRef.value.getTableData();
};
function handleSearch(data?: Record<string, any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}
const treeLevelCode: any = ref(null);
const treeId = ref('');
// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);
const treeChange = (v: any) => {
  treeLevelCode.value = v.levelCode;
  treeId.value = v.id;
  // handleSearch();
};
function actionFn(val: IActionData) {
  currentAction.value = val;

  if (val.action === ACTION.SEARCH) {
    // handleSearch(val.data);
  } else if (val.action === ACTION.EDIT) {
    isShowAside.value = val.action === ACTION.EDIT;
    asideCompRef.value.getEditFormData(val.data.id);
  } else {
    isShowAside.value = val.action === ACTION.ADD;
  }
}

defineOptions({ name: 'SecurityResponsibilitiesIndex' });
</script>
