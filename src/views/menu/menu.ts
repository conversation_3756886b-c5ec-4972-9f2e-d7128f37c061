/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-15 14:50:52
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-16 09:16:47
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/views/menu/menu.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import type { IMenu } from '@/views/menu/type';

/**
 * 菜单 - 支队、大队
 */
export const menuDataList: IMenu[] = [
  {
    label: '首页',
    key: 'home', // 一般同路由名
    icon: 'sy', // 图标名
    routeName: 'home', // 路由名称
  },
  {
    label: '人员管理',
    key: 'personManage', // 一般同路由名
    icon: 'sy', // 图标名
    routeName: 'personManage', // 路由名称
  },
  {
    label: '证书管理',
    key: 'zsgl',
    icon: 'pzgl',
    routeName: 'certificateManagement',
  },
  {
    icon: 'pzgl',
    label: '安委会管理',
    key: 'committeeSecuitymange',
    routeName: 'committeeSecuitymange',
  },
  {
    label: '专家库',
    icon: 'pzgl',
    key: 'expertdatabase',
    routeName: 'expertdatabase',
  },

  {
    icon: 'pzgl',
    label: '安全职责',
    key: 'securityresponsibilities',
    routeName: 'securityresponsibilities',
  },
  {
    icon: 'pzgl',
    label: '安全会议',
    key: 'securitymeetings',
    routeName: 'securitymeetings',
  },
  ,
  {
    icon: 'pzgl',
    label: '奖惩记录',
    key: 'rewarpunishmentrecord',
    routeName: 'rewarpunishmentrecord',
  },
];
