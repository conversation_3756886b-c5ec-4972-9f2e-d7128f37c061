<template>
  <n-layout has-sider>
    <n-layout-sider
      :class="{ [$style.wrap]: true, [$style.expand]: isExpand }"
      :collapsed-width="48"
      :collapsed="isExpand"
      :width="240"
      @collapse="setExpand(false)"
      @expand="setExpand(true)"
      bordered
      collapse-mode="width"
    >
      <div style="height: 48px; line-height: 34px">
        <n-button
          style="padding-left: 0"
          :class="$style['btn-trigger']"
          class="jzy-button"
          @click="setExpand(!isExpand)"
          text
        >
          <IconExpand
            :class="{ [$style['icon-expand']]: true, [$style['re']]: isExpand }"
          />
        </n-button>
        <span
          style="position: absolute; left: 45px; top: 7px; font-size: 16px"
          v-if="!isExpand"
          >{{ sysName }}</span
        >
      </div>

      <n-scrollbar :style="`max-height: ${scrollMaxH}`">
        <!-- <div class="jzy-title">
          <img
            src="@/assets/icon1.svg"
            alt=""
            style="display: inline-block; margin-left: 12px"
          /> -->
        <!-- </div> -->
        <n-menu
          v-model:value="activeKey"
          :collapsed="isExpand"
          :options="menuList"
          :inverted="isExpand"
          key-field="routeName"
          :icon="renderIcon"
          :render-icon="renderMenuIcon"
          children-field="childrens"
          accordion
          @update:value="handleUpdateValue"
          :theme-overrides="themeOverrides"
          class="com-menu"
        />
      </n-scrollbar>
    </n-layout-sider>
  </n-layout>
</template>

<script lang="ts" setup>
import img1 from '@/assets/logo.png';
import { useState } from '@/common/hooks/useState.ts';
import { useStore } from '@/store';
import { useMenu } from '@/views/menu/useMenu';
import { computed, h, onMounted, ref, watch } from 'vue';
import { icon_expand as IconExpand } from './assets/icon/index';
const store = useStore();
const themeOverrides = {
  nodeColorHover: '#527CFF',
  nodeColorActive: '#527CFF',
};

// function renderIcon() {
//   return h('img', {
//         src:img1,
//       });
// }
function renderIcon(icon: any) {
  // console.log(icon, '>>>>>>>>>>>>>>>>');
  return () => h('img', null, { default: () => h(img1) });
}
// render: function (data: any) {
//       console.log(data.filePath, '这里是data加图片的数据--------');
//       if (data.filePath == '') {
//         return '--';
//       }
//       return h('img', {
//         src: `http://*************:9862${data.filePath}`,
//       });
//     },
// 目标与职责
const sysName = ref('');
// 批量渲染菜单svg图标
function renderMenuIcon(option: any) {
  if (option.icon) {
    return h(
      'img',
      {
        src: window.$SYS_CFG.apiBase + '/' + option.icon,
        width: 24,
        height: 24,
      },
      { default: () => null }
    );
  } else {
    return '';
  }
}
onMounted(() => {
  if (store && store.userInfo && store.userInfo.zhLogo) {
    sysName.value =
      store.userInfo.zhLogo === 'yanchang'
        ? '目标与职责'
        : '安全组织与人员配备';
  }
  console.log(sysName.value, ' sysName.value');
});
const { menuList, activeKey, handleUpdateValue } = useMenu();
const [isExpand, setExpand] = useState(false);
const props = defineProps({
  headless: Boolean,
});

const scrollMaxH = computed(() => {
  return props.headless ? 'calc(100vh - 48px)' : 'calc(100vh - 64px - 48px)';
});

defineOptions({ name: 'MenuIndex' });
</script>

<style module lang="scss">
.n-layout {
  background: #252843 !important;
}

.wrap {
  background: #252843 !important;

  &.expand {
    background: #252843 !important;
  }
}

.btn-trigger {
  width: 100%;
  height: 48px;
  justify-content: flex-start;
  // padding-left: 32px;
  position: absolute;
  top: 4px;
  left: 4px;
  font-size: 20px;
  color: #fff;

  .icon-expand {
    color: #fff;

    &.re {
      transform-origin: center center;
      transform: rotate(-180deg);
      color: #fff;
    }
  }
}

.jzy-title {
  width: 100%;
  height: 42px;
  line-height: 42px;
}
</style>
<style lang="scss" scoped>
:deep(.n-menu .n-submenu .n-submenu-children) {
  background-color: #363d64;
}

:deep(.n-menu .n-menu-item-content .n-menu-item-content__arrow) {
  transform: rotate(180deg);
  color: #fff;
}

:deep(
  .n-menu
    .n-menu-item-content.n-menu-item-content--collapsed
    .n-menu-item-content__arrow
) {
  transform: rotate(0deg);
  color: #fff;
}
</style>
