import type { IMenu } from '@/views/menu/type';
import { h, ref, watch } from 'vue';
import * as IconMap from './assets/icon';
import certificateManagement from './assets/icon/certificateManagement.png';
import committeeSecuityMange from './assets/icon/committeeSecuitymange.png';
import expertDatabase from './assets/icon/expertdatabase.png';
import paramConfig from './assets/icon/paramConfig.png';
import personManage from './assets/icon/personManage.png';
import rewarpunishmentrecord from './assets/icon/rewarpunishmentrecord.png';
import securityResponsibilities from './assets/icon/securityResponsibilities.png';
import securityMeetings from './assets/icon/securitymeetings.png';
import home from './assets/icon/home.png';
import dutyEvaluation from './assets/icon/lzpg.png';
// import { menuDataList } from './menu';
import { useStore } from '@/store';
import { IObj } from '@/types';
import { MenuOption, NIcon } from 'naive-ui';
import { type LocationQueryRaw, useRouter } from 'vue-router';

const icons: any = {
  personManage,
  certificateManagement,
  committeeSecuityMange,
  expertDatabase,
  securityResponsibilities,
  securityMeetings,
  rewarpunishmentrecord,
  paramConfig,
  home,
  dutyEvaluation,
};

export function useMenu() {
  const store = useStore();
  const router = useRouter();
  const rName = router.currentRoute.value.name as string;
  const rQuery = router.currentRoute.value.query;
  const rMeta = router.currentRoute.value.meta;
  const activeKey = ref(addTabParamToNameIfPresent(rName, rQuery, rMeta));
  const menuList = transMenuList(store.userInfo.menuDataList);

  function handleUpdateValue(key: string, item: MenuOption) {
    activeKey.value = key;

    if (typeof item.routeName === 'string' && item.routeName) {
      const { routeName, routeQuery } = item;
      const name = processName(routeName);
      const query = routeQuery as LocationQueryRaw;
      router.push({ name, query });
    }
  }

  watch(
    () => router.currentRoute.value,
    (val) => {
      if (val.name) {
        console.log('router.currentRoute.value', val);
        activeKey.value = addTabParamToNameIfPresent(<string>val.name, val.query, val.meta);
        console.log('activeKey.value==========', activeKey.value);
      }
    }
  );

  return { menuList, activeKey, handleUpdateValue };
}

// 转换menu列表数据 - 处理icon
function transMenuList(menus: IMenu[]) {
  return menus.map((menu) => {
    const menuOption = { ...menu };

    if (typeof menu.icon === 'string' && menu.icon) {
      // menuOption.icon = renderMenuIcon(icons[menu.icon] || '');
    }

    if (menuOption.childrens) {
      const children = <IMenu[]>menuOption.childrens;
      menuOption.childrens = transMenuList(children);
    }

    return menuOption;
  });
}

function renderMenuIcon(k: string) {
  const iconKey = <keyof typeof IconMap>`icon_${k}`;
  const icon = IconMap[iconKey];

  return () =>
    h(NIcon, null, {
      default: () =>
        h('img', {
          src: k,
        }),
    });
}

function processName(name: string) {
  return name.replace(/\?.*$/, '');
}

/**
 * 还原tab参数
 * @param name
 * @param query
 * @param meta
 */
function addTabParamToNameIfPresent(name: string, query: LocationQueryRaw, meta: IObj<any>) {
  const tab = query.tab;
  const includeTab = meta?.includeTab;
  return tab && includeTab ? `${name}?tab=${tab}` : name;
}
