<template>
  <div :class="$style.DutyEvaluationModelItem">
    <div class="com-g-row-aa text-center gap-y-[5px]">
      <IconPlus style="width: 32px; height: 32px" />
      <div>添加</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ByPlus as IconPlus } from '@kalimahapps/vue-icons';

defineOptions({ name: 'DutyEvaluationModelItem' });
</script>

<style module lang="scss">
.DutyEvaluationModelItem {
  display: grid;
  justify-content: center;
  align-items: center;
  width: 210px;
  height: 108px;
  color: #0f4198;
  background: linear-gradient(90deg, #ccdfff 1%, #e5efff 100%);
  border: 2px solid #fff;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s;
  user-select: none;
  cursor: pointer;

  &:hover {
    border: 2px solid #3e62eb;
  }
}
</style>
