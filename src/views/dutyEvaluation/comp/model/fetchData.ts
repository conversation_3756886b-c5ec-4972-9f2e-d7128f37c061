import type { IModel } from './type';
import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';

export function getList(query: IObj<any>) {
  const url = api.getUrl(api.type.server, api.name.server.getEvaluateModelList, query);
  return $http.get<IModel[]>(url, { data: { _cfg: { showTip: true } } });
}

// 新增
export function postAdd(params: IObj<any>) {
  const url = api.getUrl(api.type.server, api.name.server.addEvaluateModel);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...params } });
}

// 编辑
export function postUpdate(params: IObj<any>) {
  const url = api.getUrl(api.type.server, api.name.server.updateEvaluateModel);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...params } });
}

// 删除
export function postDelete(params: IObj<any>) {
  const url = api.getUrl(api.type.server, api.name.server.deleteEvaluateModel);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...params } });
}
