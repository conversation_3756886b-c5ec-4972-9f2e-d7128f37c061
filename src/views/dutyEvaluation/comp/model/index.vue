<template>
  <div :class="$style.DutyEvaluationModelIndex">
    <p :class="$style.title">履职能力评估模型</p>
    <div :class="$style.content">
      <n-scrollbar x-scrollable>
        <div class="flex gap-x-[10px] pb-[12px]">
          <Item
            v-for="item of modelList"
            :key="item.id"
            :label="item.modelName"
            :active="curModelId === item.id"
            @edit="handleEdit(item)"
            @click="handleClick(item)"
          />
          <Add @click="handleAdd" />
        </div>
      </n-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import Item from './Item.vue';
import Add from './Add.vue';
import { ACTION } from '../../constant';
import { ref, watch } from 'vue';
import { IModel } from './type.ts';
import { getList } from './fetchData.ts';
import { useStore } from '@/store';
import { DutyEvaluationService } from '@/views/dutyEvaluation/DutyEvaluationService.ts';
const props = defineProps({ id: String });
const emits = defineEmits(['action']);
const store = useStore();

const modelList = ref<IModel[]>([]);
const curModelId = ref('');

function handleAdd() {
  emits('action', {
    action: ACTION.ADD,
    data: {},
  });
}

function handleClick(val: IModel) {
  curModelId.value = val.id;
}

function handleEdit(val: IModel) {
  emits('action', {
    action: ACTION.EDIT,
    data: { ...val },
  });
}

function getData() {
  getList({
    unitId: props.id,
  }).then((res) => {
    modelList.value = res.data || [];

    if (modelList.value.length) {
      curModelId.value = modelList.value[0].id || '';
    } else {
      curModelId.value = '';
    }
  });
}

// init

watch(
  () => curModelId.value,
  (val) => {
    // if (import.meta.env.DEV) {
    //   return DutyEvaluationService.curModelId$.next('0ad0a92d2025244e3d10eebd3051e9d5');
    // }
    DutyEvaluationService.setCurModelId(val);
  }
);
watch(
  () => props.id,
  (val) => {
    getData();
    // console.log(props.id,);
  }
);

defineExpose({
  getData,
});

defineOptions({ name: 'DutyEvaluationModelIndex' });
</script>

<style module lang="scss">
.DutyEvaluationModelIndex {
  height: 170px;
  display: grid;
  grid-template-rows: auto 1fr;
  row-gap: 10px;

  .title {
    font-size: 32px;
    color: #0f4298;
    font-weight: bold;
  }

  .content {
    display: grid;
  }
}
</style>
