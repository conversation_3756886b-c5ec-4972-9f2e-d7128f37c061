<template>
  <div :class="[$style.DutyEvaluationModelItem, active ? $style.active : '']">
    <span>{{ props.label }}</span>
    <n-button :class="$style.btnEdit" type="primary" size="tiny" @click="emits('edit')">编辑</n-button>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  label: String,
  active: Boolean,
});
const emits = defineEmits(['edit']);

defineOptions({ name: 'DutyEvaluationModelItem' });
</script>

<style module lang="scss">
.DutyEvaluationModelItem {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 210px;
  max-width: 300px;
  height: 108px;
  padding: 0 20px;
  font-size: 24px;
  color: #0f4298;
  font-weight: bold;
  white-space: nowrap;
  background: url('./assets/bg.png') 100% 0 no-repeat;
  background-size: cover;
  border: 2px solid #fff;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s;

  &:hover {
    border: 2px solid rgba(62, 98, 235, 0.5);

    .btnEdit {
      display: block !important;
    }
  }

  &.active {
    border: 2px solid #3e62eb;
  }

  .btnEdit {
    position: absolute;
    top: 8px;
    right: 8px;
    display: none;
  }
}
</style>
