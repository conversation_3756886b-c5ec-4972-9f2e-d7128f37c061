import { computed, onBeforeUnmount, ref, watch } from 'vue';
import { genCols } from './columns.ts';
import { QuotaService } from './QuotaService.ts';
import { getList, postSave } from './fetchData.ts';
import { DutyEvaluationService } from '@/views/dutyEvaluation/DutyEvaluationService.ts';
import { IObj } from '@/types';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { cloneDeep } from 'lodash-es';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import { parseInteger } from '@/utils/parse.ts';

const getEmptyTableData = () => [
  {
    pgwd: '',
    zfz: null,
    mrz: null,
    standards: [
      {
        pgbz: '',
        indicators: [
          {
            pgzb: null,
            pgfs: 0,
            pggz_symbol: null,
            pggz_number: null,
            pggz: '',
            pgfz: null,
            key: 1001,
          },
        ],
        key: 101,
      },
    ],
  },
];

export function useData() {
  const modelId = ref('');
  const reqData = ref([]); // 请求数据
  const dimensionData = ref<any[]>([]);
  let dimensionDataBak: any[] = [];
  const isEditMode = ref(false); // 添加编辑模式状态
  const [loading, wrapFn] = useAutoLoading(false);

  // 获取表格配置
  const { columns, rowKey } = genCols(dimensionData, isEditMode); // 传递编辑模式状态

  // 计算扁平化的表格数据
  const tableData = ref<any[]>([]);

  // 切换编辑模式
  async function toggleEditMode() {
    if (!modelId.value) {
      return $toast.warning('请您先添加履职能力评估模型');
    }

    if (isEditMode.value) {
      await saveData();
      tableData.value = [];
      isEditMode.value = false;
    } else {
      isEditMode.value = true;
      if (tableData.value.length === 0) {
        dimensionData.value = getEmptyTableData();
        tableData.value = QuotaService.flattenData(dimensionData);
      } else {
        dimensionDataBak = cloneDeep(dimensionData.value);
      }
    }
  }

  function getData(modelId: string) {
    wrapFn(getList({ modelId })).then((res) => {
      // 数据转换 接口到界面
      if (res.data?.length) {
        // 按dimensionId分组
        const groupByDimension: IObj<any> = {};
        res.data.forEach((item) => {
          if (!groupByDimension[item.dimensionId]) {
            groupByDimension[item.dimensionId] = {
              dimensionId: item.dimensionId,
              dimensionName: item.dimensionName,
              totalScore: item.totalScore,
              defaultScore: item.defaultScore,
              dimensionSortNum: item.dimensionSortNum || 0, // 添加维度排序字段
              standards: {},
            };
          }

          // 按standardId分组
          if (!groupByDimension[item.dimensionId].standards[item.standardId]) {
            groupByDimension[item.dimensionId].standards[item.standardId] = {
              standardId: item.standardId,
              standardName: item.standardName,
              standardSortNum: item.standardSortNum || 0, // 添加标准排序字段
              indicators: [],
            };
          }

          // 添加指标
          groupByDimension[item.dimensionId].standards[item.standardId].indicators.push({
            pgzb: item.metricCode,
            pgzbName: item.indexName,
            pgfs: item.evaluateType,
            pggz_symbol: item.ruleOperator,
            pggz_number: item.ruleThreshold,
            pggz: `${item.ruleOperator} ${item.ruleThreshold}`,
            pgfz: parseInteger(item.standardScore),
            sortNum: item.sortNum || 0, // 添加指标排序字段
            key: item.id,
          });
        });

        // 转换为dimensionData需要的格式
        // 按维度排序字段排序
        const sortedDimensions = Object.values(groupByDimension).sort(
          (a, b) => a.dimensionSortNum - b.dimensionSortNum
        );

        dimensionData.value = sortedDimensions.map((dimension) => {
          // 按标准排序字段排序
          const sortedStandards = Object.values(dimension.standards).sort(
            (a: any, b: any) => a.standardSortNum - b.standardSortNum
          );

          return {
            pgwd: dimension.dimensionName,
            zfz: parseInteger(dimension.totalScore), // 总分值
            mrz: parseInteger(dimension.defaultScore), // 默认值
            standards: sortedStandards.map((standard: any) => {
              // 按指标排序字段排序
              const sortedIndicators = standard.indicators.sort((a: any, b: any) => a.sortNum - b.sortNum);

              return {
                pgbz: standard.standardName,
                indicators: sortedIndicators,
              };
            }),
          };
        });
      }

      // 更新表格数据
      tableData.value = QuotaService.flattenData(dimensionData);
    });
  }

  function saveData() {
    return new Promise((resolve) => {
      const saveApiData = dimensionData.value.map((dimension: any) => {
        return {
          dimensionName: dimension.pgwd,
          modelId: modelId.value,
          totalScore: dimension.zfz,
          defaultScore: dimension.mrz,
          standardList: dimension.standards.map((standard: any) => {
            return {
              standardName: standard.pgbz,
              modelId: modelId.value,
              indexList: standard.indicators.map((indicator: any) => {
                return {
                  evaluateType: indicator.pgfs,
                  indexName: indicator.pgzbName,
                  metricCode: indicator.pgzb,
                  standardScore: indicator.pgfz,
                  ruleOperator: indicator.pggz_symbol,
                  ruleThreshold: indicator.pggz_number,
                };
              }),
            };
          }),
        };
      });

      postSave({
        list: saveApiData,
        modelId: modelId.value,
      }).then(() => {
        getData(modelId.value);
        DutyEvaluationService.quotaUpdate$.next(Math.random());
        resolve(true);
      });

      console.log('saveApiData::', saveApiData);
    });
  }

  function handleCancel() {
    isEditMode.value = false;
    dimensionData.value = cloneDeep(dimensionDataBak);
  }

  const subModelId$ = DutyEvaluationService.curModelId$.subscribe((id) => {
    isEditMode.value = false;
    dimensionData.value = [];
    modelId.value = id;
    getData(id);
  });

  onBeforeUnmount(() => {
    subModelId$.unsubscribe();
  });

  // 监听维度数据变化，更新表格数据
  watch(
    dimensionData,
    () => {
      // 强制更新表格数据
      tableData.value = QuotaService.flattenData(dimensionData);
    },
    { deep: true }
  );

  return {
    columns,
    getData,
    handleCancel,
    isEditMode,
    loading,
    modelId,
    reqData,
    rowKey,
    tableData,
    toggleEditMode,
  };
}
