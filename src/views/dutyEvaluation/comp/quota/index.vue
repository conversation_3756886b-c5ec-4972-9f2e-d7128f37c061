<template>
  <div :class="$style.DutyEvaluationQuotaIndex" class="com-g-row-a1">
    <ComTitle title="履职评估指标">
      <template #right>
        <n-space>
          <n-button v-if="isEditMode" type="tertiary" size="small" @click="handleCancel">取消</n-button>

          <n-button type="primary" size="small" @click="toggleEditMode" :disabled="loading">
            {{ isEditMode ? '保存' : tableData.length ? '编辑' : '添加' }}
          </n-button>
        </n-space>
      </template>
    </ComTitle>

    <n-data-table
      :columns="columns"
      :data="tableData"
      :row-key="rowKey"
      :bordered="true"
      :single-line="false"
      :striped="false"
      :loading="loading"
    />
  </div>
</template>

<script setup lang="ts">
import ComTitle from '@/components/title/ComTitle.vue';
import { NSpace } from 'naive-ui';
import { onBeforeUnmount } from 'vue';
import { QuotaService } from './QuotaService.ts';
import { useData } from './UseData.ts';

const { columns, getData, handleCancel, isEditMode, loading, modelId, rowKey, tableData, toggleEditMode } = useData();

// init
QuotaService.initData();

onBeforeUnmount(() => {
  QuotaService.clearCache();
});

defineExpose({
  refresh() {
    getData(modelId.value);
  },
});

defineOptions({ name: 'DutyEvaluationQuotaIndex' });
</script>

<style module lang="scss">
.DutyEvaluationQuotaIndex {
  background: #eef7ff;
  padding: 15px 20px 20px;
  row-gap: 15px;
}
</style>
