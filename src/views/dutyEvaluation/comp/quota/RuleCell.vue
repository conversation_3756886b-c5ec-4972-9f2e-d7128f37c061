<template>
  <div>
    <div v-if="indicator.pgfs === 1" style="color: #999; text-align: center">自定义</div>
    <n-space v-else align="center" justify="center" :vertical="false">
      <n-select
        v-if="isEditMode"
        class="!w-[80px]"
        v-model:value="pggz_symbol"
        :options="symbolOptions"
        style="width: 80px !important"
      />
      <n-input-number
        v-if="isEditMode"
        class="!w-[100px]"
        v-model:value="pggz_number"
        :show-button="false"
        :max="9999999999"
        :precision="2"
        :parse="parsePercentage"
        :format="formatPercentage"
        style="width: 80px !important"
      />
      <n-text v-if="!isEditMode">{{ indicator.pggz || '--' }}</n-text>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { NSpace, NSelect, NInputNumber, NText } from 'naive-ui';
import { QuotaService } from './QuotaService';
import { ref, watch } from 'vue';
import { isNull } from 'lodash-es';
import { formatPercentage, parseInteger, parsePercentage } from '@/utils/parse.ts';

const props = defineProps({
  standard: {
    type: Object,
    required: true,
  },
  indicator: {
    type: Object,
    required: true,
  },
  isEditMode: {
    type: Boolean,
    default: true,
  },
});

const emits = defineEmits(['updateSymbol', 'updateNumber']);

const pggz_symbol = ref(props.indicator.pggz_symbol || null);
const pggz_number = ref(props.indicator.pggz_number);

watch(
  () => pggz_symbol.value,
  (val) => {
    emits('updateSymbol', val || '');
  }
);

watch(
  () => pggz_number.value,
  (val) => {
    let _val = val;

    if (_val > 1) {
      pggz_number.value = parseInteger(_val);
    }

    emits('updateNumber', isNull(_val) ? '' : val);
  }
);

const symbolOptions = QuotaService.getRuleSymbolOptions();
</script>
