<!-- 评估维度 -->

<template>
  <n-space align="center" justify="center" :vertical="false" :size="[5, 0]">
    <n-input v-model:value="pgwd" maxlength="10" class="!w-[180px]" />
    <n-button v-if="isEditMode" quaternary circle type="default" size="small" @click="addDimension">
      <n-icon class="text-[18px]">
        <IconAdd />
      </n-icon>
    </n-button>
    <n-button
      v-if="isEditMode"
      quaternary
      circle
      type="error"
      size="small"
      @click="deleteDimension"
      :disabled="tableData.value.length <= 1"
    >
      <n-icon class="text-[18px]">
        <IconDel />
      </n-icon>
    </n-button>
  </n-space>
</template>

<script setup lang="ts">
import { NSpace, NInput, NButton } from 'naive-ui';
import { QuotaService } from './QuotaService';
import { ref, watch } from 'vue';
import { AkPlus as IconAdd, FlDelete as IconDel } from '@kalimahapps/vue-icons';

const props = defineProps({
  dimension: {
    type: Object,
    required: true,
  },
  dimensionIndex: {
    type: Number,
    required: true,
  },
  tableData: {
    type: Object,
    required: true,
  },
  isEditMode: {
    type: Boolean,
    default: true,
  },
});

const emits = defineEmits(['update']);

const pgwd = ref(props.dimension.pgwd || '');

watch(
  () => pgwd.value,
  (val) => {
    emits('update', val || '');
  }
);

const addDimension = () => {
  QuotaService.addNewDimension(props.tableData, props.dimensionIndex);
};

const deleteDimension = () => {
  QuotaService.deleteDimension(props.tableData, props.dimensionIndex);
};
</script>
