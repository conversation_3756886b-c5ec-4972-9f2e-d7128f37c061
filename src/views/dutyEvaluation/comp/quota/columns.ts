import { DataTableColumn, NEllipsis, NInputNumber, NText } from 'naive-ui';
import { h, Ref } from 'vue';
import DimensionCell from './DimensionCell.vue';
import StandardCell from './StandardCell.vue';
import IndicatorCell from './IndicatorCell.vue';
import RuleCell from './RuleCell.vue';
import EvaluationMethodCell from './EvaluationMethodCell.vue';
import { formatPercentage } from '@/utils/parse.ts';

export function genCols(tableData: Ref<any[]>, isEditMode: Ref<boolean>) {
  // 创建表格列
  const createColumns = () => {
    const columns: DataTableColumn[] = [
      {
        title: '评估维度',
        key: 'pgwd',
        width: 230,
        align: 'center',
        rowSpan: (rowData: any, rowIndex) => {
          // 计算当前维度下所有标准的所有指标数量
          const { dimension, standardIndex, indicatorIndex } = rowData;

          // 只在第一个标准的第一个指标行显示维度
          if (standardIndex === 0 && indicatorIndex === 0) {
            let totalRows = 0;
            dimension?.standards?.forEach((standard: any) => {
              totalRows += standard.indicators ? standard.indicators.length : 1;
            });
            return totalRows;
          }
          return 0;
        },
        render: (rowData: any) => {
          const { dimension, dimensionIndex, standardIndex, indicatorIndex } = rowData;

          // 只在第一个标准的第一个指标行显示维度
          if (standardIndex === 0 && indicatorIndex === 0) {
            if (isEditMode.value) {
              return h(DimensionCell, {
                dimension,
                dimensionIndex,
                tableData,
                isEditMode: isEditMode.value,
                onUpdate(val) {
                  rowData.dimension.pgwd = val;
                },
              });
            } else {
              return h(NText, {}, { default: () => dimension.pgwd || '--' });
            }
          }
          return null;
        },
      },
      {
        title: '总分值',
        key: 'zfz',
        width: 80,
        align: 'center',
        rowSpan: (rowData: any, rowIndex) => {
          // 与评估维度相同的合并逻辑
          const { dimension, standardIndex, indicatorIndex } = rowData;

          if (standardIndex === 0 && indicatorIndex === 0) {
            let totalRows = 0;
            dimension.standards.forEach((standard: any) => {
              totalRows += standard.indicators ? standard.indicators.length : 1;
            });
            return totalRows;
          }
          return 0;
        },
        render: (rowData: any) => {
          const { dimension, standardIndex, indicatorIndex } = rowData;

          if (standardIndex === 0 && indicatorIndex === 0) {
            if (isEditMode.value) {
              return h(NInputNumber, {
                value: dimension.zfz,
                placeholder: '请输数字',
                showButton: false,
                max: 999,
                min: 1,
                precision: 0,
                onUpdateValue: (val) => {
                  dimension.zfz = val;
                },
              });
            } else {
              return h(NText, {}, { default: () => dimension.zfz });
            }
          }
          return null;
        },
      },
      {
        title: '默认值',
        key: 'mrz',
        width: 80,
        align: 'center',
        rowSpan: (rowData: any, rowIndex) => {
          // 与评估维度相同的合并逻辑
          const { dimension, standardIndex, indicatorIndex } = rowData;

          if (standardIndex === 0 && indicatorIndex === 0) {
            let totalRows = 0;
            dimension.standards.forEach((standard: any) => {
              totalRows += standard.indicators ? standard.indicators.length : 1;
            });
            return totalRows;
          }
          return 0;
        },
        render: (rowData: any) => {
          const { dimension, standardIndex, indicatorIndex } = rowData;

          if (standardIndex === 0 && indicatorIndex === 0) {
            if (isEditMode.value) {
              return h(NInputNumber, {
                value: dimension.mrz,
                placeholder: '请输数字',
                showButton: false,
                min: 0,
                max: Number(dimension.zfz || 100) - 1,
                precision: 0,
                onUpdateValue: (val) => {
                  dimension.mrz = val;
                },
              });
            } else {
              return h(NText, {}, { default: () => dimension.mrz });
            }
          }
          return null;
        },
      },
      {
        title: '评估标准',
        key: 'pgbz',
        width: 230,
        align: 'center',
        rowSpan: (rowData: any, rowIndex) => {
          // 计算当前标准下所有指标数量
          const { standard, indicatorIndex } = rowData;

          // 只在标准的第一个指标行显示标准
          if (indicatorIndex === 0) {
            return standard.indicators ? standard.indicators.length : 1;
          }
          return 0;
        },
        render: (rowData: any) => {
          const { dimension, standard, standardIndex, indicatorIndex } = rowData;

          // 只在标准的第一个指标行显示标准
          if (indicatorIndex === 0) {
            if (isEditMode.value) {
              return h(StandardCell, {
                dimension,
                standard,
                standardIndex,
                isEditMode: isEditMode.value,
                onUpdate(val) {
                  rowData.standard.pgbz = val;
                },
              });
            } else {
              return h(
                NEllipsis,
                {
                  lineClamp: 1,
                  tooltip: { placement: 'bottom' },
                },
                { default: () => standard.pgbz || '--' }
              );
            }
          }
          return null;
        },
      },
      {
        title: '评估方式',
        key: 'pgfs',
        width: 120,
        align: 'center',
        render: (rowData: any) => {
          const { indicator } = rowData;

          if (!indicator) return null;

          return h(EvaluationMethodCell, {
            indicator,
            isEditMode: isEditMode.value,
            onUpdate(val) {
              rowData.indicator.pgfs = val;
            },
          });
        },
      },
      {
        title: '评估指标',
        key: 'pgzb',
        width: 230,
        align: 'center',
        render: (rowData: any) => {
          const { standard, indicator, indicatorIndex } = rowData;

          if (!standard || !indicator) return null;

          if (isEditMode.value) {
            return h(IndicatorCell, {
              standard,
              indicator,
              indicatorIndex,
              isEditMode: isEditMode.value,
              onUpdate(val) {
                rowData.indicator.pgzb = val;
              },
              onUpdatePgzbName(val) {
                rowData.indicator.pgzbName = val;
              },
            });
          } else {
            const str = indicator.pgfs === 0 ? indicator.pgzbName : indicator.pgzb;

            return h(
              NEllipsis,
              {
                lineClamp: 1,
                tooltip: { placement: 'bottom' },
              },
              { default: () => str || '--' }
            );
          }
        },
      },
      {
        title: '评估规则',
        key: 'pggz',
        width: 160,
        align: 'center',
        render: (rowData: any) => {
          const { standard, indicator } = rowData;

          if (!standard || !indicator) return null;

          // ‘0’ 处理
          if (indicator.pggz_symbol === '0') indicator.pggz_symbol = '';

          if (indicator.pggz_symbol) {
            indicator.pggz = `${indicator.pggz_symbol || ''}${indicator.pggz_number !== '' ? formatPercentage(indicator.pggz_number) : ''}`;
          } else {
            indicator.pggz = '';
            indicator.pggz_number = null;
          }

          return h(RuleCell, {
            standard,
            indicator,
            isEditMode: isEditMode.value,
            onUpdateSymbol(val) {
              rowData.indicator.pggz_symbol = val;
            },
            onUpdateNumber(val) {
              rowData.indicator.pggz_number = val;
            },
          });
        },
      },
      {
        title: '评估分值',
        key: 'pgfz',
        width: 80,
        align: 'center',
        render: (rowData: any) => {
          const { dimension, indicator } = rowData;

          if (!indicator) return null;

          if (isEditMode.value) {
            return h(NInputNumber, {
              value: indicator.pgfz,
              maxlength: 3,
              placeholder: '请输数字',
              showButton: false,
              min: -999,
              max: Number(dimension.zfz || 100),
              precision: 0,
              onUpdateValue: (value) => {
                // 确保评估分值不能大于总分值
                if (Number(value) > Number(dimension.zfz)) {
                  indicator.pgfz = dimension.zfz;
                } else {
                  indicator.pgfz = value;
                }
              },
            });
          } else {
            return h(NText, {}, { default: () => indicator.pgfz });
          }
        },
      },
    ];

    return columns;
  };

  return {
    columns: createColumns(),
    rowKey: (row: any) => row.key,
  };
}
