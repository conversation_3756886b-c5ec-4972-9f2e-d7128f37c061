import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';

// 列表
export function getList(query: IObj<any>) {
  const url = api.getUrl(api.type.server, api.name.server.getEvaluateIndexList, query);
  return $http.get<any[]>(url, { data: { _cfg: { showTip: true } } });
}

// 指标下拉
export function getIndicatorList() {
  const url = api.getUrl(api.type.server, api.name.server.getMetricList);
  return $http.get<any[]>(url, { data: { _cfg: { showTip: true } } });
}

// 保存
export function postSave(params: IObj<any>) {
  const url = api.getUrl(api.type.server, api.name.server.saveEvaluateIndex);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...params } });
}
