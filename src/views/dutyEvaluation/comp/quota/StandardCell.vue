<!-- 评估标准 -->

<template>
  <n-space align="center" justify="center" :vertical="false">
    <n-input v-model:value="pgbz" maxlength="50" class="!w-[180px]" />
    <n-button v-if="isEditMode" quaternary circle type="default" size="small" @click="addStandard">
      <n-icon class="text-[18px]">
        <IconAdd />
      </n-icon>
    </n-button>
    <n-button
      v-if="isEditMode"
      quaternary
      circle
      type="error"
      size="small"
      @click="deleteStandard"
      :disabled="dimension.standards && dimension.standards.length <= 1"
    >
      <n-icon class="text-[18px]">
        <IconDel />
      </n-icon>
    </n-button>
  </n-space>
</template>

<script setup lang="ts">
import { NSpace, NInput, NButton } from 'naive-ui';
import { QuotaService } from './QuotaService';
import { ref, watch } from 'vue';
import { AkPlus as IconAdd, FlDelete as IconDel } from '@kalimahapps/vue-icons';

const props = defineProps({
  dimension: {
    type: Object,
    required: true,
  },
  standard: {
    type: Object,
    required: true,
  },
  standardIndex: {
    type: Number,
    required: true,
  },
  isEditMode: {
    type: Boolean,
    default: true,
  },
});

const emits = defineEmits(['update']);

const pgbz = ref(props.standard.pgbz || '');

watch(
  () => pgbz.value,
  (val) => {
    emits('update', val || '');
  }
);

const addStandard = () => {
  QuotaService.addNewStandard(props.dimension, props.standardIndex);
};

const deleteStandard = () => {
  if (props.dimension.standards && props.dimension.standards.length > 1) {
    // 直接使用传入的 standardIndex 进行删除
    QuotaService.deleteStandard(props.dimension, props.standardIndex);
  }
};
</script>
