<!-- 评估指标 -->

<template>
  <n-space align="center" justify="center" :vertical="false">
    <n-input v-if="indicator.pgfs === 1" class="!w-[150px]" v-model:value="pgzb" maxlength="50" />
    <n-select v-else class="!w-[150px]" v-model:value="pgzb" :options="indicatorOptions" />
    <n-button
      v-if="isEditMode && standard.pgfs !== 1"
      quaternary
      circle
      type="default"
      size="small"
      @click="addIndicator"
    >
      <n-icon class="text-[18px]">
        <IconAdd />
      </n-icon>
    </n-button>
    <n-button
      v-if="isEditMode && standard.pgfs !== 1"
      quaternary
      circle
      type="error"
      size="small"
      @click="deleteIndicator"
      :disabled="standard.indicators && standard.indicators.length <= 1"
    >
      <n-icon class="text-[18px]">
        <IconDel />
      </n-icon>
    </n-button>
  </n-space>
</template>

<script setup lang="ts">
import { NSpace, NSelect, NButton } from 'naive-ui';
import { QuotaService } from './QuotaService';
import { ref, watch, watchEffect } from 'vue';
import { AkPlus as IconAdd, FlDelete as IconDel } from '@kalimahapps/vue-icons';

const props = defineProps({
  standard: {
    type: Object,
    required: true,
  },
  indicator: {
    type: Object,
    required: true,
  },
  indicatorIndex: {
    type: Number,
    required: true,
  },
  isEditMode: {
    type: Boolean,
    default: true,
  },
});

const emits = defineEmits(['update', 'updatePgzbName']);

// 用于切换时回退原始值
const bakData = {
  pgfs: props.indicator.pgfs,
  pgzb: props.indicator.pgzb,
};
const pgzb = ref(props.indicator.pgzb || null);

watch(
  () => pgzb.value,
  (val) => {
    if (props.indicator.pgfs === 1) {
      emits('update', val || '');
      emits('updatePgzbName', '');
    } else {
      emits('update', val || '');
      emits('updatePgzbName', getIndicatorText(val));
    }
  }
);

watch(
  () => props.indicator.pgfs,
  (val) => {
    // 处理切换
    if (val === bakData.pgfs) {
      pgzb.value = bakData.pgzb;
    } else {
      pgzb.value = val === 0 ? null : '';
    }

    if (bakData.pgfs === 0 && bakData.pgzb === '') {
      pgzb.value = null;
    }
  },
  { immediate: true }
);

const indicatorOptions = ref<any[]>([]);

QuotaService.getIndicatorOptions().then((res) => {
  indicatorOptions.value = res;
});

const addIndicator = () => {
  QuotaService.addNewIndicator(props.standard, props.indicatorIndex);
};

const deleteIndicator = () => {
  QuotaService.deleteIndicator(props.standard, props.indicatorIndex);
};

const getIndicatorText = (value: any) => {
  const opt = indicatorOptions.value.find((option) => option.value === value);
  return opt ? opt.label : '';
};
</script>
