<template>
  <n-select v-if="isEditMode" v-model:value="pgfs" :options="methodOptions" />
  <n-text v-else>{{ getMethodText(indicator.pgfs) }}</n-text>
</template>

<script setup lang="ts">
import { NSelect, NText } from 'naive-ui';
import { QuotaService } from './QuotaService';
import { ref, watch } from 'vue';

const props = defineProps({
  indicator: {
    type: Object,
    required: true,
  },
  isEditMode: {
    type: Boolean,
    default: true,
  },
});

const emits = defineEmits(['update']);

const pgfs = ref(props.indicator.pgfs || 0);
const bakData = {
  pgfs: props.indicator.pgfs,
};

watch(
  () => pgfs.value,
  (val) => {
    emits('update', val);
  },
  { immediate: true }
);

watch(
  () => props.isEditMode,
  (val) => {
    if (!val) {
      if (pgfs.value !== bakData.pgfs) {
        pgfs.value = bakData.pgfs;
      }
    }
  }
);

const methodOptions = QuotaService.getEvaluationMethodOptions();

const getMethodText = (value: number) => {
  const method = methodOptions.find((option) => option.value === value);
  return method ? method.label : '未知';
};
</script>
