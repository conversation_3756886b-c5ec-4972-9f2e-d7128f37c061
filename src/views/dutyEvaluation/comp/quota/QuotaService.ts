/**
 * 评估指标服务类
 */

import { getIndicatorList } from './fetchData.ts';

export class QuotaService {
  // 修改缓存相关的静态变量
  private static indicatorOptionsCache: { label: string; value: string }[] | null = null;

  static initData() {
    this.getIndicatorOptions();
  }

  // 添加新的评估维度
  static addNewDimension(tableData: any, index: number) {
    const newItem = {
      pgwd: '',
      zfz: null,
      mrz: null,
      key: Date.now(),
      standards: [
        {
          pgbz: '',
          indicators: [
            {
              pgzb: null,
              pgfs: 0,
              pggz_symbol: null,
              pggz_number: null,
              pggz: '',
              pgfz: null,
              key: Date.now() + 1,
            },
          ],
          key: Date.now() + 2,
        },
      ],
    };
    tableData.value.splice(index + 1, 0, newItem);
  }

  // 删除评估维度
  static deleteDimension(tableData: any, index: number) {
    if (tableData.value.length > 1) {
      tableData.value.splice(index, 1);
    }
  }

  // 添加新的评估标准
  static addNewStandard(row: any, standardIndex: number) {
    if (!row.standards) {
      row.standards = [];
    }

    const newStandard = {
      pgbz: '',
      pgfs: 0,
      indicators: [
        {
          // 评估指标数组
          pgzb: '',
          pggz_symbol: '>',
          pggz_number: null,
          pggz: '',
          pgfz: 0,
          key: Date.now(),
        },
      ],
      key: Date.now() + 1,
    };

    row.standards.splice(standardIndex + 1, 0, newStandard);
  }

  // 删除评估标准
  static deleteStandard(row: any, standardIndex: number) {
    if (row.standards && row.standards.length > 1) {
      row.standards.splice(standardIndex, 1);
    }
  }

  // 添加新的评估指标
  static addNewIndicator(standard: any, indicatorIndex: number) {
    if (!standard.indicators) {
      standard.indicators = [];
    }

    const newIndicator = {
      pgzb: '',
      pggz_symbol: '>',
      pggz_number: null,
      pggz: '',
      pgfz: null,
      key: Date.now(),
    };

    standard.indicators.splice(indicatorIndex + 1, 0, newIndicator);
  }

  // 删除评估指标
  static deleteIndicator(standard: any, indicatorIndex: number) {
    if (standard.indicators && standard.indicators.length > 1) {
      standard.indicators.splice(indicatorIndex, 1);
    }
  }

  // 将嵌套数据转换为扁平结构用于表格显示
  static flattenData(tableData: any) {
    const result: any[] = [];

    tableData.value.forEach((dimension: any) => {
      if (!dimension.standards || dimension.standards.length === 0) {
        // 如果没有评估标准，创建一个空行
        result.push({
          type: 'dimension',
          dimension,
          standard: null,
          indicator: null,
          dimensionIndex: tableData.value.indexOf(dimension),
          standardIndex: -1,
          indicatorIndex: -1,
          key: `${dimension.key}-empty`,
        });
      } else {
        // 为每个评估标准和指标创建行
        dimension.standards.forEach((standard: any, standardIndex: number) => {
          if (!standard.indicators || standard.indicators.length === 0) {
            // 如果标准没有指标，创建一个空行
            result.push({
              type: 'standard',
              dimension,
              standard,
              indicator: null,
              dimensionIndex: tableData.value.indexOf(dimension),
              standardIndex,
              indicatorIndex: -1,
              key: `${dimension.key}-${standard.key}-empty`,
            });
          } else {
            // 为每个指标创建一行
            standard.indicators.forEach((indicator: any, indicatorIndex: number) => {
              result.push({
                type: 'indicator',
                dimension,
                standard,
                indicator,
                dimensionIndex: tableData.value.indexOf(dimension),
                standardIndex,
                indicatorIndex,
                key: `${dimension.key}-${standard.key}-${indicator.key}`,
              });
            });
          }
        });
      }
    });

    return result;
  }

  // 获取评估指标选项
  static async getIndicatorOptions() {
    try {
      // 如果缓存存在，直接返回
      if (this.indicatorOptionsCache) {
        return this.indicatorOptionsCache;
      }

      const res = await getIndicatorList();
      if (res && res.data) {
        // 更新缓存
        this.indicatorOptionsCache = res.data.map((item: any) => ({
          label: item.metricName,
          value: item.metricCode,
        }));
        return this.indicatorOptionsCache;
      }
      return [];
    } catch (error) {
      console.error('获取评估指标选项失败:', error);
      return [];
    }
  }

  // 添加清除缓存的方法
  static clearCache() {
    this.indicatorOptionsCache = null;
  }

  // 获取评估规则符号选项
  static getRuleSymbolOptions() {
    return [
      { label: '>', value: '>' },
      { label: '<', value: '<' },
      { label: '=', value: '=' },
      { label: '>=', value: '>=' },
      { label: '<=', value: '<=' },
    ];
  }

  // 获取评估方式选项
  static getEvaluationMethodOptions() {
    return [
      { label: '动态评估', value: 0 },
      { label: '人工评估', value: 1 },
    ];
  }
}
