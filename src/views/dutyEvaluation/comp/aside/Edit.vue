<template>
  <div :class="$style.wrap">
    <n-form ref="formRef" :model="formData" :rules="rules" :show-label="false" require-mark-placement="left">
      <n-form-item path="modelName">
        <n-input v-model:value="formData.modelName" :placeholder="rules.modelName.message" maxlength="10" show-count />
      </n-form-item>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import type { IActionData } from '../../type';
import { computed, inject, ref, Ref } from 'vue';
import { postUpdate, postDelete } from '../model/fetchData';
import { PROVIDE_KEY } from '../../constant';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import { FormInst } from 'naive-ui';

const emits = defineEmits(['submitted']);
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const actionData = computed(() => currentAction.value.data);
const [loading, run] = useAutoLoading(true);

const formRef = ref<FormInst | null>();
const formData = ref({
  id: '',
  modelName: '',
});

const rules = {
  modelName: { required: true, message: '请输入模型名称', trigger: ['blur', 'input'] },
};

function handleSubmit() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      const params = {
        ...formData.value,
      };

      run(postUpdate(params)).then(() => {
        emits('submitted');
      });
    } else {
      $toast.error(rules.modelName.message);
    }
  });
}

function handleDelete() {
  const params = {
    id: formData.value.id,
  };

  run(postDelete(params)).then(() => {
    emits('submitted');
  });
}

function getData() {
  formData.value.id = actionData.value?.id;
  formData.value.modelName = actionData.value?.modelName;
}

// init
getData();

defineExpose({
  handleSubmit,
  handleDelete,
});

defineOptions({ name: 'DutyEvaluationEdit' });
</script>

<style module lang="scss">
.wrap {
  padding: 24px;
}

.checkbox-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  row-gap: 20px;
  padding: 0 10px;
  margin: 20px 0 30px;
  min-height: 107px;
}

.red {
  color: #a30014;
}
</style>
