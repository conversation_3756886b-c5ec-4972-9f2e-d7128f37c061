<template>
  <ComDrawerA
    :autoFocus="false"
    :footerPaddingBottom="25"
    :maskClosable="false"
    :show-action="true"
    @handle-negative="handleClose"
    @handle-positive="handleSubmit"
    positiveText="确定"
    class="!w-[430px]"
  >
    <Add ref="addRef" :id="id" v-if="isAdd" @submitted="handleSubmitted" />

    <Edit ref="editRef" v-if="isEdit" @submitted="handleSubmitted" />
    <template #footer v-if="isEdit">
      <n-flex justify="right" class="px-[24px]" :size="[20, 0]">
        <n-button type="tertiary" @click="handleClose">取消</n-button>
        <n-button type="error" @click="handleDelete">删除</n-button>
        <n-button type="primary" @click="handleSubmit">确定</n-button>
      </n-flex>
    </template>
  </ComDrawerA>
</template>

<script lang="ts" setup>
import ComDrawerA from '@/components/drawer/ComDrawerA.vue';
import Add from './Add.vue';
import Edit from './Edit.vue';
import type { IActionData } from '../../type';
import { ACTION, PROVIDE_KEY } from '../../constant';
import { computed, useAttrs, inject, Ref, ref } from 'vue';
const props = defineProps({ id: String });
const emits = defineEmits(['action']);
const attrs = useAttrs();
const show = computed(() => !!attrs.show);
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const isAdd = computed(() => currentAction.value.action === ACTION.ADD);
const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);
const addRef = ref();
const editRef = ref();

function handleSubmit() {
  if (isAdd.value) {
    addRef.value?.handleSubmit();
  }

  if (isEdit.value) {
    editRef.value?.handleSubmit();
  }
}

function handleSubmitted() {
  emits('action', { action: ACTION.REFRESH_MODEL });
  handleClose();
}

function handleDelete() {
  editRef.value?.handleDelete();
}

function handleClose() {
  emits('update:show' as any, false); // any-屏蔽组件内透传的emit类型
}

defineOptions({ name: 'DutyEvaluationAside' });
</script>

<style module lang="scss"></style>
