<template>
  <div :class="$style.DutyEvaluationObjItem">
    <img class="w-[48px] h-[48px] select-none" src="./assets/icon.png" alt="" />
    <div :class="$style.info">
      <p>{{ data.unitName }}</p>
      <p>
        已选：<span :class="$style.num">{{ data.userNum }}</span
        >人
      </p>
      <div ref="personRef">
        <n-ellipsis line-clamp="2" :tooltip="personElVisible ? { placement: 'bottom' } : false">
          {{ data.userNames }}
        </n-ellipsis>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useElementVisibility } from '@vueuse/core';
import { ref } from 'vue';
import { IUserDataCard } from './type.ts';

const props = defineProps({
  data: {
    type: Object as () => IUserDataCard,
    default: () => {},
  },
});

const personRef = ref();
const personElVisible = useElementVisibility(personRef);

defineOptions({ name: 'DutyEvaluationObjItem' });
</script>

<style module lang="scss">
.DutyEvaluationObjItem {
  display: grid;
  grid-template-columns: 48px 1fr;
  place-items: center;
  column-gap: 5px;
  min-width: 340px;
  height: 130px;
  padding: 15px;
  color: #333;
  background: linear-gradient(180deg, #ffffff 0%, #e5ecff 100%);
  border: 1px solid #fff;
  box-shadow: 0 2px 0 0 rgba(0, 20, 82, 0.18);
  border-radius: 8px;
  overflow: auto;
  transition: all 0.3s;

  &:hover {
    border: 1px solid #3e62eb;
  }

  .info {
    width: 100%;

    .num {
      font-size: 24px;
      color: #527cff;
      padding-right: 4px;
    }

    p {
      line-height: 1.8;
    }
  }
}
</style>
