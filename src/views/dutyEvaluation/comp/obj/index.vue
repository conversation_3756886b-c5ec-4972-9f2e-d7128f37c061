<template>
  <div :class="$style.DutyEvaluationObjIndex">
    <div :class="$style.content">
      <div class="com-g-row-a1">
        <ComTitle title="履职评估维度" />
        <div :class="$style.radar">
          <RadarChart
            v-if="radarData.length"
            style="width: 400px; height: 300px"
            :echartsData="echartsData"
            :indicator="indicator"
          />
          <com-empty v-else />
        </div>
      </div>
      <div class="com-g-row-a1">
        <ComTitle :title="`履职评估对象（${objData.totalNum || 0}人）`">
          <template #right>
            <n-button type="primary" size="small" @click="handleEdit">编辑</n-button>
          </template>
        </ComTitle>
        <n-scrollbar>
          <div v-if="userDataCardList.length" :class="$style.itemGroup">
            <Item v-for="item of userDataCardList" :data="item" :key="item.unitName" />
          </div>
          <com-empty class="com-empty" v-else />
        </n-scrollbar>
      </div>
    </div>

    <SelectUserMore2
      :unitId="unitId"
      :curTab="1"
      v-model:show="isShowUserSelectDia"
      :modelId="modelId"
      :userType="1"
      :custom-tabs="[{ label: '组织架构', value: 1 }]"
      :defaultCheckedIds="userIdList"
      :defaultCheckedUserInfo="userIdMap"
      title="选择人员"
      @close="isShowUserSelectDia = false"
      @success="handleUserSelect"
    />
  </div>
</template>

<script setup lang="ts">
import ComEmpty from '@/components/empty/index.vue';
import ComTitle from '@/components/title/ComTitle.vue';
import Item from './Item.vue';
import RadarChart from '@/components/echarts/radarChart.vue';
import type { IObjData, IRadar } from './type.ts';
import { computed, onBeforeUnmount, ref } from 'vue';
import { DutyEvaluationService } from '@/views/dutyEvaluation/DutyEvaluationService.ts';
import { getObjData, getRadarData, postSave } from './fetchData.ts';
import SelectUserMore2 from '@/components/select-user/SelectUserMore2.vue';
import { useStore } from '@/store';
import { ACTION } from '@/views/dutyEvaluation/constant.ts';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
const props = defineProps({ id: String });
const emits = defineEmits(['action']);
const store = useStore();

const modelId = ref('');
const unitId = computed(() => props.id);

const radarData = ref<IRadar[]>([]);
const echartsData = computed(() => {
  return [
    {
      name: '评估维度',
      value: radarData.value.map((item) => item.defaultScore),
    },
  ];
});
const indicator = computed(() => radarData.value.map((item) => ({ name: item.name, max: item.totalScore })));

const objData = ref<Partial<IObjData>>({});
const userDataCardList = computed(() => {
  return objData.value.userData || [];
});
const userIdMap = computed(() => objData.value.userMap || {});
const userIdList = computed(() => Object.keys(userIdMap.value));

const isShowUserSelectDia = ref(false);

function getData(modelId: string) {
  objData.value = {};
  getObjData({ modelId }).then((res) => {
    objData.value = res.data || {};
  });

  doGetRadarData(modelId);
}

function doGetRadarData(modelId: string) {
  radarData.value = [];
  getRadarData({ modelId }).then((res) => {
    radarData.value = res.data || [];
  });
}

function handleEdit() {
  if (!modelId.value) {
    return $toast.warning('请您先添加履职能力评估模型');
  }
  isShowUserSelectDia.value = true;
}

function handleUserSelect(dataMap: Record<string, any>) {
  let tmp: any[] = [];

  for (const [userId, val] of Object.entries(dataMap)) {
    const item = val || userIdMap.value[userId];

    tmp.push({
      userId: val ? item.id : item.userId,
      userName: item.userName,
      unitId: item.unitId,
      unitName: item.unitName,
    });
  }

  handleSave(tmp);
}

function handleSave(data: any) {
  const arr: any[] = [];

  for (const item of data) {
    arr.push({
      modelId: modelId.value,
      unitId: item.unitId,
      unitName: item.unitName,
      userId: item.userId,
      userName: item.userName,
    });
  }

  postSave(arr, modelId.value).then(() => {
    emits('action', {
      action: ACTION.REFRESH_OBJ,
    });
  });
}

const subModelId$ = DutyEvaluationService.curModelId$.subscribe((data) => {
  modelId.value = data;
  getData(data);
});

const subQuotaUpdate$ = DutyEvaluationService.quotaUpdate$.subscribe(() => {
  doGetRadarData(modelId.value);
});

onBeforeUnmount(() => {
  subModelId$.unsubscribe();
  subQuotaUpdate$.unsubscribe();
});

defineExpose({
  refresh() {
    getData(modelId.value);
  },
});

defineOptions({ name: 'DutyEvaluationObjIndex' });
</script>

<style module lang="scss">
.DutyEvaluationObjIndex {
  .content {
    display: grid;
    grid-template-columns: 500px 1fr;
    grid-template-rows: 370px;
    column-gap: 20px;

    > div {
      background: #eef7ff;
      border-radius: 6px;
      padding: 15px 20px 20px;
    }

    .radar {
      display: grid;
      place-content: center;
    }

    .itemGroup {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-auto-rows: 130px;
      gap: 20px;
      padding-top: 15px;
      padding-bottom: 5px;
    }
  }
}
</style>
