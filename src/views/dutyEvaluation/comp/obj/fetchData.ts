import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';
import { IRadar, IObjData } from './type.ts';

export function getRadarData(query: IObj<any>) {
  const url = api.getUrl(api.type.server, api.name.server.getModelRadarData, query);
  return $http.get<IRadar[]>(url, { data: { _cfg: { showTip: true } } });
}

export function getObjData(query: IObj<any>) {
  const url = api.getUrl(api.type.server, api.name.server.getEvaluateObjData, query);
  return $http.get<IObjData>(url, { data: { _cfg: { showTip: true } } });
}

// 保存
export function postSave(list: any[], modelId: string) {
  const url = api.getUrl(api.type.server, api.name.server.saveEvaluateObj);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true }, list, modelId } });
}
