<template>
  <div class="">
    <com-bread :data="breadData"></com-bread>
    <!-- h-[calc(100%-86px)] -->
    <div class="flex h-[calc(100%-20px)]">
      <transition name="slide-fade">
        <div class="h-[calc(100%+40px)]" v-show="formVisible">
          <com-tree :title="titlee" :dataList="treeData" @handelChange="handelChange"></com-tree>
        </div>
      </transition>
      <div class="!ml-[15px]" style="position: relative; width: 100%">
        <img
          @click="formVisible = !formVisible"
          src="@/assets/open.png"
          style="position: absolute; left: -1%; top: 50%; width: 30px; z-index: 1000; cursor: pointer"
        />
        <n-scrollbar style="height: calc(100vh - 135px)">
          <div class="com-g-row-aa1 gap-y-[20px]">
            <ModelIndex ref="modelIndexRef" @action="actionFn" :id="orgCode" />
            <ObjIndex ref="objIndexRef" :id="orgCode" @action="actionFn" />
            <QuotaIndex />
          </div>
        </n-scrollbar>
      </div>
    </div>

    <!-- aside -->
    <AsideComp v-model:show="isShowAside" :id="orgCode" :title="actionLabel" @action="actionFn" />
  </div>
</template>

<script lang="ts" setup>
import AsideComp from './comp/aside/index.vue';
import ModelIndex from './comp/model/index.vue';
import ObjIndex from './comp/obj/index.vue';
import QuotaIndex from './comp/quota/index.vue';
import { getOrgTrees, addPersonManage } from './fetchData';
import type { IActionData } from './type';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from './constant';
import { computed, provide, Ref, ref, onMounted } from 'vue';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import { fileDownloader } from '@/utils/fileDownloader';
import { useStore } from '@/store';
import comTree from '@/components/tree/comTree.vue';
const store = useStore();
const breadData: IBreadData[] = [{ name: '履职评估' }];
const titlee = ref('履职评估');
const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);
const isShowAside = ref(false);
const modelIndexRef = ref();
const objIndexRef = ref();
const formVisible = ref(true);
// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);
const treeId: any = ref(null);
const treeLevelCode: any = ref(null);
const orgCode: any = ref('');
//树结构点击后获取到的值
const handelChange = (v: any) => {
  // console.log(v, '>>>>');
  orgCode.value = v.id;
  // treeLevelCode.value = v.levelCode;
};
function actionFn(val: IActionData) {
  currentAction.value = val;

  if (val.action === ACTION.REFRESH_MODEL) {
    modelIndexRef.value?.getData();
  } else if (val.action === ACTION.REFRESH_OBJ) {
    objIndexRef.value?.refresh();
  } else {
    isShowAside.value = [ACTION.ADD, ACTION.EDIT].includes(val.action);
  }
}

const treeData = ref([]);
//获取树结构数据
function QueryOrgTrees() {
  const params = {
    orgCode: store.userInfo.unitId,
    needChildUnit: '1',
    needself: '1',
  };
  getOrgTrees(params).then((res: any) => {
    if (res.code != 200) return;
    const _RES: any = removeOrgTypeZero(removeEmptyChildren(res.data));
    treeData.value = _RES;
    // if (treeData.value && treeData.value.length > 0) {
    //   // treeId.value = _RES[0].id;
    //   let curKey = _RES[0].id as string;
    // }
  });
  orgCode.value = store.userInfo.unitId;
}
// 过滤部门数据 orgType: 0
function removeOrgTypeZero(nodes: any) {
  return nodes
    .filter((node: any) => node.attributes.orgType !== '0') // 过滤掉 orgType 为 "0" 的节点
    .map((node: any) => {
      if (node.children && node.children.length > 0) {
        // 递归处理子节点
        node.children = removeOrgTypeZero(node.children);
      }
      return node;
    });
}
// 递归函数，使用map生成新的数组
function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };

    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }

    return newItem; // 返回新对象
  });
}
onMounted(() => {
  QueryOrgTrees();
  // handleSearch();
});
defineOptions({ name: 'DutyEvaluationIndex' });
</script>

<style module lang="scss"></style>
