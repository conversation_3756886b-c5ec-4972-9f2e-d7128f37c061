export const enum PROVIDE_KEY {
  currentAction = 'currentAction',
}

export const enum ACTION {
  NONE = 'NONE',
  REFRESH_MODEL = 'REFRESH_MODEL',
  EDIT = 'EDIT',
  ADD = 'ADD',
  EDIT_OBJ = 'EDIT_OBJ',
  REFRESH_OBJ = 'REFRESH_OBJ',
  REFRESH_QUOTA = 'REFRESH_QUOTA',
}

export const ACTION_LABEL: { [key in ACTION]: string } = {
  [ACTION.NONE]: '',
  [ACTION.REFRESH_MODEL]: '刷新模型',
  [ACTION.EDIT]: '编辑模型',
  [ACTION.ADD]: '新建模型',
  [ACTION.EDIT_OBJ]: '编辑模型对象',
  [ACTION.REFRESH_OBJ]: '刷新模型对象',
  [ACTION.REFRESH_QUOTA]: '刷新模型指标',
};
