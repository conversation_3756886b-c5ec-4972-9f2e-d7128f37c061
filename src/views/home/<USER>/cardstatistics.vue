<template>
  <n-grid x-gap="12" :cols="cardInfo.length" style="gap: 0px 24px">
    <n-gi v-for="(item, index) in cardInfo" :key="index">
      <n-card :style="{ backgroundImage: `url(${index <= 1 ? bj1 : bj})`, backgroundSize: 'cover' }">
        <template #header>
          <div class="flex justify-between cursor-pointer" @click="jumpperson(index)">
            <div class="text-[#0F4298] font-bold text-[24px]">{{ item.personnelType }}</div>
            <div>
              <span class="font-bold text-[32px] text-[#0F4298]">{{ item.personnelCount }}</span>
              <span class="text-[14px] text-[#0F4298]">人</span>
            </div>
          </div>
        </template>
        <div class="flex justify-between">
          <view class="cursor-pointer" @click="jumpcertificate(index)">
            <span :class="[index <= 1 ? 'text-[#939393]' : 'text-[#7C4003]', 'text-[16px]']">证书已过期：</span>
            <span class="text-[red]">{{ item.certificateExpiredCount }}本</span>
          </view>
          <view v-if="item.unitCount > 0">
            <span class="text-[#939393] text-[16px]">单位数：</span>
            <span class="text-[#939393] text-[16px]">{{ item.unitCount }}家</span>
          </view>
        </div>
      </n-card>
    </n-gi>
    <!-- <n-gi>
      <n-card :style="{ backgroundImage: `url(${bj})`, backgroundSize: 'cover' }">
        <template #header>
          <div class="flex justify-between">
            <div>特种作业人员</div>
            <div>
              <span class="font-bold">328</span>
              <span class="text-[14px]">人</span>
            </div>
          </div>
        </template>
        <div class="flex justify-between">
          <view>
            <span class="text-[#939393]">证书已过期:</span>
            <span class="text-[red]">12本</span>
          </view>
          <view>
            <span class="text-[#939393]">单位数:</span>
            <span class="text-[#939393]">15家</span>
          </view>
        </div>
      </n-card>
    </n-gi>
    <n-gi>
      <n-card :style="{ backgroundImage: `url(${bj1})`, backgroundSize: 'cover' }">
        <template #header>
          <div class="flex justify-between">
            <div>承包商作业人员</div>
            <div>
              <span class="font-bold">328</span>
              <span class="text-[14px]">人</span>
            </div>
          </div>
        </template>
        <div class="flex justify-between">
          <view>
            <span class="text-[#939393]">证书已过期:</span>
            <span class="text-[red]">12本</span>
          </view>
          <view>
            <span class="text-[#939393]">承包商单位数:</span>
            <span class="text-[#939393]">15家</span>
          </view>
        </div>
      </n-card>
    </n-gi> -->
  </n-grid>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import bj from '../static/bj.png';
import bj1 from '../static/bj1.png';
import { checkSysPower, getpersonnelTypeStatistics } from '../fetchData';
import router from '@/router';
import { useStore } from '@/store';
const store = useStore();
const props: any = defineProps({
  unitInfo: { type: Object },
});

const params = ref({
  unitId: '',
});
const cardInfo = ref<any>([]);
function fetchData() {
  // 请求卡片统计接口
  getpersonnelTypeStatistics(params.value).then((res: any) => {
    if (res.code != '200') return;
    cardInfo.value = res.data;
  });
}
// 跳转人员管理
function jumpperson(index: number) {
  if (index > 1) {
    console.log('跳转承包商管理页面', index);

    checkSysPower({ sysCode: 'inter_web', userId: store.userInfo.id }).then((res: any) => {
      console.log(res, 'res');
      if (res.code != 'success') return;
      if (res.data) {
        if (window.location.hostname == 'agjp.tanzervas.com') {
          // 演示环境
          window.open(
            window.$SYS_CFG.apiBase +
              '/inter-manage/#/personnelResumeManagement?token=' +
              res.data.token +
              '&sysCode=inter_web'
          );
        } else {
          window.open(
            window.$SYS_CFG.apiBase +
              '/ehs-partner-mgr/#/personnelResumeManagement?token=' +
              res.data.token +
              '&sysCode=inter_web'
          );
        }
      }
    });
    // https://test-bw.gsafetycloud.com/ehs-partner-mgr/#/personnelResumeManagement
    // ehs - clnt - platform - service / login / checkSysPower
    // window.open(
    //   window.$SYS_CFG.apiBase +
    //     '/ehs-partner-mgr/#/personnelResumeManagement?token=' +
    //     sessionStorage.getItem('ehs-org-alloc-mgr-token')
    // );
  } else {
    console.log('跳转人员管理', index);
    router.push({
      path: '/personManage',
      query: {
        tab: index + 1,
      },
    });
  }
}
function jumpcertificate(index: number) {
  if (index > 1) {
    // window.open(window.$SYS_CFG.apiBase + '/#/contractManagement');
    // 什么都不做
    // window.open(
    //   window.$SYS_CFG.apiBase +
    //     '/ehs-partner-mgr/#/personnelResumeManagement?token=' +
    //     sessionStorage.getItem('ehs-org-alloc-mgr-token')
    // );
  } else {
    // console.log('跳转证书管理', index);
    router.push({
      path: '/certificateManagement',
      query: {
        certificateStatus: 2,
      },
    });
  }
}
watch(
  () => props.unitInfo,
  (newValue) => {
    params.value.unitId = newValue.id;
    fetchData();
  }
);

defineOptions({ name: 'CardStatistics' });
</script>
