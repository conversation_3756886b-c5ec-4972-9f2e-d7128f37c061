<!-- eslint-disable prettier/prettier -->
<!--
 * @Author: fang<PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-24 14:01:26
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2025-03-27 15:46:17
 * @FilePath: \ehs-org-alloc-mgr\src\views\home\comp\compliance.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <n-space vertical>
    <n-spin :show="loading">
      <ul class="flex flex-row justify-around">
        <li v-for="(item, index) in dataCard" :key="index" :style="{'background': item.bgColor}" class="flex flex-col items-center justify-center mt-[10px] w-[180px] h-[290px] rounded-[6px]">
          <div class="relative">
            <img class="w-[94px] h-[94px]" :src="item.src" />
            <div :style="{'color': item.color}" class="absolute top-[0] left-[0] z-[99] w-[94px] h-[94px] leading-[94px] text-center text-[22px]">{{ item.rate }}</div>
          </div>
          <div class="pt-[34px]">
            <div class="text-[16px] text-[#000] font-bold">{{ item.lable }}</div>
            <div :style="{'color': item.color}" class="text-[14px] text-center"><span class="text-[28px] font-bold">{{ item.value }}</span> 个</div>
          </div>
        </li>
      </ul>
    </n-spin>
  </n-space>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, watchEffect, nextTick } from 'vue';
import { statistics } from '../fetchData';
import youxiu from '../static/youxiu.png';
import jige from '../static/jige.png';
import weixian from '../static/weixian.png';
const props: any = defineProps({
  unitInfo: { type: Object },
  unitType: { type: String },
});
const loading = ref(false);
const unitIds = ref('');
const dataCard = ref([
  { lable: '正常', value: '0', rate: '0%', bgColor: '#EDFAF6', color: '#00B578', src: youxiu },
  { lable: '即将过期', value: '0', rate: '0%', bgColor: '#FCF5EB', color: '#FF9500', src: jige },
  { lable: '已过期', value: '0', rate: '0%', bgColor: '#FCEDED', color: '#FF3E3E', src: weixian },
]);

const getTabData = async () => {
  loading.value = true;
  const params = {
    unitId: unitIds.value,
    isDownUnit: props.unitType,
    status: 0,
  };
  try {
    const res: any = await statistics(params);
    loading.value = false;
    if (res.code != 200) return;
    const _Data: any = res.data;
    if (_Data.length === 0) {
      return;
    } else {
      for (let index = 0; index < _Data.length; index++) {
        const element = _Data[index];
        if (element.certificateStatusName == '正常') {
          dataCard.value[0].value = element.certificateStatusCount;
          dataCard.value[0].rate = element.percentage + '%';
        }
        if (element.certificateStatusName == '即将过期') {
          dataCard.value[1].value = element.certificateStatusCount;
          dataCard.value[1].rate = element.percentage + '%';
        }
        if (element.certificateStatusName == '已过期') {
          dataCard.value[2].value = element.certificateStatusCount;
          dataCard.value[2].rate = element.percentage + '%';
        }
      }
    }
  } catch (error) {
    loading.value = false;
  }
};

watch(
  () => props.unitInfo,
  (newValue) => {
    unitIds.value = newValue.id;
    init();
  }
);

const init = () => {
  dataCard.value = [
    { lable: '正常', value: '0', rate: '0%', bgColor: '#EDFAF6', color: '#00B578', src: youxiu },
    { lable: '即将过期', value: '0', rate: '0%', bgColor: '#FCF5EB', color: '#FF9500', src: jige },
    { lable: '已过期', value: '0', rate: '0%', bgColor: '#FCEDED', color: '#FF3E3E', src: weixian },
  ];
  getTabData();
};

defineOptions({ name: 'ComplianceListComponent' }); // 修改组件名称为多单词形式
defineExpose({ init });
</script>
<style scoped lang="scss"></style>
