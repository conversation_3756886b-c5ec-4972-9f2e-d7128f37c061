<!-- eslint-disable prettier/prettier -->
<!--
 * @Author: fang<PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-24 14:01:26
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2025-03-25 20:29:16
 * @FilePath: \ehs-org-alloc-mgr\src\views\home\comp\compliance.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <n-data-table :min-height="240" :max-height="240" :loading="loading" :columns="columns" :data="tableData" :pagination="false" :bordered="false" />
</template>

<script setup lang="ts">
import { ref, onMounted, watch, watchEffect, nextTick } from 'vue';
import { certificateComplianceList } from '../fetchData';
const props: any = defineProps({
  unitInfo: { type: Object },
  unitType: { type: String },
  cardType: { type: String },
});
const loading = ref(false);
const isDownUnitShow = ref(false); // 是否显示业务单位 false是  true否
isDownUnitShow.value = props.unitInfo.orgType == '1' ? false : true;

const tableData = ref<any[]>([]);
const columns = ref<any[]>([
  {
    title: '单位名称',
    key: 'unitName',
    ellipsis: {
      tooltip: true,
    },
    width: 180,
  },
  { title: '持证人数', key: 'certificateHoldCount', width: 80 },
  { title: '证书数量', key: 'certificateCount', width: 80 },
  {
    title: '持证占比',
    key: 'certificateHoldRate',
    width: 80,
    render: (row: any) => {
      return row.certificateHoldRate ? row.certificateHoldRate + '%' : '--';
    },
  },
]);

async function getTabData(id?: any) {
  if (props.unitType == '1') {
    columns.value[0] = {
      title: '单位名称',
      key: 'unitName',
      ellipsis: {
        tooltip: true,
      },
      width: 180,
    };
  } else {
    columns.value[0] = {
      title: '部门名称',
      key: 'unitName',
      ellipsis: {
        tooltip: true,
      },
      width: 180,
    };
  }
  const params = {
    pageNo: 1,
    pageSize: -1,
    unitId: id ? id : props.unitInfo.id,
    isDownUnit: props.unitType,
    certificateType: props.cardType ? props.cardType : 1,
  };
  loading.value = true;
  try {
    const res: any = await certificateComplianceList(params);
    loading.value = false;
    if (res.code != 200) return;
    const _Data: any = res.data.rows;
    if (_Data.length === 0) {
      tableData.value = [];
      return;
    }
    tableData.value = _Data || [];
  } catch (error) {
    loading.value = false;
    tableData.value = [];
  }
}

watch(
  () => props.unitInfo,
  (newValue) => {
    if (newValue.orgType != '1') {
      isDownUnitShow.value = true;
    } else {
      isDownUnitShow.value = false;
    }
    init(newValue.id);
  }
);

const init = (id?: any) => {
  if (isDownUnitShow.value) {
    getTabData(id);
  }
};

defineOptions({ name: 'ComtypeTableComponent' }); // 修改组件名称为多单词形式
defineExpose({ init });
</script>
<style scoped lang="scss">
.n-radio-group .n-radio-button {
  color: #527cff !important;
}

.n-radio-group .n-radio-button.n-radio-button--checked {
  background-color: #527cff !important;
  color: white !important;
}
</style>
