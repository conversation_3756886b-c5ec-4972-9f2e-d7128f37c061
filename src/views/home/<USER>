import type { IDetail, IPageDataRes, PageModel, OrgTree } from './type';
import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';
// 获取树形结构
export function getOrgTrees(query: IObj<any>) {
  return $http.get<OrgTree>(api.getUrl(api.type.server, api.name.interface.getTreeData, query));
}
// 卡片统计部分
export function getpersonnelTypeStatistics(query: IObj<any>) {
  return $http.get<any>(api.getUrl(api.type.server, api.name.interface.personnelTypeStatistics, query));
}

// 人员统计分析-进度条
export function personnelStatistics(query: IObj<any>) {
  return $http.post<any>(api.getUrl(api.type.server, api.name.interface.personnelStatistics, query));
}
// 人员统计分析-列表
export function personnelStatisticsList(query: IObj<any>) {
  return $http.post<any>(api.getUrl(api.type.server, api.name.interface.personnelStatisticsList, query));
}
// 岗位统计分析-饼图
export function postStatistics(query: IObj<any>) {
  return $http.post<any>(api.getUrl(api.type.server, api.name.interface.postStatistics, query));
}
// 岗位统计分析-列表
export function postStatisticsList(query: IObj<any>) {
  return $http.post<any>(api.getUrl(api.type.server, api.name.interface.postStatisticsList, query));
}

// 获取新token
export function checkSysPower(query: IObj<any>) {
  // console.log(query, 'query');
  // return $http.post<any>(api.getUrl(api.type.platform, 'login/checkSysPower', query), query);
  const url = api.getUrl(api.type.platform, 'login/checkSysPower');
  return $http.post<any>(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...query },
  });
}

// 证书状态分布列表明细
export function certificateList(query: IObj<any>) {
  return $http.post<any>(api.getUrl(api.type.server, '/comprehensive/analysis/certificateStatus/list', query));
}
// 证书状态分布-饼图
export function statistics(query: IObj<any>) {
  return $http.post<any>(api.getUrl(api.type.server, '/comprehensive/analysis/certificateStatus/statistics', query));
}

// 证书类型
export function getCertificateType(query) {
  return $http.get<any>(api.getUrl(api.type.server, '/comprehensive/analysis/getCertificateTypeList', query));
}
// 证书类型分布列表明细
export function certificateComplianceList(query: IObj<any>) {
  return $http.post<any>(api.getUrl(api.type.server, '/comprehensive/analysis/certificateCompliance/list', query));
}
// 证书状态分布列表 -业务
export function detailsComplianceList(query: IObj<any>) {
  return $http.post<any>(api.getUrl(api.type.server, '/comprehensive/analysis/certificateStatus/details', query));
}
// 证书类型分布-饼图
export function certificateCompliance(query: IObj<any>) {
  return $http.post<any>(
    api.getUrl(api.type.server, '/comprehensive/analysis/certificateCompliance/statistics', query)
  );
}
// 获取证书类型
// export function getCertificateTypeList(query: IObj<any>) {
//   return $http.post<any>(api.getUrl(api.type.server, 'certificate/type/getCertificateTypeList', query));
// }
