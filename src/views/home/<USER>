<!-- eslint-disable prettier/prettier -->
<template>
  <div class="">
    <com-bread :data="breadData"></com-bread>
    <div class="flex h-full overflow-hidden">
      <transition name="slide-fade">
        <div class="h-full" v-show="formVisible">
          <comTree class="tree" @handelChange="treeChange" :dataList="treeData"></comTree>
        </div>
      </transition>
      <div class="!ml-[15px] w-full relative">
        <img @click="formVisible = !formVisible" src="@/assets/open.png"
          class="absolute left-[-1%] top-[50%] w-[30px] cursor-pointer z-[99]" />
        <div class="w-full overflow-auto h-full">
          <!-- 统计 -->
          <div class="rounded-[6px]">
            <Cardstatistics :unitInfo="unitInfo"></Cardstatistics>
          </div>
          <!-- 人员统计分析 -->
          <div v-show="unitInfo?.orgType != '1'"
            class="bg-[#eef7ff] rounded-[6px] min-h-[446px] mt-[20px] px-[24px] py-[15px]">
            <personStatistics :unitInfo="unitInfo"></personStatistics>
          </div>
          <!-- 证书合规分析 -->
          <div class="bg-[#eef7ff] rounded-[6px] min-h-[466px] mt-[20px] px-[24px] py-[15px]">
            <compliance :unitInfo="unitInfo" />
          </div>
          <!-- 岗位统计分析 -->
          <div class="bg-[#eef7ff] rounded-[6px] min-h-[466px] mt-[20px] px-[24px] py-[15px]">
            <stationStatistics :unitInfo="unitInfo"></stationStatistics>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import ComBread from '@/components/breadcrumb/ComBread.vue';
import comTree from '@/components/tree/comTree.vue';
import Cardstatistics from './comp/cardstatistics.vue';
import personStatistics from './comp/personStatistics.vue';
import stationStatistics from './comp/stationStatistics.vue';
import compliance from './comp/compliance.vue';
import { useStore } from '@/store/index';
import { computed, onMounted, provide, Ref, ref } from 'vue';
import Content from './content.vue';
import { getOrgTrees } from './fetchData';
const breadData: any[] = [{ name: '首页' }];
const store = useStore();
const formVisible = ref(true);
// 组织树结构
const treeData = ref([]);

// 获取组织树
function QueryOrgTrees() {
  const params = {
    orgCode: store.userInfo.unitId,
    needChildUnit: '1',
    needself: '1',
  };
  getOrgTrees(params).then((res: any) => {
    if (res.code != 200) return;
    const _RES: any = removeEmptyChildren(filterDepartments(res.data));
    treeData.value = _RES;
    // }
  });
}
function filterDepartments(data) {
  function filterNodes(nodes) {
    return nodes.filter((node) => {
      console.log(node.attributes, 'node');
      if (node.attributes && node.attributes.orgType === '0') {
        return false;
      }
      if (node.children) {
        node.children = filterNodes(node.children);
      }
      return true;
    });
  }

  return filterNodes(data);
}
// 递归函数，使用map生成新的数组
function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };

    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }

    return newItem; // 返回新对象
  });
}

onMounted(() => {
  QueryOrgTrees();
  // handleSearch();
});
const unitInfo = ref({});
// 点击数获取数据
const treeChange = (v: any) => {
  console.log(v, '=======v');
  unitInfo.value = v;
};

defineOptions({ name: 'HomeIndex' });
</script>

<style scoped lang="scss">
/* 定制滚动条整体 */
::-webkit-scrollbar {
  width: 4px;
  /* 宽度 */
}

/* 定制滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: transparent;
  /* 轨道颜色 */
}

/* 定制滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgba(0, 0, 0, 0.2);
}

/* 滑块在鼠标悬停时改变颜色 */
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.2);
}

::v-deep {
  .n-data-table .n-data-table-th {
    background-color: #bbccf3;
  }

  .n-data-table .n-data-table-tr.n-data-table-tr--striped .n-data-table-td {
    background-color: #f5f7fa;
  }
}
</style>
