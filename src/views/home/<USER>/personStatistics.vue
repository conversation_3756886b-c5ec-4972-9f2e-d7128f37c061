<template>
  <n-card style="background-color: #eef7ff">
    <template #header>
      <div class="flex justify-between">
        <div class="flex flex-row items-center">
          <div
            class="w-[4px] h-[16px] bg-[#527CFF] rounded-[4px] mr-[10px]"
          ></div>
          <div class="text-[16px] text-[#222222] font-bold">人员统计分析</div>
        </div>
        <div v-if="isDownUnitShow">
          <n-radio-group
            v-model:value="unitType"
            name="radiobuttongroup1"
            :on-update:value="unitTypeChange"
          >
            <n-radio-button
              v-for="song in unitlist"
              :key="song.value"
              :value="song.value"
              :label="song.label"
            />
          </n-radio-group>
        </div>
      </div>
    </template>
    <n-grid
      x-gap="12"
      :cols="2"
      class="h-[380px] mt-[16px] mb-[13px]"
      style="gap: 0px 24px"
    >
      <n-gi class="bg-white p-5 pb-[4px] border border-[#DFE6F2] rounded-md">
        <div style="width: 100%">
          <n-radio-group
            v-model:value="personType"
            name="radiobuttongroup2"
            :on-update:value="personTypeChange"
          >
            <n-radio-button
              v-for="song in personList"
              :key="song.value"
              :value="song.value"
              :label="song.label"
            />
          </n-radio-group>
        </div>
        <n-space vertical>
          <n-spin :show="loadingPross">
            <div
              class="flex flex-col h-[300px] rounded-md overflow-y-auto mt-4"
            >
              <div v-if="progressList.length > 0">
                <div
                  class="mb-2"
                  v-for="(item, index) in progressList"
                  :key="index"
                >
                  <div
                    :class="[
                      'flex',
                      'justify-between',
                      index > 0 ? 'mt-4' : '',
                    ]"
                  >
                    <div>{{ item.unitName }}</div>
                    <div>
                      {{ item.personnelCount }}（{{ item.percentage }}%）
                    </div>
                  </div>
                  <n-progress
                    type="line"
                    status="info"
                    :percentage="item.percentage"
                    :show-indicator="false"
                    color="#6DA2FC"
                    rail-color="#DCE4F4"
                  />
                </div>
              </div>
              <div
                v-else
                style="
                  display: flex;
                  align-items: center;
                  flex-direction: column;
                  height: 300px;
                  color: gray;
                  justify-content: center;
                "
              >
                <img src="../static/noData.png" />
                <p style="margin-left: 20px">暂无数据</p>
              </div>
            </div>
          </n-spin>
        </n-space>
        <!-- <div ref="chartContainer" id="main" style="width: 100%; height: 300px"></div> -->
      </n-gi>
      <n-gi class="bg-white p-5 pb-[4px] border border-[#DFE6F2] rounded-md">
        <n-data-table
          :min-height="288"
          :max-height="288"
          :columns="columns"
          :loading="loading"
          :data="data"
          :pagination="false"
          :bordered="false"
        />
      </n-gi>
    </n-grid>
  </n-card>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, watchEffect, nextTick } from 'vue';
import { useNaivePagination } from '@/common/hooks/useNaivePagination';
import { personnelStatistics, personnelStatisticsList } from '../fetchData';

const props: any = defineProps({
  unitInfo: { type: Object },
});
const { pagination, updateTotal } = useNaivePagination(getTabData);
pagination.pageSize = 5;
const loading = ref(false);
const loadingPross = ref(false);
// 图表参数
const chartOption = ref<any>({
  unitId: '',
  personnelType: '1',
  isDownUnit: '1',
});
const isDownUnitShow = ref(false);
const params = ref({
  unitId: '',
  isDownUnit: '1',
  pageNo: 1,
  pageSize: -1,
});
// 进度条数据
const progressList = ref<any[]>([]);
// 表格数据
const data = ref<any[]>([]);
const columns = ref<any>([
  {
    title: '单位名称',
    key: 'unitName',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '安全管理人员',
    key: 'safeManageCount',
    width: 130,
  },
  {
    title: '特种作业人员',
    key: 'specialJobCount',
    width: 130,
  },
]);
const columns1 = ref<any>([
  {
    title: '单位名称',
    key: 'unitName',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '安全管理人员',
    key: 'safeManageCount',
    width: 130,
  },
  {
    title: '特种作业人员',
    key: 'specialJobCount',
    width: 130,
  },
  {
    title: '承包商作业人员',
    key: 'contractorJobCount',
    width: 130,
  },
]);

// 单位类型
const unitType = ref('1');
const unitlist = [
  { value: '1', label: '下级单位' },
  { value: '0', label: '本级部门' },
];
// 人员类型
const personType = ref('1');
const personList = ref<any[]>([
  { value: '1', label: '安全管理人员' },
  { value: '2', label: '特种作业人员' },
  { value: '3', label: '承包商作业人员' },
]);

// 类型切换事件
const unitTypeChange = (val: string) => {
  unitType.value = val;
  params.value.pageNo = 1;
  params.value.isDownUnit = val;
  chartOption.value.isDownUnit = val;
  nextTick(() => {
    fetchData();
    getTabData();
  });
};

const personTypeChange = (val: string) => {
  personType.value = val;
  chartOption.value.personnelType = val;
  fetchData();
};
watch(
  () => props.unitInfo,
  (newValue) => {
    chartOption.value.unitId = newValue.id;
    params.value.unitId = newValue.id;
    unitType.value = '1';
    // console.log('获取数据参数--------------:', props.unitInfo);
    if (newValue.orgType != '1') {
      params.value.isDownUnit = '1';
      chartOption.value.isDownUnit = '1';

      isDownUnitShow.value = true;
    } else {
      // unitType.value = '0';
      // params.value.isDownUnit = '0';
      isDownUnitShow.value = false;
    }
    nextTick(() => {
      fetchData();
      getTabData();
    });
  }
);
// 分页数据获取
function getTabData() {
  // 在这里编写获取数据的逻辑
  try {
    if (unitType.value == '1') {
      columns.value = [
        {
          title: '单位名称',
          key: 'unitName',
          ellipsis: {
            tooltip: true,
          },
        },
        {
          title: '安全管理人员',
          key: 'safeManageCount',
          width: 130,
        },
        {
          title: '特种作业人员',
          key: 'specialJobCount',
          width: 130,
        },
        {
          title: '承包商作业人员',
          key: 'contractorJobCount',
          width: 130,
        },
      ];
    } else {
      columns.value = [
        {
          title: '部门名称',
          key: 'unitName',
          ellipsis: {
            tooltip: true,
          },
        },
        {
          title: '安全管理人员',
          key: 'safeManageCount',
          width: 130,
        },
        {
          title: '特种作业人员',
          key: 'specialJobCount',
        },
      ];
    }
    loading.value = true;
    personnelStatisticsList(params.value)
      .then((res: any) => {
        // console.log('获取数据:', res);
        loading.value = false;
        if (res.code != '200') return;
        data.value = res.data.rows || [];
        updateTotal(res.data.total || 0);
      })
      .catch(() => {
        loading.value = false;
        data.value = [];
      });
  } catch (error) {
    loading.value = false;
  }
}

// 进度条统计数据获取
const fetchData = () => {
  try {
    loadingPross.value = true;
    personnelStatistics(chartOption.value)
      .then((res: any) => {
        console.log('获取数据:', res);
        if (res.code != '200') return;
        progressList.value = res.data;
      })
      .catch(() => {
        progressList.value = [];
      })
      .finally(() => {
        loadingPross.value = false;
      });
  } catch (error) {
    loadingPross.value = false;
  }
  // console.log('获取数据参数:', chartOption.value);
};

defineOptions({ name: 'personStatistics' });
</script>
<style scoped lang="scss">
.active {
  background-color: #527cff !important;
  border-radius: 4px !important;
  color: white !important;
}

.n-radio-group .n-radio-button {
  // border: #527cff 1px solid !important;
  color: #527cff !important;
}

.n-radio-group .n-radio-button.n-radio-button--checked {
  background-color: #527cff !important;
  color: white !important;
  border: 0px;
}

.n-card.n-card--bordered {
  border: 0px !important;
}

::v-deep .n-card-header {
  padding: 0px !important;
}

::v-deep .n-card__content {
  padding: 0px !important;
}

.header {
  position: relative;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    border-radius: 4px;
    background-color: #527cff;
    position: absolute;
    top: 14%;
    left: 0;
  }
}

/* 定制滚动条整体 */
::-webkit-scrollbar {
  width: 4px;
  /* 宽度 */
}

/* 定制滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: transparent;
  /* 轨道颜色 */
}

/* 定制滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgba(0, 0, 0, 0.2);
}

/* 滑块在鼠标悬停时改变颜色 */
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.2);
}
</style>
