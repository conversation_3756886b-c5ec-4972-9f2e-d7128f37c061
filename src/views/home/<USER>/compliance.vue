<!-- eslint-disable prettier/prettier -->
<!--
 * @Author: fang<PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-24 14:01:26
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2025-03-27 19:43:32
 * @FilePath: \ehs-org-alloc-mgr\src\views\home\comp\compliance.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="flex justify-between">
    <div class="flex flex-row items-center">
      <div class="w-[4px] h-[16px] bg-[#527CFF] rounded-[4px] mr-[10px]"></div>
      <div class="text-[16px] text-[#222222] font-bold">证书合规分析</div>
    </div>
    <n-radio-group v-model:value="unitType" name="radiobuttongroupa" :on-update:value="unitTypeChange"
      v-if="isDownUnitShow">
      <n-radio-button v-for="song in unitlist" :key="song.value" :value="song.value" :label="song.label" />
    </n-radio-group>
  </div>
  <!-- contont -->
  <div class="flex w-full h-full mt-[16px]">
    <!-- 证书类型分布 -->
    <div class="w-[49%] mr-[24px] bg-[#fff] rounded-[6px] px-[20px] py-[11px]">
      <div class="flex flex-row items-center">
        <img class="w-[17px] h-[12px] mr-[8px]" src="@/components/header/assets/icon-title-arrow3.png" />
        <div class="text-[16px] text-[#222222] w-full font-bold flex justify-between">
          <div class="flex flex-row items-center h-[34px]">
            <span>证书类型分布</span>
            <n-select v-if="activeType == 2" class="!w-[192px] pl-[19px]" filterable v-model:value="cardType" placeholder="请选择"
              label-field="name" value-field="id" :options="optionsType" :on-update:value="cardTypeChange" />
          </div>
          <div class="flex flex-row items-center" v-if="isDownUnitShow">
            <img class="w-[20px] h-[20px] ml-[7px] cursor-pointer" @click="changeActiveType(1)" v-if="activeType == 1"
              src="../static/iconac.png" />
            <img class="w-[20px] h-[20px] ml-[7px] cursor-pointer" @click="changeActiveType(1)" v-else
              src="../static/icon.png" />
            <img class="w-[20px] h-[20px] ml-[7px] cursor-pointer" @click="changeActiveType(2)" v-if="activeType == 2"
              src="../static/iconsac.png" />
            <img class="w-[20px] h-[20px] ml-[7px] cursor-pointer" @click="changeActiveType(2)" v-else
              src="../static/icons.png" />
          </div>
        </div>
      </div>
      <div class="h-[325px] pt-[20px] relative">
        <!-- 证书类型分布 图表 -->
        <div v-show="activeType == 1">
          <PieChartComponent title="证书类型分布" :chartData="genderDistributionData" />
          <div v-if="chartDate.length == 0"
            class="w-full bg-[#fff] h-[325px] absolute top-0 left-0 flex items-center flex-col justify-center">
            <img src="../static/noData.png" />
            <p class="text-[#999]">暂无数据</p>
          </div>
        </div>
        <!-- 证书状态分布 table -->
        <comtype-table v-if="activeType == 2" :unitInfo="unitInfo" :unitType="unitType" :cardType="cardType"
          ref="comType" />
      </div>
    </div>
    <!-- 证书状态分布 -->
    <div class="flex-1 bg-[#fff] rounded-[6px] px-[20px] py-[11px]">
      <div class="flex flex-row items-center">
        <img class="w-[17px] h-[12px] mr-[8px]" src="@/components/header/assets/icon-title-arrow3.png" />
        <div class="text-[16px] text-[#222222] w-full font-bold flex justify-between">
          <div class="flex flex-row items-center h-[34px]">
            <span>证书状态分布</span>
            <n-select v-if="active == 2" class="!w-[192px] pl-[19px]" filterable v-model:value="cardState" placeholder="请选择"
              :options="options" :on-update:value="cardStateChange" />
          </div>
          <div class="flex flex-row items-center">
            <img class="w-[20px] h-[20px] ml-[7px] cursor-pointer" @click="changeAC(1)" v-if="active == 1"
              src="../static/iconac.png" />
            <img class="w-[20px] h-[20px] ml-[7px] cursor-pointer" @click="changeAC(1)" v-else
              src="../static/icon.png" />
            <img class="w-[20px] h-[20px] ml-[7px] cursor-pointer" @click="changeAC(2)" v-if="active == 2"
              src="../static/iconsac.png" />
            <img class="w-[20px] h-[20px] ml-[7px] cursor-pointer" @click="changeAC(2)" v-else
              src="../static/icons.png" />
          </div>
        </div>
      </div>
      <div class="h-[325px] pt-[20px]">
        <!-- 证书状态分布 列表 -->
        <compliance-list v-if="active == 1" :unitInfo="unitInfo" :unitType="unitType" ref="comList" />
        <!-- 证书状态分布 table -->
        <compliance-table v-if="active == 2" :unitInfo="unitInfo" :cardState="cardState" :unitType="unitType"
          ref="comTable" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, computed } from 'vue';
import * as echarts from 'echarts'; // 正确引入 ECharts
import complianceTable from './complianceTable.vue';
import comtypeTable from './comtypeTable.vue';
import complianceList from './complianceList.vue';
import { certificateCompliance, getCertificateType } from '../fetchData';
import PieChartComponent from './pieComponent.vue';
import { genderColorConfig } from './colorList';

const comTable = ref();
const comList = ref();
const comType = ref();
const props: any = defineProps({
  unitInfo: { type: Object },
});
const isDownUnitShow = ref(false);
// 单位类型
const unitType = ref('1');
const unitlist = [
  { value: '1', label: '下级单位' },
  { value: '0', label: '本级部门' },
];
// 证书类型
const activeType = ref(1); // 证书类型分布 1图 2table
const cardType = ref<any>('');
const chartDate = ref<any[]>([]);
const optionsType = ref<any[]>([{ label: '全部', value: '' }]);
const cardTypeChange = (val: string) => {
  cardType.value = val;
  if (activeType.value == 2) {
    nextTick(() => {
      comType.value && comType.value.init();
    });
  }
};

const getOptions = async () => {
  if (!props.unitInfo.id) return;
  const res = await getCertificateType({ unitId: props.unitInfo.id, isDownUnit: unitType.value });
  optionsType.value = [{ name: '全部', id: '' }, ...res.data];
};

// 证书状态
const active = ref(1); // 证书状态分布 1列表 2table
const cardState = ref<any>('');
const chartOption = ref<any>({ unitId: '', isDownUnit: unitType.value });
const options = [
  { label: '全部', value: '' },
  { label: '正常', value: '1' },
  { label: '已过期', value: '2' },
  { label: '即将过期', value: '3' },
];
const cardStateChange = (val: string) => {
  cardState.value = val;
  if (active.value == 2) {
    nextTick(() => {
      comTable.value && comTable.value.init();
    });
  }
};

// 类型切换事件
const unitTypeChange = (val: string) => {
  unitType.value = val;
  cardState.value = '';
  cardType.value = '';
  if (active.value == 1) {
    nextTick(() => {
      comList.value && comList.value.init();
    });
  }
  if (activeType.value == 1) {
    nextTick(() => {
      getChartDate();
    });
  }
  if (active.value == 2) {
    nextTick(() => {
      comTable.value && comTable.value.init();
    });
  }
  if (activeType.value == 2) {
    nextTick(() => {
      getOptions();
      comType.value && comType.value.init();
    });
  }
};

const changeAC = (val: number) => {
  active.value = val;
  if (val == 1) {
    nextTick(() => {
      comList.value && comList.value.init();
    });
  } else {
    nextTick(() => {
      comTable.value && comTable.value.init();
    });
  }
};

const changeActiveType = (val: number) => {
  activeType.value = val;
  if (val == 1) {
  } else {
    nextTick(() => {
      comType.value && comType.value.init();
    });
  }
};

// 证书合规分析-证书类型分布
function getChartDate() {
  chartOption.value.isDownUnit = unitType.value;
  certificateCompliance(chartOption.value).then((res: any) => {
    if (res.code != '200') return;
    chartDate.value =
      (res.data &&
        res.data.map((item: any) => {
          return {
            ...item,
            name: item.certificateTypeName,
            value: item.certificateTypeCount,
          };
        })) ||
      [];
  });
}

watch(
  () => props.unitInfo,
  (newValue, oldValue) => {
    chartOption.value.unitId = newValue.id;
    // 初始化
    active.value = 1;
    activeType.value = 1;
    unitType.value = '1';
    if (newValue.orgType != '1') {
      isDownUnitShow.value = true;
    } else {
      isDownUnitShow.value = false;
    }
    getChartDate();
    getOptions();
  },
  { immediate: true }
);

watch(
  () => activeType.value,
  (val) => {
    if (activeType.value === 1) {
      getChartDate();
    }
  },
  { immediate: true }
);

const genderDistributionData = computed(() => {
  return chartDate.value.map((item, index) => ({
    name: item.certificateTypeName,
    value: item.certificateTypeCount,
    percent: item.percentage,
    color: genderColorConfig[index] || '',
  }));
});

defineOptions({ name: 'ComplianceComponent' }); // 修改组件名称为多单词形式
</script>
<style scoped lang="scss">
.n-radio-group .n-radio-button {
  // border: #527cff 1px solid !important;
  color: #527cff !important;
}

.n-radio-group .n-radio-button.n-radio-button--checked {
  background-color: #527cff !important;
  color: white !important;
}
</style>
