<template>
  <n-card style="background-color: #eef7ff">
    <template #header>
      <div class="flex justify-between">
        <div class="flex flex-row items-center">
          <div class="w-[4px] h-[16px] bg-[#527CFF] rounded-[4px] mr-[10px]"></div>
          <div class="flex flex-row items-center h-[34px] text-[16px] text-[#222222] font-bold">
            <span>岗位统计分析</span>
            <n-select
              class="!w-[192px] pl-[19px]"
              v-model:value="bookState"
              placeholder="请选择岗位"
              filterable
              label-field="name"
              value-field="name"
              :options="[{ name: '全部' }, ...genderDistributionData]"
              :on-update:value="(val: string) => bookStateChange(val)"
            />
          </div>
        </div>
        <div class="flex flex-row items-center" v-if="isDownUnitShow">
          <n-radio-group v-model:value="unitType" name="radiobuttongroup1" :on-update:value="unitTypeChange">
            <n-radio-button v-for="song in unitlist" :key="song.value" :value="song.value" :label="song.label" />
          </n-radio-group>
        </div>
      </div>
    </template>
    <n-grid x-gap="12" :cols="2" class="h-[325px] mt-[16px] mb-[13px]" style="gap: 0px 24px">
      <n-gi class="bg-white p-5 border border-[#DFE6F2] rounded-md">
        <PieChartComponent
          v-if="chartDate.length > 0"
          class="mt-[20px]"
          title="岗位统计分析"
          :chartData="bookState == '全部' ? genderDistributionData : filterableData"
        />
        <!-- <div v-if="chartDate.length > 0" ref="chartContainer" id="main" style="width: 100%; height: 300px"></div> -->
        <div
          v-else
          style="
            display: flex;
            align-items: center;
            flex-direction: column;
            height: 300px;
            color: gray;
            justify-content: center;
          "
        >
          <img src="../static/noData.png" />
          <p style="margin-left: 20px">暂无数据</p>
        </div>
      </n-gi>
      <n-gi class="bg-white p-5 border border-[#DFE6F2] rounded-md">
        <n-data-table
          :min-height="288"
          :max-height="288"
          :loading="loading"
          :columns="columns"
          :data="data"
          :pagination="false"
          :bordered="false"
        />
      </n-gi>
    </n-grid>
  </n-card>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, watchEffect, nextTick, computed } from 'vue';
import * as echarts from 'echarts'; // 正确引入 ECharts
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { postStatistics, postStatisticsList } from '../fetchData';
import PieChartComponent from './pieComponent.vue';
import { genderColorConfig } from './colorList';
const isDownUnitShow = ref(false);
const props: any = defineProps({
  unitInfo: { type: Object },
});
const { pagination, updateTotal } = useNaivePagination(getTabData);
pagination.pageSize = 5;
const loading = ref(false);
// 表格数据
const data = ref<any[]>([]);
const columns = [
  {
    title: '单位名称',
    key: 'unitName',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '人员数量',
    key: 'personCount',
    width: 80,
  },
  {
    title: '人员占比',
    key: 'percentage',
    width: 80,
    render: (row: any) => {
      return row.percentage ? row.percentage + '%' : '--';
    },
  },
];
// 图表参数
const chartOption = ref({
  isDownUnit: '1',
  unitId: '',
});
// 列表参数
const params = ref({
  unitId: '',
  postName: '1',
  pageNo: 1,
  pageSize: -1,
  isDownUnit: '1',
});

// 图表实例
const chartDate = ref<any[]>([]);
// 岗位名称
const bookState = ref('全部');
const bookStateChange = (val: string) => {
  bookState.value = val;
  params.value.postName = val;
  if (val == '全部') params.value.postName = '1';
  nextTick(() => {
    getTabData();
  });
};
const filterableData = computed(() => {
  return chartDate.value
    .filter((item) => bookState.value === '全部' || item.postName === bookState.value)
    .map((item, index) => ({
      name: item.postName,
      value: item.postPersonCount,
      percent: item.percentage,
      color: genderColorConfig[index] || '',
    }));
});
// 单位类型
const unitType = ref('1');
const unitlist = [
  { value: '1', label: '下级单位' },
  { value: '0', label: '本级部门' },
];
const fetchData = () => {
  postStatistics(chartOption.value).then((res: any) => {
    if (res.code != '200') return;
    chartDate.value = res.data || [];
    // (res.data &&
    //   res.data.map((item: any) => {
    //     return {
    //       name: item.postName,
    //       value: item.percentage,
    //     };
    //   })) ||
    // [];
  });
};
// 类型切换事件
const unitTypeChange = (val: string) => {
  unitType.value = val;
  params.value.pageNo = 1;
  params.value.isDownUnit = val;
  chartOption.value.isDownUnit = val;
  getTabData();
  fetchData();
};

watch(
  () => props.unitInfo,
  (newValue) => {
    chartOption.value.unitId = newValue.id;
    params.value.unitId = newValue.id;
    unitType.value = '1';
    if (newValue.orgType != '1') {
      params.value.isDownUnit = '1';
      chartOption.value.isDownUnit = '1';
      isDownUnitShow.value = true;
    } else {
      params.value.isDownUnit = '0';
      chartOption.value.isDownUnit = '0';
      isDownUnitShow.value = false;
    }
    fetchData();
    getTabData();
  }
);

const genderDistributionData = computed(() => {
  return chartDate.value.map((item, index) => ({
    name: item.postName,
    value: item.postPersonCount,
    percent: item.percentage,
    color: genderColorConfig[index] || '',
  }));
});
// 分页数据获取
function getTabData() {
  // 在这里编写获取数据的逻辑
  try {
    loading.value = true;
    postStatisticsList(params.value)
      .then((res: any) => {
        console.log('获取数据:', res);
        loading.value = false;
        if (res.code != '200') return;
        data.value = res.data.rows || [];
        updateTotal(res.data.total || 0);
      })
      .catch((error) => {
        loading.value = false;
        data.value = [];
      });
  } catch (error) {
    loading.value = false;
  }
}

defineOptions({ name: 'personStatistics' });
</script>
<style scoped lang="scss">
.active {
  background-color: #527cff !important;
  border-radius: 4px !important;
  color: white !important;
}

.n-radio-group .n-radio-button {
  // border: #527cff 1px solid !important;
  color: #527cff !important;
}

.n-radio-group .n-radio-button.n-radio-button--checked {
  background-color: #527cff !important;
  color: white !important;
}

.n-card.n-card--bordered {
  border: 0px !important;
}

::v-deep .n-card-header {
  padding: 0px !important;
}

::v-deep .n-card__content {
  padding: 0px !important;
}

.header {
  position: relative;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    background-color: #527cff;
    position: absolute;
    top: 10%;
    left: 0;
  }
}

.header {
  position: relative;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    border-radius: 4px;
    background-color: #527cff;
    position: absolute;
    top: 14%;
    left: 0;
  }
}
</style>
