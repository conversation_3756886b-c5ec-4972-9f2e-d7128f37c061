<template>
  <div class="chart-container" ref="chartRef"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  // 图表标题
  title: {
    type: String,
    default: '',
  },
  // 图表数据
  chartData: {
    type: Array,
    required: true,
    // 数据格式: [{name: '名称', value: 数值, color: {渐变色配置}}]
  },
  // 图例数据
  legendData: {
    type: Array,
    default: () => [],
  },
  // 是否显示图例
  showLegend: {
    type: Boolean,
    default: true,
  },
});

const chartRef = ref(null);
let chart = null;

onMounted(() => {
  if (chartRef.value) {
    chart = echarts.init(chartRef.value);
    renderChart();

    window.addEventListener('resize', handleResize);
  }
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
    window.removeEventListener('resize', handleResize);
  }
});

// 监听数据变化，重新渲染图表
watch(
  () => props.chartData,
  () => {
    renderChart();
  },
  { deep: true }
);

const handleResize = () => {
  chart && chart.resize();
};

// 在script setup区域顶部添加颜色生成函数
const getRandomColor = () => {
  const hue = Math.floor(Math.random() * 360); // 色相值0-360
  return `hsl(${hue}, 70%, 50%)`;
};

const renderChart = () => {
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}人 ({d}%)',
      confine: true,
      position: function (point, params, dom, rect, size) {
        const chartWidth = size.viewSize[0];
        return point[0] > chartWidth / 2 ? [20, '50%'] : null;
      },
    },
    legend: {
      type: 'scroll',
      show: props.showLegend,
      orient: 'vertical',
      right: 50,
      top: 'center',
      data: props.legendData.length > 0 ? props.legendData : props.chartData.map((item) => item.name),
      padding: 10,
      borderRadius: 4,
      itemGap: 15,
      textStyle: {
        color: '#333',
        fontSize: 14,
        rich: {
          name: {
            width: 80,
            overflow: 'truncate',
            ellipsis: '...',
          },
          value: {
            color: '#527CFF',
            padding: [0, 5],
          },
        },
      },
      formatter: (name) => {
        const item = props.chartData.find((v) => v.name === name);
        // 名称超过4字换行显示
        const showName = name.length > 6 ? `${name.slice(0, 6)}...` : name;
        const value = item?.value || 0;
        const percent = item?.percent || 0;
        // 使用富文本格式
        if (!item.percent) return `{name|${showName}} {value|${value}人}`;
        return `{name|${showName}} {value|${value}人 ${percent}%}`;
      },
      tooltip: {
        show: true,
        formatter: ({ name }) => name,
        position: function (point) {
          // 固定提示框在右侧对齐
          return [point[0] - 120, point[1] - 20];
        },
        backgroundColor: 'rgba(0,0,0,0.7)',
        textStyle: {
          color: '#fff',
          fontSize: 12,
        },
      },
      icon: 'circle',
      itemWidth: 10,
      itemHeight: 10,
    },
    series: [
      {
        name: props.title,
        type: 'pie',
        radius: ['40%', '78%'],
        center: ['30%', '45%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold',
            // 新增文字截断配置
            width: 80,
            overflow: 'truncate',
            ellipsis: '...',
            formatter: (params) => {
              // 截断超过6个字符的标签
              return params.name.length > 6 ? `${params.name.slice(0, 6)}...` : params.name;
            },
          },
          itemStyle: {
            borderWidth: 3,
            shadowBlur: 15,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        labelLine: {
          show: false,
        },
        data: props.chartData.map((item, index) => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.color || getRandomColor(),
          },
        })),
      },
    ],
  };

  chart.setOption(option);
};
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 320px;
}
</style>
