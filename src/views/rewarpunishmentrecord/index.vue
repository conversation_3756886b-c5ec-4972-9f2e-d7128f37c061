<template>
  <div class="">
    <com-bread :data="breadData"></com-bread>
    <!-- h-[calc(100%-86px)] -->
    <div class="flex h-[calc(100%-85px)]">
      <div class="h-[calc(100%+40px)]" v-show="showTree">
        <com-tree
          :title="titlee"
          :dataList="treeData"
          @handelChange="handelChange"
        ></com-tree>
      </div>

      <div class="!ml-[15px] relative">
        <img
          @click="showTree = !showTree"
          src="@/assets/open.png"
          class="change-tree-btn"
        />
        <RadioTab :tabList="tabList" :tab="category" @change="tabChange" />
        <div class="com-table-filter h-[calc(100%-0px)]" v-if="category == 3">
          <ConfigList />
        </div>
        <div class="com-table-container h-[calc(100%-0px)]" v-else>
          <Filter
            ref="FilterRef"
            :unitId="unitId || store.userInfo.unitId"
            :loginUnitId="store.userInfo.orgCode"
            :category="category"
            @action="actionFn"
            class="my-[15px]"
          />
          <!-- tab -->
          <n-tabs
            animated
            v-model:value="objType"
            @update:value="handleChangetab"
            tab-style="font-size: 17px;font-weight: bold"
          >
            <n-tab-pane
              v-for="(item, index) of objTypes"
              :key="index"
              :name="item.name"
              :tab="item.label"
            >
            </n-tab-pane>
          </n-tabs>
          <TableList
            :loginUnitId="store.userInfo.orgCode"
            :category="category"
            :objType="objType"
            ref="tableCompRef"
            @action="actionFn"
          />
        </div>
      </div>
    </div>
    <!-- 新增编辑弹窗 -->
    <n-drawer v-model:show="FormModalShow" class="models !w-[650px]" auto-focus>
      <n-drawer-content closable>
        <template #header>
          <div class="flex flex-row items-center">
            <img
              class="w-[17px] h-[12px] mr-[20px]"
              src="@/components/header/assets/icon-title-arrow3.png"
            />
            <div class="text-[16px] text-[#222222] font-bold">
              {{ actionLabel }}
            </div>
          </div>
        </template>
        <FormModal
          :category="Number(category)"
          :currentAction="currentAction"
          :objType="objType"
          :treeData="JSON.parse(JSON.stringify(treeData))"
          @closeModal="closeModal"
          ref="FormModalRef"
        />
        <template #footer>
          <div class="flex justify-end items-center">
            <n-button @click="FormModalShow = false">{{
              currentAction.action == ACTION.DETAIL ? '关闭' : '取消'
            }}</n-button>
            <n-button
              v-if="[ACTION.ADD, ACTION.EDIT].includes(currentAction.action)"
              type="primary"
              @click="saveFormMoal"
              style="margin-left: 10px"
            >
              确定
            </n-button>
          </div>
        </template>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, computed, toRaw } from 'vue';
import comTree from '@/components/tree/comTree.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import RadioTab from '@/components/tab/ComRadioTabA.vue';
import Filter from '@/views/rewarpunishmentrecord/comp/Filter.vue';
import TableList from '@/views/rewarpunishmentrecord/comp/TableList.vue';
import FormModal from '@/views/rewarpunishmentrecord/Add/FormModal.vue';
import ConfigList from './config/index.vue';
import { useStore } from '@/store';
import { getOrgTrees } from './fetchData.ts';
import { useRoute, useRouter } from 'vue-router';
import { TreeOption } from 'naive-ui';
import { IActionData } from './type.ts';
import { ACTION, ACTION_LABEL } from './constant.ts';
import { IObj } from '@/types/index.js';

const store = useStore();
const router = useRouter();
const route = useRoute();
const category = ref<any>(route.query?.tab || 1);
const showTree = ref(true);
const titlee = ref('奖惩记录');
const treeData = ref([]);
const treeLevelCode: any = ref(null);
const unitId: any = ref('');
const FormModalShow = ref(false);
const FormModalRef = ref();
const tableCompRef = ref();
const FilterRef = ref();
const currentAction = ref<IActionData>({ action: ACTION.SEARCH, data: {} });
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);
const breadData = ref<any>([
  { name: '奖惩记录' },
  {
    name: ['奖励记录', '惩罚记录', '奖惩配置'][
      route.query?.tab ? category.value - 1 : 0
    ],
  },
]);

// tabs
const tabList = [
  { name: '1', label: '奖励记录' },
  { name: '2', label: '惩罚记录' },
  { name: '3', label: '奖惩配置' },
];

const objTypes = [
  { name: 1, label: '员工' },
  { name: 2, label: '部门' },
  { name: 3, label: '单位' },
];
const objType = ref<any>(1);

function tabChange(_category: number) {
  category.value = _category;
  breadData.value = [
    { name: '奖惩记录' },
    {
      name: ['', '奖励记录', '惩罚记录', '奖惩配置'][category.value],
    },
  ];
  // router.push({
  //   path: "/rewarpunishmentrecord",
  //   query: {
  //     tab: _category,
  //   },
  // });
}

function closeModal() {
  FormModalShow.value = false;
  FilterRef.value.resetForm();
}
// 切换 tab
function handleChangetab(val: any) {
  objType.value = val;
}

function actionFn(val: IActionData) {
  currentAction.value = val;
  if (val.action === ACTION.SEARCH) {
    handleSearch(val.data);
  }
  //新增 编辑 查看
  if ([ACTION.ADD, ACTION.EDIT, ACTION.DETAIL].includes(val.action)) {
    FormModalShow.value = true;
  }
}

function saveFormMoal() {
  FormModalRef.value.handleSubmit();
}
// 搜索
function handleSearch(data?: IObj<any>) {
  console.log(data, 'data');
  tableCompRef.value?.getTableDataWrap(data);
}
// 获取组织树
function QueryOrgTrees() {
  const params = {
    orgCode: store.userInfo.unitId,
    needChildUnit: '1',
    needself: '1',
  };
  getOrgTrees(params).then((res: any) => {
    if (res.code != 200) return;
    const _RES: any = removeEmptyChildren(res.data);
    treeData.value = _RES;
  });
}

//树结构点击后获取到的值
const handelChange = (v: TreeOption) => {
  unitId.value = v.id;
  treeLevelCode.value = v.levelCode;
};
// 递归函数，使用map生成新的数组
function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };

    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }

    return newItem; // 返回新对象
  });
}
onMounted(() => {
  QueryOrgTrees();
});

defineOptions({ name: 'rewarpunishmentrecordIndex' });
</script>
<style lang="scss">
.n-drawer-mask {
  background-color: rgba(0, 0, 0, 0);
}
</style>
