/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-15 17:54:11
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-16 18:21:46
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/views/personManage/checklist-conf/check-library/type.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { ACTION } from './constant';
import type { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

/**
 *列表数据
 */
export interface IPageData {
  /**
   * 姓名、单位、部门或岗位关键词
   */
  keyWords?: string;
  /**
   * 部门id或者单位id
   */
  orgCode: string;
  /**
   * 页码
   */
  pageNo?: number;
  /**
   * 条数
   */
  pageSize?: number;
  /**
   * 人员类型 1安全管理人员 2特种作业人员
   */
  type: number;
  [property: string]: any;
}

export type IPageDataRes = IPageRes<IPageData>;

/**
 * 树结构
 */
export interface OrgTree {
  /**
   * 节点属性
   */
  attributes?: { [key: string]: any };
  /**
   * 点是否被选中
   */
  checked?: boolean;
  /**
   * 节点的子节点
   */
  children?: OrgTree[];
  hasChildren?: boolean;
  hasParent?: boolean;
  /**
   * 主键id
   */
  id?: string;
  /**
   * 层级
   */
  level?: number;
  /**
   * 父ID
   */
  parentId?: string;
  state?: string;
  /**
   * 节点名称
   */
  text?: string;
  /**
   * 树名
   */
  treeName?: string;
  /**
   * 节点类型
   */
  type?: string;
  /**
   * 节点id
   */
  typeId?: string;
  [property: string]: any;
}
/**
 * GetOrgUserVo获取组织树人员
 */
export interface GetOrgUserVo {
  /**
   * 部门Id
   */
  deptId?: string;
  /**
   * 部门名称
   */
  deptName?: string;
  /**
   * 用户id
   */
  id?: string;
  /**
   * 是否配置： 1是 0否
   */
  isConfig?: string;
  /**
   * 用户类型
   */
  personType?: string;
  /**
   * 用户类型名称
   */
  personTypeName?: string;
  /**
   * 岗位
   */
  postName?: string;
  /**
   * 单位Id
   */
  unitId?: string;
  /**
   * 单位名称
   */
  unitName?: string;
  /**
   * 姓名
   */
  userName?: string;
  /**
   * 手机号
   */
  userTelphone?: string;
  [property: string]: any;
}
