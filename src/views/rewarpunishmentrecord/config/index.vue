<template>
  <div class="">
    <div class="flex gap-4 justify-end items-center">
      <n-button type="primary" @click="formAdd"> 新增 </n-button>
    </div>
    <n-tabs animated @update:value="tabChange" tab-style="font-size: 17px;font-weight: bold">
      <n-tab-pane :name="1" tab="奖励类型配置"> </n-tab-pane>
      <n-tab-pane :name="2" tab="惩罚类型配置"> </n-tab-pane>
    </n-tabs>
    <n-data-table
      class="h-full com-table"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :row-key="(row: any) => row.id"
      :loading="loading"
      :pagination="pagination"
      :render-cell="useEmptyCell"
      :min-height="450"
      :max-height="530"
    />
  </div>
  <n-drawer v-model:show="showModal" class="models !w-[550px]" :autoFocus="false">
    <n-drawer-content closable>
      <template #header>
        <div class="flex flex-row items-center">
          <img class="w-[17px] h-[12px] mr-[20px]" src="@/components/header/assets/icon-title-arrow3.png" />
          <div class="text-[16px] text-[#222222] font-bold">
            {{ formData.id ? '编辑' : '新增' }}
          </div>
        </div>
      </template>
      <n-form
        label-placement="left"
        label-width="110px"
        label-align="right"
        ref="formRef"
        :model="formData"
        :rules="rules"
      >
        <n-form-item path="category" label="奖惩类型">
          <n-select
            v-model:value="formData.category"
            :disabled="formData.id"
            placeholder="请选择"
            clearable
            :options="typeoptions"
          />
        </n-form-item>
        <n-form-item label="类型名称" path="rewardPunishmentTypeName">
          <n-input
            v-model:value="formData.rewardPunishmentTypeName"
            maxlength="20"
            show-count
            clearable
            placeholder="请输入"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <div class="flex justify-end items-center">
          <n-button @click="closeModal" :loading="formLoading">取消</n-button>
          <n-button style="margin-left: 10px" type="primary" @click="submitForm" :loading="formLoading">
            确定
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<script setup lang="ts">
import { useActionDivider } from '@/common/hooks/useActionDivider';
import { useAutoLoading } from '@/common/hooks/useAutoLoading';
import { useNaivePagination } from '@/common/hooks/useNaivePagination';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { NButton, useMessage } from 'naive-ui';
import { h, onMounted, ref } from 'vue';
import {
  getRewardPunishmentTypeList,
  rewardPunishmentTypeEdit,
  rewardPunishmentTypeDelete,
  rewardPunishmentTypeAdd,
} from '@/views/rewarpunishmentrecord/fetchData.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';

const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);
const columns = ref([
  {
    title: '序号',
    key: 'index',
    width: 65,
    align: 'center',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '类型名称',
    key: 'rewardPunishmentTypeName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    fixed: 'right',
    width: '250px',
    title: '操作',
    key: 'actions',
    align: 'left',
    render(row: any) {
      return useActionDivider([
        [
          h(
            NButton,
            {
              text: true,
              class: 'com-edit-button',
              onClick: () => {
                formData.value.category = row.category;
                formData.value.id = row.id;
                formData.value.rewardPunishmentTypeName = row.rewardPunishmentTypeName;
                showModal.value = true;
              },
            },
            { default: () => '编辑' }
          ),
        ],
        [
          h(
            NButton,
            {
              text: true,
              class: 'com-del-button ',
              onClick: () => {
                delData(row.id);
              },
            },
            { default: () => '删除' }
          ),
        ],
      ]);
    },
  },
]);
const tableData = ref([]);
const formLoading = ref(false);
const showModal = ref(false);
const message = useMessage();

const category = ref(1);
const formRef = ref();
const formData = ref({
  id: null,
  category: category.value,
  rewardPunishmentTypeName: '',
});
const typeoptions = [
  {
    label: '奖励',
    value: 1,
  },
  {
    label: '惩罚',
    value: 2,
  },
];
// 规则
const rules: any = {
  category: [
    {
      type: 'number',
      required: true,
      message: '请选择奖惩类型',
      trigger: ['blur', 'change'],
    },
  ],
  rewardPunishmentTypeName: [
    {
      required: true,
      message: '请输入类型名称',
      trigger: ['blur'],
    },
  ],
};

function tabChange(val: any) {
  category.value = val;
  getTableData();
}
function getTableData() {
  const params = {
    category: category.value,
    pageSize: pagination.pageSize,
    pageNo: pagination.page,
  };
  search(getRewardPunishmentTypeList(params)).then((res: any) => {
    if (res.code != 'success') return;
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function delData(id: string) {
  $dialog.error({
    title: '删除',
    content: '确定删除吗',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      let { code }: any = await rewardPunishmentTypeDelete({ id });
      if (code == 'success') {
        pagination.pageSize = 10;
        pagination.page = 1;
        getTableData();
      }
    },
  });
}
// 新增
function formAdd() {
  formData.value.id = null;
  formData.value.category = category.value;
  formData.value.rewardPunishmentTypeName = '';
  showModal.value = true;
}
function closeModal() {
  showModal.value = false;
}
function submitForm() {
  formRef.value.validate(async (errors: any) => {
    if (!errors) {
      formLoading.value = true;
      try {
        let { code }: any = formData.value.id
          ? await rewardPunishmentTypeEdit(formData.value)
          : await rewardPunishmentTypeAdd(formData.value);
        formLoading.value = false;
        if (code == 'success') {
          message.success(formData.value.id ? '编辑成功' : '新增成功');
          closeModal();
          getTableData();
        }
      } catch (error) {
        formLoading.value = false;
      }
    }
  });
}
onMounted(() => {
  getTableData();
});
defineOptions({ name: 'rewarpunishmentconfigIndex' });
</script>
