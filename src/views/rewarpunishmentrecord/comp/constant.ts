import { NImageGroup, NImage } from 'naive-ui';
import { h } from 'vue';
import { Base64 } from 'js-base64';
function getApiBase() {
  return window.$SYS_CFG.apiPreviewURL;
}

export function getColumns(category: number) {
  return [
    {
      title: '序号',
      key: 'index',
      width: 65,
      align: 'left',
      render: (_: any, index: number) => {
        return index + 1;
      },
    },
    {
      title: `${category == 1 ? '奖励' : '惩罚'}类型`,
      key: 'rewardPunishmentTypeName',
      align: 'left',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: `${category == 1 ? '奖励金额/物品' : '惩罚程度'}`,
      key: 'thing',
      align: 'left',
      width: 130,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render: (row: any) => {
        return row.thing || '--';
      },
    },
    {
      title: `${category == 1 ? '奖励标准' : '惩罚事由'}`,
      key: 'standard',
      align: 'left',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render: (row: any) => {
        return row.standard || '--';
      },
    },
    {
      title: `${category == 1 ? '奖励' : '惩罚'}状态`,
      key: 'rewardPunishmentStatus',
      align: 'left',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render: (row: any) => {
        return category == 1
          ? ['', '未发放', '已发放', '已取消'][row.rewardPunishmentStatus] || '--'
          : ['', '未惩罚', '已惩罚', '已取消'][row.rewardPunishmentStatus] || '--';
      },
    },
    {
      title: `${category == 1 ? '奖励' : '惩罚'}日期`,
      key: 'occurTime',
      align: 'left',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: `${category == 1 ? '奖励' : '惩罚'}文件`,
      key: 'attachments',
      width: '130',
      resizable: true,
      align: 'left',
      render: (row: any) => {
        const list = row.fileUrlList && row.fileUrlList.length ? row.fileUrlList.slice(0, 1) : [];
        if (!list.length) {
          return '--';
        }
        // 假设 list 是一个包含附件信息的数组
        return list.map((attachment: any) => {
          // 根据附件类型生成相应的虚拟DOM元素
          const name = attachment.address.split('/');
          const fileName = name[name.length - 1];
          const type = fileName.split('.');
          const typeName = type[type.length - 1];
          if (typeName == 'doc' || typeName == 'docx') {
            const b64Encoded = Base64.encode(window.$SYS_CFG.apiPreviewURL + attachment.address);
            return h(
              'a',
              {
                href: window.$SYS_CFG.apiPreviewLink + encodeURIComponent(b64Encoded),
                title: fileName,
                target: '_blank',
                style: {
                  color: '#02a7f0',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                  overflow: 'hidden',
                  width: '120px',
                  display: 'inline-block',
                },
              },
              fileName
            );
          } else {
            return h(
              'a',
              {
                href: window.$SYS_CFG.apiPreviewURL + attachment.address,
                title: fileName,
                target: '_blank',
                style: {
                  color: '#02a7f0',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                  overflow: 'hidden',
                  width: '120px',
                  display: 'inline-block',
                },
              },
              fileName
            );
          }
        });
      },
    },
    {
      title: '现场照片',
      key: 'pictureUrlList',
      align: 'left',
      fixed: 'right',
      resizable: true,
      render: (row: any) => {
        const list = row.pictureUrlList && row.pictureUrlList.length ? row.pictureUrlList.slice(0, 1) : [];
        if (!list.length) return '--';
        return h(
          NImageGroup,
          {},
          list.map((item: any) =>
            h(NImage, {
              src: `${window.$SYS_CFG.apiPreviewURL}${item.address}`,
              style: { width: '40px', height: '40px', borderRadius: '5px' },
            })
          )
        );
      },
    },
  ];
}

export function getOtherCols(objType: number) {
  const data = [
    [
      {
        title: `员工姓名`,
        key: 'objName',
        align: 'left',
        resizable: true,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: `部门`,
        key: 'objDeptName',
        align: 'left',
        resizable: true,
        ellipsis: {
          tooltip: true,
        },
      },
    ],
    [
      {
        title: `部门`,
        key: 'objName',
        align: 'left',
        resizable: true,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: `部门负责人`,
        key: 'objDeptChargeUserName',
        width: 130,
        align: 'left',
        resizable: true,
        ellipsis: {
          tooltip: true,
        },
        render: (row: any) => {
          return row.objDeptChargeUserName || '--';
        },
      },
    ],
    [
      {
        title: `单位`,
        key: 'objName',
        align: 'left',
        resizable: true,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: `单位负责人`,
        key: 'objUnitChargeUserName',
        width: 130,
        align: 'left',
        ellipsis: {
          tooltip: true,
        },
        render: (row: any) => {
          return row.objUnitChargeUserName || '--';
        },
      },
    ],
  ];
  return data[objType - 1];
}

export function giveTypeList(type = 1) {
  return [
    {
      label: type == 1 ? '未发放' : '未惩罚',
      value: 1,
    },
    {
      label: type == 1 ? '已发放' : '已惩罚',
      value: 2,
    },
    {
      label: '已取消',
      value: 3,
    },
  ];
}
