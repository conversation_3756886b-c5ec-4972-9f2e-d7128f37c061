<template>
  <!-- class="h-full" -->
  <n-data-table
    class="com-table"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :loading="loading"
    :pagination="pagination"
    :render-cell="useEmptyCell"
    :min-height="400"
    :max-height="400"
  />
  <!-- :scroll-x="1500" -->
</template>
<script setup lang="ts">
import { h, ref, VNode, toRaw, watch } from 'vue';
import type { IPageData } from '@/views/rewarpunishmentrecord/type.ts';
import { NButton } from 'naive-ui';
import {
  getColumns,
  getOtherCols,
} from '@/views/rewarpunishmentrecord/comp/constant';
import {
  ACTION,
  ACTION_LABEL,
} from '@/views/rewarpunishmentrecord/constant.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import {
  recordList,
  recordDelete,
} from '@/views/rewarpunishmentrecord/fetchData.ts';
import { useStore } from '@/store';
const emits = defineEmits(['action']);
const store = useStore();
const props = defineProps({
  useArray: {
    type: Array<string | number | undefined>,
    default: () => [],
  },
  // 人员 部门 单位
  objType: {
    type: Number,
    default: 1,
  },
  // 奖励记录/惩罚记录
  category: {
    type: Number,
    default: 1,
  },
  treeCode: {
    type: String,
  },
  loginUnitId: {
    type: Boolean,
    default: false,
  },
});
let filterData: IObj<any> = {}; // 搜索条件
const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const columns = ref<any>([]);
const tableData = ref<IPageData[]>([]);
function setColumns() {
  columns.value = [];
  let otherCols: any = getOtherCols(props.objType);
  let commonCols: any = getColumns(props.category);
  commonCols.splice(1, 0, ...otherCols);
  columns.value.push(...commonCols);

  // 添加操作栏 action
  columns.value.splice(columns.value.length, 0, {
    title: '操作',
    key: 'actions',
    align: 'left',
    fixed: 'right',
    width: 250,
    render(row: any) {
      return getActionBtn(row);
    },
  });
}
function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-edit-button',
          onClick: () =>
            emits('action', { action: ACTION.DETAIL, data: toRaw(row) }),
        },
        { default: () => '查看' }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-edit-button',
          disabled: row.createUnitId && props.loginUnitId != row.createUnitId,
          onClick: () =>
            emits('action', { action: ACTION.EDIT, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.EDIT }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-del-button ',
          disabled: row.createUnitId && props.loginUnitId != row.createUnitId,
          onClick: () => handleDelete(row.id),
        },
        { default: () => ACTION_LABEL.DELETE }
      ),
    ],
  ];
  return useActionDivider(acList);
}
function getTableData() {
  console.log("'调用'>>>", '调用');
  setColumns();
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    objType: props.objType,
    category: Number(props.category),
    ...filterData,
    unitId: filterData.unitId || store.userInfo.unitId,
  };
  search(recordList(params)).then((res: any) => {
    if (res.code != 'success') return;
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = data;
  pagination.page = 1;
  getTableData();
}
// 删除
function handleDelete(id: any) {
  $dialog.error({
    title: '删除',
    content: `你确定要删除这条记录吗？`,
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      let { code }: any = await recordDelete({ id });
      if (code == 'success') {
        pagination.page = 1;
        pagination.pageSize = 10;
        getTableData();
      }
    },
  });
}
watch(
  () => [props.objType, props.category],
  (val) => {
    pagination.page = 1;
    pagination.pageSize = 10;
    setColumns();
    getTableData();
  },
  { immediate: true }
);

defineExpose({ getTableDataWrap, getTableData });
defineOptions({ name: 'personManageTable' });
</script>
<style lang="scss" scoped>
.box {
  --com-border-radius: 5px;
}
</style>
