<template>
  <n-form :model="filterForm" label-placement="left" inline :show-feedback="false">
    <n-row gutter="12">
      <n-col :span="6">
        <n-form-item path="rewardPunishmentTypeId" :label="`${category == 1 ? '奖励' : '惩罚'}类型:`">
          <n-select
            placeholder="请选择"
            v-model:value="filterForm.rewardPunishmentTypeId"
            clearable
            label-field="rewardPunishmentTypeName"
            value-field="id"
            :options="configData"
            @update:value="doHandle(ACTION.SEARCH)"
          />
        </n-form-item>
      </n-col>
      <n-col :span="6">
        <n-form-item path="rewardPunishmentStatus" :label="`${category == 1 ? '奖励' : '惩罚'}状态:`">
          <n-select
            placeholder="请选择"
            v-model:value="filterForm.rewardPunishmentStatus"
            clearable
            :options="giveTypeList(category)"
            @update:value="doHandle(ACTION.SEARCH)"
          />
        </n-form-item>
      </n-col>
      <n-col :span="8">
        <n-form-item :label="`${category == 1 ? '奖励' : '惩罚'}日期:`">
          <n-date-picker
            v-model:value="dateRange"
            placeholder="请选择"
            value-format="yyyy-MM-dd"
            type="daterange"
            @change="dateChange"
            clearable
          />
        </n-form-item>
      </n-col>
      <n-col :span="4">
        <div class="flex gap-4 justify-end items-center">
          <n-button type="primary" @click="doHandle(ACTION.ADD)"> 新增 </n-button>
        </div>
      </n-col>
    </n-row>
  </n-form>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { ACTION } from '../constant';
import { getRewardPunishmentTypeList } from '@/views/rewarpunishmentrecord/fetchData.ts';
import { giveTypeList } from '@/views/rewarpunishmentrecord/comp/constant';
import { trimObjNull } from '@/utils/obj.ts';
import dayjs from 'dayjs';

const props: any = defineProps({
  // 奖励记录/惩罚记录
  category: {
    type: Number,
    default: 1,
  },
  unitId: {
    type: String,
    default: '',
  },
});
const dateRange = ref();
const emits = defineEmits(['action']);
const filterForm = ref({
  rewardPunishmentTypeId: null,
  rewardPunishmentStatus: null,
  startDate: '',
  endDate: '',
  category: props.category,
});
const configData = ref([]); //奖励类型

function resetForm() {
  // 奖励类型/奖励状态/日期
  filterForm.value = {
    rewardPunishmentTypeId: null,
    rewardPunishmentStatus: null,
    startDate: '',
    endDate: '',
    category: props.category,
  };
  dateRange.value = null;
  doHandle(ACTION.SEARCH);
}

// 编制时间
function dateChange(date: any) {
  if (date) {
    filterForm.value.startDate = dayjs(date[0]).format('YYYY-MM-DD');
    filterForm.value.endDate = dayjs(date[1]).format('YYYY-MM-DD');
  } else {
    filterForm.value.startDate = filterForm.value.endDate = '';
  }
  doHandle(ACTION.SEARCH);
}

// 获取奖励类型
function getRewardPunishmentTypeListApi() {
  getRewardPunishmentTypeList({
    category: props.category,
    pageNo: 1,
    pageSize: -1,
  }).then((res: any) => {
    if (res.code != 'success') return;
    configData.value = res.data.rows || [];
  });
}
function doHandle(action: ACTION) {
  emits('action', {
    action,
    data: trimObjNull({
      unitId: props.unitId,
      ...filterForm.value,
    }),
  });
}

watch(
  () => props.category,
  () => {
    getRewardPunishmentTypeListApi(); //获取奖励类型
    resetForm();
  }
);
// watch(filterForm.value, () => {
//   doHandle(ACTION.SEARCH);
// });
watch(
  () => props.unitId,
  () => {
    doHandle(ACTION.SEARCH);
  }
);

onMounted(() => {
  getRewardPunishmentTypeListApi(); //获取奖励类型
});

defineOptions({ name: 'checkTempFilterComp' });
defineExpose({ resetForm, doHandle });
</script>
<style module lang="scss">
:global(.com-table-filter) {
  @apply rounded-tl-none border-none pb-[0px];
}
</style>
