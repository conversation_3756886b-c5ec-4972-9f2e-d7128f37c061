/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-15 14:50:52
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-19 15:22:47
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/views/personManage/fetchData.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import type { IPageDataRes, OrgTree } from './type';
import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';

// 获取树形结构
export function getOrgTrees(query: IObj<any>) {
  return $http.get<OrgTree>(api.getUrl(api.type.server, api.name.interface.getTreeData, query));
}

// // 获取人员列表
// export function getPersonManageList(params: any) {
//   const url = api.getUrl(api.type.server, api.name.interface.getPersonManageList);
//   return $http.post<IPageDataRes>(url, {
//     data: { _cfg: { showTip: false }, ...params },
//   });
// }
// 获取人员列表
export function getPersonManageList(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getExpertPersonManageList);
  return $http.post<IPageDataRes>(url, {
    data: { _cfg: { showTip: true }, ...params },
  });
}

// 奖惩配置
// 列表
export function getRewardPunishmentTypeList(query: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.getRewardPunishmentTypeList, query);
  return $http.post<IPageDataRes>(url, { data: { _cfg: { showTip: false } } });
}

//详情
export function getRewardPunishmentTypeDetail(query: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.getRewardPunishmentTypeDetail, query);
  return $http.post<IPageDataRes>(url, { data: { _cfg: { showTip: false } } });
}

// 新增
export function rewardPunishmentTypeAdd(params: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.rewardPunishmentTypeAdd);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}
// 修改
export function rewardPunishmentTypeEdit(params: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.rewardPunishmentTypeEdit);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}
// 删除
export function rewardPunishmentTypeDelete(params: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.rewardPunishmentTypeDelete, params);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true } },
  });
}

// 奖惩记录管理
export function recordList(params: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.recordList);
  return $http.post<IPageDataRes>(url, {
    data: { _cfg: { showTip: false }, ...params },
  });
}

//详情
export function getRecordInfo(query: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.getRecordInfo, query);
  return $http.get<IPageDataRes>(url, { data: { _cfg: { showTip: false } } });
}

// 新增
export function recordAdd(params: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.recordAdd);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}
// 修改
export function recordEdit(params: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.recordEdit);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}
// 删除
export function recordDelete(params: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.recordDelete, params);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true } },
  });
}

// 获取组织机构树
export function getOrgTree(query: IObj<any>) {
  const url = api.getUrl('ehs-clnt-hazard-service', '/ehsUpms/getOrgTree', {
    ...query,
  });
  return $http.post<any[]>(url, { data: { _cfg: { showTip: true } } });
}
