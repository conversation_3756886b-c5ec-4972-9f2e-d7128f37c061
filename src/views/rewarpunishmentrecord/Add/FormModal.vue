<template>
  <n-form
    ref="formRef"
    :disabled="currentAction.action == ACTION.DETAIL"
    :model="modelForm"
    :rules="rules"
    label-placement="left"
    label-width="140px"
    require-mark-placement="right-hanging"
    size="small"
    :style="{
      maxWidth: '640px',
    }"
  >
    <n-form-item v-if="objType == 1" label="员工:" ref="typeNameRef" path="objName">
      <n-input
        @click="
          () => {
            currentAction.action == ACTION.ADD ? showSelectUserModel() : showSelectUserRadioModel();
          }
        "
        v-model:value="modelForm.objName"
        readonly="true"
        placeholder="请从组织架构选择员工"
      />
      <n-button
        @click="
          () => {
            currentAction.action == ACTION.ADD ? showSelectUserModel() : showSelectUserRadioModel();
          }
        "
        style="margin-left: 10px"
        :disabled="currentAction.action == ACTION.DETAIL"
        >选择</n-button
      >
    </n-form-item>
    <n-form-item v-if="[2, 3].includes(objType)" :label="`${objType == 2 ? '部门' : '单位'}:`" path="objIds">
      <n-tree-select
        v-if="currentAction.action == ACTION.ADD"
        v-model:value="modelForm.objIds"
        multiple
        checkable
        :options="treeDataList"
        check-strategy="all"
        label-field="treeName"
        key-field="id"
        children-field="children"
        :filterable="true"
        placeholder="请选择"
      />
      <n-tree-select
        v-else
        v-model:value="modelForm.objId"
        :options="treeDataList"
        label-field="treeName"
        key-field="id"
        children-field="children"
        :filterable="true"
        placeholder="请选择"
      />
    </n-form-item>
    <n-form-item path="rewardPunishmentTypeId" :label="`${category == 1 ? '奖励' : '惩罚'}类型:`">
      <n-select
        placeholder="请选择"
        v-model:value="modelForm.rewardPunishmentTypeId"
        clearable
        label-field="rewardPunishmentTypeName"
        value-field="id"
        :options="configData"
      />
    </n-form-item>
    <n-form-item path="thing" :label="`${category == 1 ? '奖励物品/金额' : '惩罚程度'}:`">
      <n-input v-model:value="modelForm.thing" maxlength="30" show-count clearable placeholder="请输入" />
    </n-form-item>
    <n-form-item path="standard" :label="`${category == 1 ? '奖励标准' : '惩罚事由'}:`">
      <n-input v-model:value="modelForm.standard" maxlength="50" show-count clearable placeholder="请输入" />
    </n-form-item>
    <n-form-item path="rewardPunishmentStatus" :label="`${category == 1 ? '奖励' : '惩罚'}状态:`">
      <n-select
        placeholder="请选择"
        v-model:value="modelForm.rewardPunishmentStatus"
        clearable
        :options="giveTypeList(category)"
      />
    </n-form-item>
    <n-form-item path="occurTime" :label="`${category == 1 ? '奖励' : '惩罚'}日期:`">
      <n-date-picker
        style="width: 100%"
        v-model:formatted-value="modelForm.occurTime"
        value-format="yyyy-MM-dd"
        type="date"
        clearable
        placeholder="请选择"
      />
    </n-form-item>
    <n-form-item path="fileUrlList" :label="`${category == 1 ? '奖励' : '惩罚'}文件:`">
      <file-upload
        :mode="currentAction.action == ACTION.DETAIL ? 'detail' : 'edit'"
        :data="fileUrlList"
        @removee="handleRemove2"
        @update="handleImgUpdate2"
        :size="30"
        :max="3"
        accept=".doc,.docx,.pdf"
        :disabled1="currentAction.action == ACTION.DETAIL"
        tips="支持上传3个文件，支持doc/docx、pdf等格式，大小不超过30MB"
      ></file-upload>
    </n-form-item>
    <n-form-item path="pictureUrlList" label="现场照片:">
      <file-upload
        :mode="currentAction.action == ACTION.DETAIL ? 'detail' : 'edit'"
        :data="pictureUrlList"
        @removee="handleRemove"
        @update="handleImgUpdate"
        :max="5"
        :size="10"
        accept=".jpeg,.png,.jpg"
        :disabled1="currentAction.action == ACTION.DETAIL"
        tips="支持扩展名：.jpeg,.png,.jpg"
      ></file-upload>
    </n-form-item>
  </n-form>
  <!-- 选择员工  新增多选 -->
  <selectUser
    ref="selectUserRef"
    :parentOrgCode="orgCode"
    @getPersonManageData="getPersonManageData"
    v-model:show="selectUserShow"
    @close="selectUserShow = false"
    :userConfigFeid="true"
    @selectUserData="selectUserData"
    :useArray="modelForm.objIds"
  />
  <!-- 选择员工  编辑单选 -->
  <selectUserRadio
    ref="selectUserRadioRef"
    v-model:showModal="isShowAside3"
    @success="getPersonManageRadioData"
    @close="isShowAside3 = false"
  ></selectUserRadio>
</template>

<script lang="ts" setup>
import { useStore } from '@/store';
import { ref, onMounted } from 'vue';
import { IUploadRes } from '@/components/upload/type';
import { useMessage } from 'naive-ui';
import {
  recordEdit,
  recordAdd,
  getRewardPunishmentTypeList,
  getPersonManageList,
  getRecordInfo,
} from '@/views/rewarpunishmentrecord/fetchData.ts';
import { FileUpload } from '@/components/upload';
import { giveTypeList } from '@/views/rewarpunishmentrecord/comp/constant';
import selectUser from '@/components/select-user/Select-user.vue';
import { ACTION } from '@/views/rewarpunishmentrecord/constant.ts';
import selectUserRadio from '@/components/select-user/Select-user-radio.vue';

const pictureUrlList = ref<any[]>([]);
const fileUrlList = ref<any[]>([]);
const store = useStore();
const props = defineProps({
  // 人员 部门 单位
  objType: {
    type: Number,
    default: 1,
  },
  // 奖励记录/惩罚记录
  category: {
    type: Number,
    default: 1,
  },
  treeData: {
    type: Array,
    default: () => [],
  },
  currentAction: {
    type: Object,
    default: () => {},
  },
});
const treeDataList =
  props.objType == 2 ? disableNodesByOrgType(props.treeData, ['1', '2']) : disableNodesByOrgType(props.treeData, ['0']);
const emits = defineEmits(['closeModal']);
const configData = ref([]); //奖励类型
// 人员--多选
const selectUserRef = ref();
const selectUserShow = ref(false);
const isShowAside3 = ref(false);
const userList: any = ref([]);
const selectUserRadioRef = ref();
const objNames = ['员工', '部门', '单位'];

const modelForm = ref({
  id: '',
  objId: '',
  objType: props.objType,
  category: props.category,
  objName: '',
  rewardPunishmentTypeId: null,
  thing: '',
  standard: '',
  rewardPunishmentStatus: null,
  occurTime: null,
  fileUrlList: [],
  pictureUrlList: [],
  objIds: [],
});
const orgCode = ref(store.userInfo.unitId);
const formRef = ref();
const message = useMessage();
// 规则
const rules: any = {
  rewardPunishmentTypeId: [
    {
      required: true,
      message: `请选择${props.category == 1 ? '奖励' : '惩罚'}类型`,
      trigger: ['blur', 'change'],
    },
  ],
  rewardPunishmentStatus: [
    {
      type: 'number',
      required: true,
      message: `请选择${props.category == 1 ? '奖励' : '惩罚'}状态`,
      trigger: ['blur', 'change'],
    },
  ],
  objName: [
    {
      required: true,
      message: `请选择${objNames[props.objType - 1]}`,
      trigger: ['blur', 'change'],
    },
  ],
  objIds: [
    {
      type: 'array',
      required: true,
      message: `请选择${objNames[props.objType - 1]}`,
      trigger: ['change'],
    },
  ],
};

/**
 * 根据 orgType 数组禁用节点
 * @param {Array} treeData 树形数据源
 * @param {Array} orgTypes 需要禁用的 orgType 值数组
 * @returns {Array} 处理后的树形数据
 */
function disableNodesByOrgType(treeData: any, orgTypes: any) {
  return treeData.map((node: any) => {
    // 如果当前节点的 orgType 在传入的数组中，则禁用该节点
    if (orgTypes.includes(node.attributes.orgType)) {
      node.disabled = true;
    } else {
      node.disabled = false;
    }
    // 递归处理子节点
    if (node.children && node.children.length > 0) {
      node.children = disableNodesByOrgType(node.children, orgTypes);
    }
    return node;
  });
}
// 删除回调
function handleRemove(data: any) {
  modelForm.value.pictureUrlList = modelForm.value.pictureUrlList.filter((item: any) => {
    if (item.id !== data.id) {
      return item;
    }
  });
}

// 删除回调
function handleRemove2(data: any) {
  modelForm.value.fileUrlList = modelForm.value.fileUrlList.filter((item: any) => {
    if (item.id !== data.id) {
      return item;
    }
  });
}
// 上传回调
function handleImgUpdate2(res: IUploadRes[]) {
  if (!res || !res.length) return;

  const result: any = res.filter((item) => item !== null).map((item) => ({ address: item }));

  // 使用 concat 拼接到 attachments
  modelForm.value.fileUrlList = modelForm.value.fileUrlList.concat(result);

  // 过滤掉空的附件
  modelForm.value.fileUrlList = modelForm.value.fileUrlList.filter(
    (item: any) => item && item.address // 这里检查 item 是否有效
  );
  const uniqueAddresses = new Set();
  modelForm.value.fileUrlList = modelForm.value.fileUrlList.filter((item: any) => {
    const isDuplicate = uniqueAddresses.has(item.address);
    uniqueAddresses.add(item.address);
    return !isDuplicate; // 返回 false 以删除重复项
  });
}
// 上传回调
function handleImgUpdate(res: IUploadRes[]) {
  if (!res || !res.length) return;

  const result: any = res.filter((item) => item !== null).map((item) => ({ address: item }));

  // 使用 concat 拼接到 attachments
  modelForm.value.pictureUrlList = modelForm.value.pictureUrlList.concat(result);

  // 过滤掉空的附件
  modelForm.value.pictureUrlList = modelForm.value.pictureUrlList.filter(
    (item: any) => item && item.address // 这里检查 item 是否有效
  );
  const uniqueAddresses = new Set();
  modelForm.value.pictureUrlList = modelForm.value.pictureUrlList.filter((item: any) => {
    const isDuplicate = uniqueAddresses.has(item.address);
    uniqueAddresses.add(item.address);
    return !isDuplicate; // 返回 false 以删除重复项
  });
}
function getRewardPunishmentTypeListApi() {
  getRewardPunishmentTypeList({
    category: props.category,
    pageNo: 1,
    pageSize: -1,
  }).then((res: any) => {
    if (res.code != 'success') return;
    configData.value = res.data.rows || [];
  });
}

function objNameChange(id: string) {
  const treeName = findTreeNameById(props.treeData[0], id);
  if (treeName) {
    modelForm.value.objName = treeName;
    console.log('id>>>', modelForm.value.objName);
  } else {
    console.warn(`No treeName found for id: ${id}`);
  }
}

function findTreeNameById(node: any, targetId: string): string | null {
  // 如果当前节点的 id 匹配目标 id，返回其 treeName
  if (node.id === targetId) {
    return node.text;
  }

  // 遍历当前节点的子节点
  if (node.children && Array.isArray(node.children)) {
    for (const child of node.children) {
      const result = findTreeNameById(child, targetId);
      if (result) {
        return result;
      }
    }
  }

  // 如果没有找到匹配的节点，返回 null
  return null;
}
// 单选人员选中数据处理
const getPersonManageRadioData = (arr: any) => {
  if (arr && arr.length) {
    let { userName, id, unitName, unitId, deptId } = arr[0];
    modelForm.value.objId = id;
    modelForm.value.objName = userName;
    userList.value = [
      {
        id,
        userName,
        unitId,
        unitName,
        deptId,
      },
    ];
  }
};
// 单选人员
const showSelectUserRadioModel = () => {
  if (userList.value && userList.value.length) {
    isShowAside3.value = true;
    selectUserRadioRef.value.getList(userList.value);
  } else {
    let { id, name, unitId, deptId } = modelForm.value.objIdsOriginData[0];
    userList.value = [
      {
        userId: id || modelForm.value.objId,
        unitId: unitId,
        deptId: deptId,
        userName: name,
      },
    ];
    isShowAside3.value = true;
    selectUserRadioRef.value.getList(userList.value);
  }
};

// 多选人员
const showSelectUserModel = () => {
  selectUserShow.value = true;
  selectUserRef.value.getList(modelForm.value.objIds);
};
// 获取人员列表
async function getPersonManageData(params: any) {
  let res = await getPersonManageList({ ...params, type: 1 });
  selectUserRef.value.renderTable(res);
}
const typeNameRef: any = ref(null);
const selectUserData = (arr: any, levelCode: any) => {
  modelForm.value.objIds = arr;
  modelForm.value.objIdsOriginData = arr;
  modelForm.value.objName = arr.map((item: any) => item.userName).join(',');
};

const ACTION_ADD = ACTION.ADD; // 假设 ACTION 是一个已定义的对象

function transformObjIds(objIds: any[], objType: number, category: number): any[] {
  if ([2, 3].includes(objType)) {
    // 匹配对应的objName
    objNameChange(modelForm.value.objId);
    console.log('objNameChange>>>', modelForm.value.objName);
    return objIds.map((item) => ({
      id: item,
      name: '',
      unitId: item.unitId || '',
      deptId: item.deptId || '',
    }));
  } else if ([1].includes(objType)) {
    return objIds.map((item) => ({
      id: item.id || item.userId,
      name: item.userName,
      unitId: item.unitId || '',
      deptId: item.deptId || '',
    }));
  } else if ([2, 3].includes(category)) {
    if ([1].includes(category)) {
      return userList.value;
    } else {
      return [{ id: objIds, name: '' }];
    }
  }
  return []; // 默认返回空数组，或者根据需求调整
}

async function handleApiRequest(modelForm: any, newObjIds: any[], isAdd: boolean): Promise<any> {
  try {
    const apiFunc = isAdd ? recordAdd : recordEdit;
    const response = await apiFunc({ ...modelForm, objIds: newObjIds });
    return response.code;
  } catch (error) {
    console.error('API request error:', error);
    return 'error'; // 或者其他错误代码
  }
}

function handleSubmit() {
  formRef.value.validate(async (errors: any) => {
    if (!errors) {
      const isAdd = props.currentAction.action === ACTION_ADD;
      const newObjIds = transformObjIds(
        isAdd ? modelForm.value.objIds : [modelForm.value.objId],
        props.objType,
        props.category
      );

      const code = await handleApiRequest(modelForm.value, newObjIds, isAdd);

      if (code === 'success') {
        message.success(isAdd ? '新增成功' : '编辑成功');
        userList.value = [];
        emits('closeModal');
      }
    }
  });
}
function getDetail(id: any) {
  getRecordInfo({
    id,
  }).then((res: any) => {
    if (res.code == 'success') {
      modelForm.value = {
        ...res.data,
        occurTime: res.data.occurTime || null,
        objIds: res.data.objIds.map((item: any) => item.id),
        pictureUrlList: res.data.pictureUrlList || [],
        fileUrlList: res.data.fileUrlList || [],
        objIdsOriginData: res.data.objIds,
      };
      pictureUrlList.value = res.data.pictureUrlList.map((item: any) => {
        const fileName = item.address.split('/').pop();
        return {
          ...item,
          name: fileName,
        };
      });
      fileUrlList.value = res.data.fileUrlList.map((item: any) => {
        const fileName = item.address.split('/').pop();
        return {
          ...item,
          name: fileName,
        };
      });
    }
  });
}

getRewardPunishmentTypeListApi(); //获取奖励类型
defineExpose({
  handleSubmit,
});

onMounted(() => {
  if (props.currentAction.action != ACTION.ADD) {
    getDetail(props.currentAction.data.id);
  }
});
defineOptions({ name: 'rewarpunishmentrecordEdit' });
</script>

<style module lang="scss"></style>
