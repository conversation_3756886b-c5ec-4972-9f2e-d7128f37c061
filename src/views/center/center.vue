<!--
 * @Author: xginger <EMAIL>
 * @Date: 2025-06-03 14:10:39
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-06-19 18:06:45
 * @FilePath: \ehs-org-alloc-mgr\src\views\center\center.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="load">
    <span class="sp">加载中。。。</span>
    <n-spin :size="500" stroke="#517efe" />
  </div>
</template>

<script setup lang="ts">
// console.log(menuDataList);
import { useMenu } from '@/views/menu/useMenu';
import { ref, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useStore } from '@/store';
import { Address } from './featchData';
const router: any = useRouter();
// 先获取地址栏中code值
const url = 'code';
// 登录
const route = useRoute();
const store = useStore();
const extractedValue: any = ref('');
// const newToken = route.query.token;
// console.log(newToken, '.>>>>>>>>>>>>>>>>>>>>.');
// // router.push({ name: 'personManage' });
// if (newToken) {
//   extractedValue.value = newToken.replace(/^['"]|['"]$/g, ''); // ��理引号
//   // console.log(extractedValue.value);
//   // store.login({
//   //   sysCode: 'org_person_web',
//   //   code: extractedValue.value,
//   // });
// }
// onMounted(() => {
//   // 登录后从pinia取出code判断
// });

watch(
  () => route.query, // 监控路由查询参数的变化
  (newQuery) => {
    const newToken = newQuery.token;

    if (newToken) {
      extractedValue.value = newToken.replace(/^['"]|['"]$/g, ''); // 清理引号
      console.log(111, '>>>>>');
      // console.log(extractedValue.value);

      store.login(
        { sysCode: 'org_person_web', code: extractedValue.value },
        (routerName: any) => {
          Address()
            .then((res: any) => {
              window.sessionStorage.setItem(
                'ehs-org-alloc-mgr-address',
                res.data
              );
              window.sessionStorage.setItem(
                'ehs-org-alloc-mgr-token',
                newToken
              );
            })
            .finally(() => {
              router.push({ name: routerName });
            });
        }
      );
    }
  },
  { immediate: true } // 安全起见，立即调用一次
);
defineOptions({ name: 'centerIndex' });
</script>

<style scoped>
.load {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  .sp {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 25px;
  }
}
</style>
