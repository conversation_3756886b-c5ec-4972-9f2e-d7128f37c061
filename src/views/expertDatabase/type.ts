import { ACTION } from './constant';
import type { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 分页列表数据
export interface IPageData {
  /**
   * 行政区划编码
   */
  areaCode: string;
  /**
   * 行政区划名称
   */
  areaName: string;
  createdBy: string;
  createdTime: string;
  id: string;
  isIot: string;
  /**
   * 是否建设物联网
   */
  isIotName: string;
  jurisdiction: string;
  /**
   * 被管辖范围
   */
  jurisdictionName: string;
  /**
   * 单位名称
   */
  unitName: string;
  unittype: string;
  /**
   * 单位类型中文
   */
  unitTypeName: string;
  updatedBy: string;
  updatedTime: string;
}
export type IPageDataRes = IPageRes<IPageData>;

export interface IDetail {
  areaCode: string;
  areaName: string;
  createdBy: string;
  createdTime: string;
  /**
   * 主键ID
   */
  id: string;
  isIot: string;
  /**
   * 单位辖区范围
   */
  jurisdiction: string;
  unitName: string;
  unitType: string;
  updatedBy: string;
  updatedTime: string;
}
export interface PageModel {
  pageNo?: number;
  pages?: number;
  pageSize?: number;
  rows?: { [key: string]: any }[];
  total?: number;
  [property: string]: any;
}
export interface OrgTree {
  /**
   * 节点属性
   */
  attributes?: { [key: string]: any };
  /**
   * 点是否被选中
   */
  checked?: boolean;
  /**
   * 节点的子节点
   */
  children?: OrgTree[];
  hasChildren?: boolean;
  hasParent?: boolean;
  /**
   * 主键id
   */
  id?: string;
  /**
   * 层级
   */
  level?: number;
  /**
   * 父ID
   */
  parentId?: string;
  state?: string;
  /**
   * 节点名称
   */
  text?: string;
  /**
   * 树名
   */
  treeName?: string;
  /**
   * 节点类型
   */
  type?: string;
  /**
   * 节点id
   */
  typeId?: string;
  [property: string]: any;
}
