<template>
  <n-form :show-feedback="false" label-placement="left">
    <div class="flex justify-between">
      <span></span>
      <div class="w-[12%] flex justify-end">
        <n-button type="primary" @click="doHandle(ACTION.ADD)">
          {{ ACTION_LABEL.ADD }}
        </n-button>
      </div>
    </div>
  </n-form>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { FlFilledAdd as IconAdd } from '@kalimahapps/vue-icons';
import { ACTION, ACTION_LABEL } from '../constant';
import { trimObjNull } from '@/utils/obj.ts';

const emits = defineEmits(['action']);

const filterForm = ref(initForm());
function initForm() {
  return { templateName: '' };
}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: trimObjNull(filterForm.value),
  });
}

watch(filterForm.value, () => {
  doHandle(ACTION.SEARCH);
});

onMounted(() => {
  doHandle(ACTION.SEARCH);
});

defineOptions({ name: 'checkTempFilterComp' });
</script>
<style module lang="scss">
.n-form {
  border-radius: 10px !important;
}
</style>
