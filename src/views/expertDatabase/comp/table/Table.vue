<template>
  <div class="box">
    <n-data-table
      class="h-full com-table"
      :loading="loading"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :pagination="pagination"
      :render-cell="useEmptyCell"
      :theme-overrides="themeOverrides"
    />
  </div>
</template>

<script lang="ts" setup>
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { useStore } from '@/store';
import { IObj } from '@/types';
import mittBus from '@/utils/mittBus';
import { DataTableColumns, NButton, useMessage } from 'naive-ui';
import { h, ref, VNode } from 'vue';
import { cols } from '../../comp/table/columns';
import { ACTION_LABEL } from '../../constant';
import { delExpert, getExportData } from '../../fetchData';

const store = useStore();
const message = useMessage();
const emits = defineEmits(['action', 'getTable']);
const props: any = defineProps({
  treeId: {
    type: String,
    default: '',
  },
  treeLevelCode: {
    type: String,
    default: '',
  },
});
// 表格样式
const themeOverrides = {
  tdColorStriped: '#dfeefc',
  thColor: '#BBCCF3',
  thTextColor: '#222',
  // tdColorHover: 'rgba(18, 83, 123, 0.35)',
  tdColorStripedModal: 'red',
  // tdColorHoverModal: 'rgba(18, 83, 123, 0.35)',
  // tdColorHoverPopover: 'rgba(18, 83, 123, 0.35)',
};
mittBus.on('addUpdate', (v: any) => {
  pagination.page = v;
});
const [loading, search] = useAutoLoading(true);
const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData);

let filterData: IObj<any> = {}; // 搜索条件

function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    unitId: props.treeId,
    levelCode: props.treeLevelCode,
    ...filterData,
  };
  console.log(params, 'params=======');
  search(getExportData(params)).then((res) => {
    tableData.value = res.data.rows || [];
    emits('getTable', tableData.value);
    if (tableData.value.length == 0) {
    }
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

function setColumns() {
  columns.value.push(...cols);

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 290,
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-del-button ',
          disabled: row.createUnitId != store.userInfo.orgCode,
          onClick: () =>
            // emits("action", { action: ACTION.EDIT, data: toRaw(row) }),
            handleDelete(row),
        },
        { default: () => ACTION_LABEL.DELETE }
      ),
    ],
  ];

  return useActionDivider(acList);
}
// 移除
function handleDelete(row: any) {
  $dialog.error({
    title: '移除',
    content: '确定移除吗?',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      let res: any = await delExpert({
        id: row.id,
      });
      if (res.code == 200) {
        if (tableData.value.length - 1 == 0) {
          pagination.page = pagination.page - 1;
        }
        getTableData();
        message.success('移除成功');
      }
    },
  });
}

// on created
setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
});

defineOptions({ name: 'ExpertDatabaseTable' });
</script>

<style module lang="scss"></style>
