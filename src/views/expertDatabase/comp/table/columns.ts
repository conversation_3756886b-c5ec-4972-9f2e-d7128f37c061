import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '姓名',
    key: 'name',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },

  {
    title: '手机号',
    key: 'phone',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '单位名称',
    key: 'unit_name',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '部门',
    key: 'dept',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '岗位',
    key: 'post',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
];
