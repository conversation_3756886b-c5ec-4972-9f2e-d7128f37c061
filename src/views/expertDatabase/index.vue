<!-- <template>
  <div class="com-g-row-a1">
    <com-bread :data="breadData"></com-bread>
    <div class="com-g-row-a1">
      <filter-comp class="com-table-filter" style="border-radius: 5px 5px 0 0" @action="actionFn" />
      <table-comp
        class="com-table-container"
        style="border-radius: 0 0 5px 5px"
        ref="tableCompRef"
        @action="actionFn"
      />
      <selectUser
        ref="selectUserRef"
        :parentOrgCode="orgCode"
        @getPersonManageData="getTreeDataPerson1"
        v-model:show="isShowAside"
        :title="actionLabel"
        @close="isShowAside = false"
        @selectUserData="handleAdd"
      ></selectUser>
    </div>
  </div>
</template> -->
<template>
  <div class="">
    <!-- <div class="head_title" style="margin-top: 10px; margin-bottom: 15px; margin-left: 5px">安全职责</div> -->
    <com-bread :data="breadData"></com-bread>
    <!-- h-[calc(100%-46px)] -->
    <div class="flex h-[calc(100%-0px)]">
      <transition name="slide-fade">
        <div class="h-[calc(100%)]" v-show="formVisible">
          <comTree class="tree" @handelChange="treeChange" :dataList="treeData" @treeChange="treeChange"></comTree>
        </div>
      </transition>

      <div class="!ml-[15px] bg-[#eef7ff]" style="border-radius: 5px; position: relative">
        <img
          @click="formVisible = !formVisible"
          src="@/assets/open.png"
          style="position: absolute; left: -1%; top: 47.6%; width: 30px; cursor: pointer; z-index: 99"
        />
        <filter-comp class="com-table-filter" style="border-radius: 5px 5px 0 0" @action="actionFn" />
        <table-comp
          class="com-table-container h-[calc(100%-82px)]"
          style="border-radius: 0 0 5px 5px"
          ref="tableCompRef"
          :treeId="treeId"
          :treeLevelCode="treeLevelCode"
          @action="actionFn"
        />
        <selectUser
          ref="selectUserRef"
          :parentOrgCode="orgCode"
          @getPersonManageData="getTreeDataPerson1"
          v-model:show="isShowAside"
          :title="actionLabel"
          @close="isShowAside = false"
          @selectUserData="handleAdd"
        >
        </selectUser>
        <!-- <Table
          class="com-table-container h-[calc(100%-58px)]"
          :code="treeLevelCode"
          ref="tableCompRef"
          @action="actionFn"
        /> -->
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
// import AsideComp from './comp/aside/index.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import selectUser from '@/components/select-user/Select-user.vue';
import comTree from '@/components/tree/comTree.vue';
import { useStore } from '@/store/index';
import mittBus from '@/utils/mittBus';
import { TreeOption, useMessage } from 'naive-ui';
import { computed, onMounted, provide, Ref, ref } from 'vue';
import FilterComp from './comp/Filter.vue';
import TableComp from './comp/table/Table.vue';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from './constant';
import { addExpert, getOrgTrees, getPersonManageList } from './fetchData';
import type { IActionData } from './type';
const breadData: any[] = [{ name: '专家库' }];
const message = useMessage();
const store = useStore();
const selectUserRef: any = ref(null);
// const breadData: IBreadData[] = [{ name: '配置管理' }, { name: '管辖范围配置' }];
const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);
const tableCompRef = ref();
const isShowAside = ref(false);
const orgCode = ref(store.userInfo.unitId);
const formVisible = ref(true);
// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

// 组织树结构
const treeData = ref([]);
const treeId: any = ref(null);
const treeLevelCode: any = ref(null);

function actionFn(val: IActionData) {
  currentAction.value = val;
  console.log(val, 'val======');
  if (val.action === ACTION.SEARCH) {
    // handleSearch(val.data);
  } else {
    isShowAside.value = val.action === ACTION.ADD;
    // selectUserRef.value.getTreeAndTable();
  }
}
async function getTreeDataPerson1(params: any) {
  let res = await getPersonManageList(params);

  selectUserRef.value.renderTable(res);
}
function handleSearch(data?: Record<string, any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

const handleAdd = async (arr: any, code: any) => {
  let newArr = arr.map((item: any) => {
    return {
      unit: item.unitName,
      dept: item.deptName,
      name: item.userName,
      phone: item.userTelphone,
      post: item.postName,
      ...item,
    };
  });
  let res: any = await addExpert({ expertList: newArr, levelCode: code });
  if (res.code == 200) {
    mittBus.emit('addUpdate', 1);

    tableCompRef.value?.getTableData();

    message.success('新增成功');
  }
};

// 获取组织树
function QueryOrgTrees() {
  const params = {
    orgCode: store.userInfo.unitId,
    needChildUnit: '1',
    needself: '1',
  };
  getOrgTrees(params).then((res: any) => {
    if (res.code != 200) return;
    const _RES: any = removeEmptyChildren(res.data);
    treeData.value = _RES;
    // }
  });
}
// 递归函数，使用map生成新的数组
function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };

    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }

    return newItem; // 返回新对象
  });
}

onMounted(() => {
  QueryOrgTrees();
  // handleSearch();
});
// 点击数获取数据
const treeChange = (v: TreeOption) => {
  orgCode.value = v.id;
  treeId.value = v.id;
  treeLevelCode.value = v.levelCode;
  if (v.levelCode && v.id) {
    handleSearch({ levelCode: v.levelCode, unitId: v.id });
  }
};

defineOptions({ name: 'DutyEvaluationIndex' });
</script>

<style module lang="scss"></style>
