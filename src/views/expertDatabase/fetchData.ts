import type { IDetail, IPageDataRes, PageModel, OrgTree } from './type';
import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';

// 获取分页
export function pageData(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.jurisdictionPageList, query);
  return $http.get<IPageDataRes>(url, { data: { _cfg: { showTip: true } } });
}

// 获取详情
export function getDetail(id: string) {
  const url = api.getUrl(api.type.demo, api.name.demo.jurisdictionDetail, {
    id,
  });
  return $http.get<IDetail>(url, { data: { _cfg: { showTip: true } } });
}

// // 更新
// export function postUpdate(data: { id: string; jurisdiction: string }) {
//   const url = api.getUrl(api.type.demo, api.name.demo.jurisdictionUpdate);
//   return $http.post(url, {
//     data: { _cfg: { showTip: true, showOkTip: true }, ...data },
//   });
// }

// 获取专家库列表数据
export function getExportData(query: IObj<any>) {
  const url = api.getUrl(api.type.server, api.name.interface.getExpertList, query);
  return $http.get<PageModel>(url, { data: { _cfg: { showTip: false } } });
}

// 删除专家库数据
export function delExpert(data: { id: string }) {
  const url = api.getUrl(api.type.server, api.name.interface.delExpertList);
  return $http.delete<any>(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...data },
  });
}
// 新增专家库 addExpertList
export function addExpert(data: any) {
  const url = api.getUrl(api.type.server, api.name.interface.addExpertList);
  return $http.post<any>(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...data },
  });
}
// 获取人员列表
export function getPersonManageList(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getExpertPersonManageList);
  console.log(url, '>>>>>>>>>>>>>>>>>>>>.');
  return $http.post<IPageDataRes>(url, {
    data: { _cfg: { showTip: true }, ...params },
  });
}

// 获取树形结构
export function getOrgTrees(query: IObj<any>) {
  return $http.get<OrgTree>(api.getUrl(api.type.server, api.name.interface.getTreeData, query));
}

// 获取树结构列表
export function getTreeDataPerson(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getTreeDataPerson);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}
// 获取人员组织书
