import type { IDetail, IPageDataRes, OrgTree } from './type';
import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';

// 获取分页
export function pageData(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.jurisdictionPageList, query);
  return $http.get<IPageDataRes>(url, { data: { _cfg: { showTip: true } } });
}

// 获取详情
export function getDetail(id: string) {
  const url = api.getUrl(api.type.demo, api.name.demo.jurisdictionDetail, {
    id,
  });
  return $http.get<IDetail>(url, { data: { _cfg: { showTip: true } } });
}
// 安全会议删除
export function meetingDel(id: string) {
  const url = api.getUrl(api.type.server, api.name.interface.meetingDel, {
    id,
  });
  return $http.get<IDetail>(url, { data: { _cfg: { showTip: true } } });
}

// 更新
export function postUpdate(data: { id: string; jurisdiction: string }) {
  const url = api.getUrl(api.type.demo, api.name.demo.jurisdictionUpdate);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...data },
  });
}
// 获取树形结构
export function getOrgTrees(query: IObj<any>) {
  return $http.get<OrgTree>(api.getUrl(api.type.server, api.name.interface.getTreeData, query));
}

export function endMeeting(query: IObj<any>) {
  return $http.get<any>(api.getUrl(api.type.server, api.name.interface.mettingEnd, query));
}

export function batchDelete(data: any) {
  const url = api.getUrl(api.type.server, api.name.interface.batchDelete, data);
  return $http.post<IDetail>(url, {
    data: { _cfg: { showTip: true } },
  });
}
// 获取树结构列表
export function getTreeDataPerson(data: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getMettingList);
  return $http.post<IDetail>(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
// 获取树结构列表
export function getTreeDataPerson2(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getTreeDataPerson);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}
// 删除安全会议
export function delMetting(id: string) {
  const url = api.getUrl(api.type.server, api.name.interface.meetingDel, {
    id,
  });
  return $http.delete<any>(url, {
    data: { _cfg: { showTip: true, showOkTip: false } },
  });
}
// 获取安委会列表
export function getSafeTyData(data: object) {
  const url = api.getUrl(api.type.server, api.name.interface.getSafeTyList, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...data },
  });
}
// 安全列表新增
// export function addMeetingData(params: any) {
//   const url = api.getUrl(api.type.server, api.name.interface.addMeetingList);
//   return $http.post(url, {
//     data: { _cfg: { showTip: true, showOkTip: false }, ...params },
//   });
// }
// 修改列表
// export function editMeetingData(params: any) {
//   const url = api.getUrl(api.type.server, api.name.interface.editMeetingList);
//   return $http.post(url, {
//     data: { _cfg: { showTip: true, showOkTip: false }, ...params },
//   });
// }
// 详情
// export function detailMeetingData(id: any) {
//   const url = api.getUrl(api.type.server, api.name.interface.getMeetingDetail, {
//     id,
//   });
//   return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
// }

/**
 * 获取签到表
 * @param params
 */
export function getMeetingSignList(params: any) {
  const url = api.getUrl(api.type.server, api.name.server.getMeetingSignList, params);
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}

/**
 * 获取详情
 * @param params
 */
export function getMeetingDetail(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getMeetingDetail, params);
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}
//获取模板
export function downloadTemplateUrl(type: string) {
  const url = api.getUrl(api.type.server, api.name.server.downMeetingTemplate, { type });
  return url;
}
//新增
export function meetingAdd(params: any) {
  const url = api.getUrl(api.type.server, api.name.server.meetingAdd);

  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}
//编辑
export function meetingEdit(params: any) {
  const url = api.getUrl(api.type.server, api.name.server.meetingUpdate);

  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}
//编辑-参会信息
export function meetingUpdateCHMX(params: any) {
  const url = api.getUrl(api.type.server, api.name.server.meetingUpdateCHMX);

  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}

// detail
export function getDetailFn(id: string) {
  const url = api.getUrl(api.type.server, api.name.server.meetingDetail, { id });
  return $http.get<any>(url, {
    data: { _cfg: { showTip: true, showOkTip: false } },
  });
}

// 查询参会人 分页
export function getMeetingUserTable(params: any) {
  const url = api.getUrl(api.type.server, api.name.server.getMeetingSignPage, { ...params });
  return $http.get(url, {
    data: { _cfg: { showTip: true, showOkTip: false } },
  });
}

// 导出详情
export function exportMeetingDetail(meetingId: string) {
  const url = api.getUrl(api.type.server, api.name.server.exportMeetingDetail, { meetingId });
  return url;
}
