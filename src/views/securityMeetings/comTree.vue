<template>
  <div
    class="bg-[#EEF7FF] w-[323px] h-full"
    style="border-radius: 5px; border-right: 1px solid #eee"
  >
    <!-- 采用menu的形式 可以自由选择-->
    <!-- <n-menu :options="menuOptions" :default-expanded-keys="defaultExpandedKeys" accordion default-value="test1" /> -->
    <!-- 采用表单树的形式 -->
    <!-- <h3 class="p-4 pb-[0]"></h3> -->
    <div class="flex justify-start items-center mb-4 pt-5 pl-5">
      <img src="@/assets/icon.png" style="width: 18px" alt="" />
      <div style="font-size: 16px; font-weight: 600; margin-left: 10px">
        所属单位
      </div>
    </div>
    <div style="padding-left: 15px; padding-right: 15px; margin-bottom: 5px">
      <n-input v-model:value="pattern" placeholder="搜索" clearable>
        <template #suffix>
          <BySearch :class="$style['icon']" />
        </template>
      </n-input>
    </div>
    <n-scrollbar style="height: calc(100vh - 211px)">
      <n-tree
        ref="treeRef"
        class="px-4"
        :data="treeData"
        :pattern="pattern"
        block-line
        key-field="id"
        label-field="text"
        :cancelable="false"
        :show-irrelevant-nodes="false"
        :render-label="renderLabel"
        :render-switcher-icon="renderSwitcherIconWithExpaned"
        :theme-overrides="themeOverrides"
        :default-expanded-keys="defaultExpandedkeys"
        :default-selected-keys="defaultSelectedkeys"
        :override-default-node-click-behavior="override"
      />
    </n-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, h, reactive, watch, ref, nextTick } from 'vue';
// import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
// import type { MenuOption } from 'naive-ui';
import type { Component } from 'vue';
import { NIcon, TreeOption } from 'naive-ui';
import {
  CaDataStructured as structure,
  CaEnterprise as unit,
} from '@kalimahapps/vue-icons';
import { BySearch } from '@kalimahapps/vue-icons';
import { getTreeDataPerson } from './fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading';
import { icon_chevron as iconChevronForward } from './assets/index';
import Dept from './assets/dept.png';
import YeWu from './assets/yewu.png';
import JianGuan from './assets/jianguan.png';
import YangChang from '@/assets/iconImg/yanchang.png';
import AnGang from '@/assets/iconImg/angang.png';
import WyCh from '@/assets/iconImg/wych2.png';
import Zhongdanong from '@/assets/iconImg/zhongdanong.png';
import { useStore } from '@/store';
const store = useStore();
const [loading, search] = useAutoLoading(true);
// 选中样式
const themeOverrides = {
  nodeHeight: '40px',
};
const pattern = ref('');
function renderSwitcherIconWithExpaned({ option }: any) {
  if (option.root === 1) {
    return null;
  } else {
    if (option.children && option.children.length > 0) {
      return h(
        NIcon,
        {
          style: {
            width: '14px',
            height: '14px',
          },
        },
        { default: () => h(iconChevronForward) }
      );
    }
  }
}
function renderLabel(e: any) {
  // 如果节点没有子节点，不显示展开图标
  if (e.option.children && e.option.children.length === 0) {
    e.option.isLeaf = true;
    return h(
      'div',
      {
        style: {
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          padding: '10px 0 10px 0',
        },
      },
      [
        h('img', {
          style: {
            width: '20px',
            height: '20px',
            marginRight: '5px',
          },
          src:
            e.option.attributes?.orgType === '2'
              ? JianGuan
              : e.option.attributes?.orgType === '1'
                ? YeWu
                : Dept,
        }),
        h(
          'div',
          {
            style: {
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            },
            title: e.option.text,
          },
          e.option.text
        ),
      ]
    );
  } else {
    // 如果节点有子节点，正常显示
    if (e.option.root === 1) {
      // e.option.isLeaf = true;
      return h(
        'div',
        {
          style: {
            display: 'flex',
            alignItems: 'center',
            padding: '10px 0 10px 0',
          },
        },
        [
          h('img', {
            style: {
              width: '16px',
              height: '16px',
              marginRight: '10px',
              // marginLeft: '24px',
            },
            src: store.userInfo.logoPicUrl,
          }),
          h(
            'div',
            {
              style: {
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              },
              title: e.option.text,
            },
            e.option.text
          ),
        ]
      );
    } else {
      return h(
        'div',
        {
          style: {
            display: 'flex',
            alignItems: 'center',
            padding: '10px 0 10px 0',
            marginLeft: '-10px',
          },
        },
        [
          h('img', {
            style: {
              width: '16px',
              height: '16px',
              marginRight: '5px',
            },
            src:
              e.option.attributes?.orgType === '2'
                ? JianGuan
                : e.option.attributes?.orgType === '1'
                  ? YeWu
                  : Dept,
          }),
          h(
            'div',
            {
              style: {
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              },
              title: e.option.text,
            },
            e.option.text
          ),
        ]
      );
    }
  }
}
const emits = defineEmits(['action', 'treeChange']);
const defaultExpandedkeys = ref<string[]>([]);
const defaultSelectedkeys = ref<string[]>([]);
const props = defineProps({
  dataList: {
    type: Array,
    default: () => [],
  },
});

// const [loading, search] = useAutoLoading(false);
// const defaultExpandedKeys = ['test'];
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 15, 20, 30, 50, 100],
  onChange: (page: number) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  },
});
const treeRef = ref(null);
const treeData = ref([]);
watch(
  () => props.dataList,
  (nv) => {
    if (props.dataList?.length) {
      let curKey = props.dataList[0].id as string;
      defaultExpandedkeys.value = [curKey];
      defaultSelectedkeys.value.push(curKey);
      treeData.value = props.dataList;
      treeData.value[0].root = 1;
      nextTick(() => {
        const firstNodeSwitcher = treeRef.value.selfElRef.querySelector(
          '.n-tree-node-switcher'
        );
        if (firstNodeSwitcher) {
          // 设置display为none
          firstNodeSwitcher.style.display = 'none';
        }
      });
      emits('treeChange', props.dataList[0]);
      calleArr(props.dataList);
    }
  },
  { immediate: true }
);
function calleArr(array: Array<any>) {
  var data = array;
  if (data[0].children?.length) {
    calleArr(data[0].children);
  } else {
    // emits('treeChange', data[0]);
    // defaultSelectedkeys.value.push(data[0].id);
    // localStorage.setItem('_riskUnitID', data[0].id);
    // localStorage.setItem('_riskUnitName', data[0].text);
  }
}
function override({ option }: { option: TreeOption }) {
  const op: any = option;
  // console.log("🚀 ~ override ~ option:", option);
  // localStorage.setItem('_riskUnitID', op.id);
  // localStorage.setItem('_riskUnitName', op.text);

  emits('treeChange', option);
}

onMounted(() => {});

defineOptions({ name: 'comTree' });
</script>

<style module lang="scss">
.icon {
  color: #c0c4cc;
}
</style>
