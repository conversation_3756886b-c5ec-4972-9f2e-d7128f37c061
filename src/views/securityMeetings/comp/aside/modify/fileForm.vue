<template>
  <div class="files-form">
    <n-form
      ref="fileFormRef"
      :model="ModifyService.formData"
      label-placement="left"
      label-width="100"
      require-mark-placement="left"
    >
      <n-form-item label="上传现场照片">
        <ImgUpload :data="imgsData" @update="handleImgsUpdate" :size="30" />
      </n-form-item>

      <n-form-item label="上传会议纪要">
        <FileUpload
          class="mt-[-15px]"
          accept=".docx, .pdf,.doc"
          :data="fileData"
          @update="handleFilesUpdate"
          :max="3"
          :size="30"
        />
      </n-form-item>

      <n-form-item label="点击下载会议纪要模板">
        <ul class="tempList">
          <li class="fileTempDown" @click="downloadTemplateClick('1')">会议纪要模板一</li>
          <li class="fileTempDown" @click="downloadTemplateClick('2')">会议纪要模板二</li>
        </ul>
      </n-form-item>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject } from 'vue';
import type { Ref } from 'vue';
import { ModifyService } from './formService';
import { ImgUpload, FileUpload } from '@/components/upload';
import { IUploadRes } from '@/components/upload/type';
import { downloadTemplateUrl, meetingUpdateCHMX, getDetailFn } from '@/views/securityMeetings/fetchData';
import { fileDownloader } from '@/utils/fileDownloader';
import { useStore } from '@/store';
import { $toast } from '@/common/shareContext/useToastCtx';
import { ACTION, PROVIDE_KEY } from '@/views/securityMeetings/constant';
import type { IActionData } from '@/views/securityMeetings/type';

defineOptions({ name: 'SecurityMeetingFiles' });

// inject
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>;
const curRow = computed(() => currentAction.value.data);

const { userInfo } = useStore();

// const isDisabled = computed(() => userInfo.id !== ModifyService.formData.value.noteUserId);

const fileFormRef = ref();
const formData = ref({
  meetingSitePhotoAttachment: [],
  meetingMinutesAttachment: [],
});

const imgsData = ref<any[]>([]);
function handleImgsUpdate(res: IUploadRes[]) {
  if (!res || !res.length) {
    if (formData.value.meetingSitePhotoAttachment.length > 0) {
      formData.value.meetingSitePhotoAttachment = [];
    }
    return;
  }
  const result: any = res.map((item: any) => {
    if (typeof item === 'string') {
      return { address: item };
    } else {
      return {
        ...item,
      };
    }
  });
  formData.value.meetingSitePhotoAttachment = result;
}

const fileData = ref<any[]>([]);
function handleFilesUpdate(res: IUploadRes[]) {
  if (!res || !res.length) {
    if (formData.value.meetingMinutesAttachment.length > 0) {
      formData.value.meetingMinutesAttachment = [];
    }
    return;
  }

  const result: any = res.map((item: any) => {
    if (typeof item === 'string') {
      return { address: item };
    } else {
      return {
        ...item,
      };
    }
  });
  formData.value.meetingMinutesAttachment = result;
}

// 下载模板
async function downloadTemplateClick(type: string) {
  const url = downloadTemplateUrl(type);
  fileDownloader(url);
}

// init page
async function initFormData() {
  try {
    const { code, data } = await getDetailFn(curRow.value.id);
    if (+code === 200) {
      let meetingMinutesAttachment = [];
      if (Array.isArray(data.meetingMinutesAttachment)) {
        meetingMinutesAttachment = data.meetingMinutesAttachment;

        fileData.value = data.meetingMinutesAttachment.map((item) => {
          const fileName = item.address.split('/').pop();
          return {
            ...item,
            name: fileName,
          };
        });
      }
      let meetingSitePhotoAttachment = [];
      if (Array.isArray(data.meetingSitePhotoAttachment)) {
        meetingSitePhotoAttachment = data.meetingSitePhotoAttachment;

        imgsData.value = data.meetingSitePhotoAttachment.map((item) => {
          const fileName = item.address.split('/').pop();
          return {
            ...item,
            name: fileName,
          };
        });
      }

      formData.value = {
        meetingSitePhotoAttachment,
        meetingMinutesAttachment,
      };
    }
  } catch (e) {}
}
initFormData();

// expose-methods
const emits = defineEmits(['action']);

function resetForm() {
  formData.value = {
    meetingSitePhotoAttachment: [],
    meetingMinutesAttachment: [],
  };
  imgsData.value = [];
  fileData.value = [];
}

async function updateSubmit() {
  try {
    const params = Object.assign(
      {
        id: curRow.value.id,
      },
      formData.value
    );

    ModifyService.submitLoading.value = true;

    const { code } = await meetingUpdateCHMX(params);

    ModifyService.submitLoading.value = false;
    if (+code === 200) {
      emits('action', { action: ACTION.UPDATESUCCESS });
      resetForm();
    }
  } catch (e: any) {
    ModifyService.submitLoading.value = false;
    if (e.message) $toast.error(e.message);
  }
}

defineExpose({
  updateSubmit,
  resetForm,
});
</script>

<style scoped lang="scss">
.files-form {
  @apply w-full px-[24px] pt-[24px];
}

.tempList {
  @apply flex flex-col;
  .fileTempDown {
    @apply mb-[15px] text-[#3e62eb] cursor-pointer;
    &:hover {
      color: #6c87eb;
    }
  }
}
</style>
