<template>
  <div class="base-form">
    <n-form
      ref="baseFormRef"
      :model="ModifyService.formData.value"
      label-placement="left"
      label-width="110"
      require-mark-placement="left"
      :rules="formRules"
    >
      <n-form-item label="安全专题会议" path="committeeManagementId" v-show="meetingType === '0'">
        <n-select
          v-model:value="ModifyService.formData.value.committeeManagementId"
          :options="awhOpt"
          label-field="name"
          value-field="id"
          placeholder="请选择安委会"
          clearable
        />
      </n-form-item>

      <n-form-item label="会议名称" path="meetingName">
        <n-input
          v-model:value="ModifyService.formData.value.meetingName"
          placeholder="请输入会议名称"
          show-count
          maxlength="30"
          clearable
        />
      </n-form-item>

      <n-form-item label="会议地点" path="address">
        <n-input
          v-model:value="ModifyService.formData.value.address"
          placeholder="请输入会议地点"
          maxlength="30"
          show-count
          clearable
        />
      </n-form-item>

      <n-form-item label="会议时间" path="meetingStartTime">
        <n-date-picker
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          v-model:formatted-value="dateTimeRange"
          clearable
        />
      </n-form-item>

      <n-form-item ref="meetingPersonRef" label="参会人" path="meetingUserRelList">
        <n-input
          v-model:value="meetingPerson"
          readonly="true"
          placeholder="请从组织架构选择人员"
          @click="toggleUserSelect"
        />
        <n-button @click="toggleUserSelect" style="margin-left: 10px">选择</n-button>
      </n-form-item>

      <n-form-item label="现场记录人员" path="noteUserId">
        <n-select
          v-model:value="ModifyService.formData.value.noteUserId"
          :options="noteUserOpt"
          :disabled="noteIsDisabled"
          :on-update:value="changeNode"
          label-field="userName"
          value-field="userId"
          placeholder="请选择现场记录人员"
          clearable
          filterable
        />
      </n-form-item>

      <n-form-item label="会议类型" path="meetingType">
        <n-radio-group v-model:value="ModifyService.formData.value.meetingType" name="typeradiogroup">
          <n-space>
            <n-radio value="1">线上会议</n-radio>
            <n-radio value="2">线下会议</n-radio>
          </n-space>
        </n-radio-group>
      </n-form-item>

      <n-form-item v-show="hasMeetingUrl" label="会议链接">
        <n-input
          v-model:value="ModifyService.formData.value.meetingUrl"
          placeholder="请输入会议链接"
          maxlength="200"
          show-count
          clearable
        />
      </n-form-item>
    </n-form>

    <selectUser
      ref="selectUserRef"
      title="请选择参会人"
      :rule="true"
      :parentOrgCode="orgCode"
      :userConfigFeid="true"
      v-model:show="showUserSelect"
      @getPersonManageData="getUserData"
      @selectUserData="selectUserChange"
      @close="toggleUserSelect(false)"
    ></selectUser>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, inject } from 'vue';
import type { Ref } from 'vue';
import { ModifyService, formRules } from './formService';
import { MeetingService } from '@/views/securityMeetings/meetingService';
import { useStore } from '@/store';
import {
  meetingAdd,
  meetingEdit,
  getSafeTyData,
  getTreeDataPerson2,
  getDetailFn,
} from '@/views/securityMeetings/fetchData';
import selectUser from '@/components/select-user/Select-user.vue';
import { $toast } from '@/common/shareContext/useToastCtx';
import { ACTION, PROVIDE_KEY } from '@/views/securityMeetings/constant';
import type { IActionData } from '@/views/securityMeetings/type';

defineOptions({ name: 'SecurityMeetingBase' });

// inject
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>;
const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);
const curRow = computed(() => currentAction.value.data);

const store = useStore();
const orgCode = ref(store.userInfo.unitId);

const meetingType = computed(() => MeetingService.curMeeingType.value);
const hasMeetingUrl = computed(() => {
  if (ModifyService.formData.value.meetingType === '1') {
    return true;
  } else {
    ModifyService.formData.value.meetingUrl = '';
    return false;
  }
});

const baseFormRef = ref();

// 获取安委会列表
const awhOpt = ref([]);
const getAWHOption = async () => {
  let res: any = await getSafeTyData({
    unitId: store.userInfo.orgCode,
    pageNo: 1,
    pageSize: -1,
  });
  awhOpt.value = res.data.rows;
};

// 会议时间
const dateTimeRange = computed({
  get: () => {
    if (ModifyService.formData.value.meetingStartTime && ModifyService.formData.value.meetingEndTime) {
      return [ModifyService.formData.value.meetingStartTime, ModifyService.formData.value.meetingEndTime];
    }
    return undefined;
  },
  set: (value) => {
    ModifyService.formData.value.meetingStartTime = value && value.length ? value[0] : '';
    ModifyService.formData.value.meetingEndTime = value && value.length ? value[1] : '';
  },
});

// 人员选择
const meetingPersonRef = ref();

const meetingPerson = ref('');

const showUserSelect = ref(false);
const toggleUserSelect = (isShow = true) => {
  const _list = ModifyService.formData.value.meetingUserRelList;
  // 回显赋值
  if (isShow) selectUserRef.value.getList(_list);
  showUserSelect.value = isShow;
};

const selectUserRef = ref();
async function getUserData(params: any) {
  let res = await getTreeDataPerson2({ ...params, type: 1 });
  selectUserRef.value.renderTable(res);
}

function selectUserChange(arr: any) {
  const _list = arr.map((item: any) => {
    item.userId = item.userId ? item.userId : item.id;
    return item;
  });
  const _name = _list.map((user: any) => user.userName).join('、');

  if (ModifyService.formData.value.noteUserId) {
    const _ids = _list.map((item: any) => item.userId);
    if (_ids.length < 1 || !_ids.includes(ModifyService.formData.value.noteUserId)) {
      ModifyService.formData.value.noteUserId = null;
      ModifyService.formData.value.noteName = '';
    }
  }

  ModifyService.formData.value.meetingUserRelList = _list;
  meetingPerson.value = _name;
  meetingPersonRef.value.restoreValidation();
  toggleUserSelect(false);
}

// 记录人
const noteIsDisabled = computed(() => ModifyService.formData.value?.meetingUserRelList.length < 1);
const noteUserOpt = computed(() => ModifyService.formData.value?.meetingUserRelList || []);

const changeNode = (value: string, selectItem: any) => {
  ModifyService.formData.value.noteUserId = value;
  ModifyService.formData.value.noteName = selectItem.userName;
};

function resetForm() {
  meetingPerson.value = '';
  ModifyService.resetFormData();
}

// init page
async function initFormData() {
  try {
    const { code, data } = await getDetailFn(curRow.value.id);
    if (+code === 200) {
      ModifyService.setFormData(data);

      if (Array.isArray(data.meetingUserRelList)) {
        const _list = data.meetingUserRelList.map((item: any) => item.userName);
        meetingPerson.value = _list.join('、');
      }
    }
  } catch (e) {}
}

function initPage() {
  getAWHOption();
  if (isEdit.value) {
    initFormData();
  }
}

initPage();

// expose-methods
const emits = defineEmits(['action']);

function formValidator() {
  baseFormRef.value?.validate((errors: any) => {
    if (!errors) {
      updateSubmit();
    } else {
      if (errors.length) {
        try {
          const first = errors[0][0];
          if (first.message) {
            $toast.error(first.message);
          }
        } catch (e) {}
      }
    }
  });
}

async function updateSubmit() {
  try {
    const params = Object.assign(
      {
        type: MeetingService.curMeeingType.value,
      },
      ModifyService.formData.value
    );

    ModifyService.submitLoading.value = true;

    const updateFn = isEdit.value ? meetingEdit : meetingAdd;
    const { data } = await updateFn(params);

    ModifyService.submitLoading.value = false;
    if (data) {
      emits('action', { action: ACTION.UPDATESUCCESS });
      resetForm();
    }
  } catch (e: any) {
    ModifyService.submitLoading.value = false;
    if (e.message) $toast.error(e.message);
  }
}

defineExpose({
  formSubmit: formValidator,
  resetForm,
});
</script>

<style scoped lang="scss">
.base-form {
  @apply w-full px-[24px] pt-[24px];
}
</style>
