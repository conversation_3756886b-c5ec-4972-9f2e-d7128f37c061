import { ref } from 'vue';
import type { FormRules } from 'naive-ui';
import { MeetingService } from '@/views/securityMeetings/meetingService';

export class ModifyService {
  static submitLoading = ref(false);

  static formData = ref<any>({
    id: '',
    committeeManagementId: null,
    meetingName: '',
    address: '',
    meetingStartTime: '',
    meetingEndTime: '',
    meetingUserRelList: [],
    meetingUrl: '',
    meetingType: '1',
    noteUserId: null,
    noteName: '',
  });

  static setFormData(data: any) {
    let meetingUserRelList = [];
    if (Array.isArray(data.meetingUserRelList)) {
      meetingUserRelList = data.meetingUserRelList;
    }

    ModifyService.formData.value = {
      id: data.id,
      committeeManagementId: data.committeeManagementId ? data.committeeManagementId : undefined,
      meetingName: data.meetingName,
      address: data.address,
      meetingStartTime: data.meetingStartTime,
      meetingEndTime: data.meetingEndTime,
      meetingUserRelList,
      meetingUrl: data.meetingUrl,
      meetingType: data.meetingType,
      noteUserId: data.noteUserId,
      noteName: data.noteName,
    };
  }

  static resetFormData() {
    ModifyService.formData.value = {
      id: '',
      committeeManagementId: null,
      meetingName: '',
      address: '',
      meetingStartTime: '',
      meetingEndTime: '',
      meetingUserRelList: [],
      meetingUrl: '',
      meetingType: '1',
      noteUserId: null,
      noteName: '',
    };
  }
}

export const formRules: FormRules = {
  committeeManagementId: [
    {
      required: true,
      message: '请选择安委会',
      trigger: ['blur', 'change'],
      validator: (rule, val) => {
        if (MeetingService.curMeeingType.value === '0') {
          return !!val;
        } else {
          return true;
        }
      },
    },
  ],
  meetingName: [{ required: true, message: '请输入会议名称', trigger: ['blur', 'change'] }],
  address: [{ required: true, message: '请输入会议地点', trigger: ['blur', 'change'] }],
  meetingStartTime: [
    {
      message: '请选择会议时间',
      required: true,
      trigger: ['blur', 'change'],
    },
  ],
  noteUserId: [{ required: true, message: '请选择现场记录人员', trigger: ['blur', 'change'] }],
  meetingUserRelList: [
    {
      required: true,
      message: '请选择参会人',
      trigger: ['blur', 'change'],
      validator: (rule, val) => {
        return val.length > 0;
      },
    },
  ],
  meetingType: [
    {
      required: true,
      message: '请输入会议链接',
      trigger: ['blur'],
    },
  ],
};
