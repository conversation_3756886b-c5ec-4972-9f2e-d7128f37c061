<template>
  <ul class="detail-baseinfo">
    <li class="info-item" v-if="+info.type === 0">
      <span class="item-label">安全专题会议:</span>
      <span class="item-value">{{ info.awhMeetingName }}</span>
    </li>
    <li class="info-item">
      <span class="item-label">会议名称:</span>
      <span class="item-value">{{ info.meetingName }}</span>
    </li>
    <li class="info-item">
      <span class="item-label">会议地点:</span>
      <span class="item-value">{{ info.address }}</span>
    </li>
    <li class="info-item">
      <span class="item-label">会议时间:</span>
      <span class="item-value">{{ meetingTime }}</span>
    </li>
    <li class="info-item">
      <span class="item-label">参会人:</span>
      <span class="item-value">{{ userList }}</span>
    </li>
    <li class="info-item">
      <span class="item-label">现场记录人员:</span>
      <span class="item-value">{{ info.noteName }}</span>
    </li>
    <li class="info-item">
      <span class="item-label">会议类型:</span>
      <span class="item-value">{{ +info.meetingType === 1 ? '线上会议' : '线下会议' }}</span>
    </li>
    <li class="info-item" v-if="+info.meetingType === 1">
      <span class="item-label">会议链接:</span>
      <span class="item-value">{{ info.meetingUrl }}</span>
    </li>
  </ul>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { DetailService } from './detailService';

defineOptions({ name: 'SecurityMeetingDetailBase' });

const info = computed(() => DetailService.detailData.value);

const meetingTime = computed(() => {
  return `${info.value.meetingStartTime} ~ ${info.value.meetingEndTime}`;
});
const userList = computed(() => {
  let _list = [];
  if (Array.isArray(info.value?.meetingUserRelList)) {
    _list = info.value?.meetingUserRelList?.filter((item: any) => item.userName).map((item: any) => item.userName);
  }
  return _list.length > 0 ? _list.join(',') : '--';
});
</script>

<style scoped lang="scss">
.detail-baseinfo {
  padding: 16px;
  margin: 20px;
  background-color: #fff;
  border-radius: 4px;
}
.info-item {
  @apply flex flex-row w-full text-[16px] text-[#333] mb-[10px];
  line-height: 1.5em;

  .item-label {
    @apply w-[110px] mr-[8px] text-[#737373];
    text-align: right;
  }
  .item-value {
    @apply flex-1;
  }
}
</style>
