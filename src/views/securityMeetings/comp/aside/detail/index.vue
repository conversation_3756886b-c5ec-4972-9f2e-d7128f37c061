<template>
  <div class="detail-wrap">
    <RadioTab :tabList="tabList" :tab="curTab" @change="tabChange" />
    <KeepAlive>
      <component :is="curComp" />
    </KeepAlive>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, inject } from 'vue';
import type { Ref } from 'vue';
import RadioTab from '@/components/tab/ComRadioTabB.vue';
import BaseInfo from './baseInfo.vue';
import FileInfo from './fileInfo.vue';
import { DetailService } from './detailService';
import { getDetailFn } from '@/views/securityMeetings/fetchData';
import { ACTION, PROVIDE_KEY } from '@/views/securityMeetings/constant';
import type { IActionData } from '@/views/securityMeetings/type';

defineOptions({ name: 'SecurityMeetingDetail' });

const tabList = [
  { name: '1', label: '会议信息', comp: BaseInfo },
  { name: '2', label: '参会明细', comp: FileInfo },
];
const curTab = ref('1');
const curComp = computed(() => tabList.find((item) => item.name === curTab.value)?.comp);

const tabChange = (tab: any) => {
  curTab.value = tab;
};

// inject
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>;
const curRow = computed(() => currentAction.value.data);

async function getDetail() {
  try {
    const { code, data } = await getDetailFn(curRow.value.id);
    if (+code === 200) {
      DetailService.detailData.value = data;
    }
  } catch (e) {}
}
getDetail();
</script>

<style scoped lang="scss"></style>
