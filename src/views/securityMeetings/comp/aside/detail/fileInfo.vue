<template>
  <div class="detail-fileinfo">
    <div class="header">
      <n-button type="primary" @click="handleExport">导出</n-button>
    </div>
    <ul class="detail-baseinfo">
      <li class="info-item">
        <span class="item-label">会议纪要:</span>
        <div v-if="fileList?.length > 0" class="item-value_files">
          <div v-for="file in fileList" :key="file" :src="file" class="item-files">
            <div class="file-icon">
              <AkFile class="icon" />
            </div>
            <a :href="file.url" target="_blank">{{ file.name }}</a>
          </div>
        </div>
        <span class="item-value" v-else>暂无会议纪要</span>
      </li>
      <li class="info-item">
        <span class="item-label">现场照片:</span>
        <div v-if="imgList?.length > 0" class="item-value_imgs">
          <n-image-group show-toolbar>
            <n-image
              class="img-item"
              v-for="img in imgList"
              :key="img"
              :src="img"
              :preview-src="img"
              object-fit="contain"
            />
          </n-image-group>
        </div>
        <span class="item-value" v-else>暂无照片</span>
      </li>
    </ul>

    <div class="teble-wrap">
      <div class="title">签到表</div>
      <n-data-table
        class="h-full com-table"
        remote
        striped
        :max-height="350"
        virtual-scroll
        :columns="cols"
        :data="tableData"
        :bordered="false"
        :pagination="pagination"
        :loading="loading"
        :render-cell="useEmptyCell"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { DetailService } from './detailService';
import { useAutoLoading } from '@/common/hooks/useAutoLoading';
import { useEmptyCell } from '@/common/hooks/useEmptyCell';
import { useNaivePagination } from '@/common/hooks/useNaivePagination';
import { cols } from './tableCol';
import { getMeetingUserTable, exportMeetingDetail } from '@/views/securityMeetings/fetchData';
import { fileDownloader } from '@/utils/fileDownloader';
import { AkFile } from '@kalimahapps/vue-icons';
import { Base64 } from 'js-base64';

defineOptions({ name: 'SecurityMeetingDetailFile' });

const info = computed(() => DetailService.detailData.value);

const fileList = computed(() => {
  let filesList = [];

  if (Array.isArray(info.value?.meetingMinutesAttachment)) {
    filesList = info.value?.meetingMinutesAttachment
      .filter((item: any) => item.address)
      .map((item: any) => {
        const _list = item.address.split('/');
        const name = _list.pop();

        const type = name.split('.');
        const typeName = type[type.length - 1];
        let url = '';
        if (typeName == 'doc' || typeName == 'docx') {
          const b64Encoded = Base64.encode(window.$SYS_CFG.apiPreviewURL + item.address);
          url = window.$SYS_CFG.apiPreviewLink + encodeURIComponent(b64Encoded);
        } else {
          url = window.$SYS_CFG.apiPreviewURL + item.address;
        }
        return {
          name,
          url,
        };
      });
  }

  return filesList;
});

const imgList = computed(() => {
  let imgList = [];
  if (Array.isArray(info.value?.meetingSitePhotoAttachment)) {
    imgList = info.value?.meetingSitePhotoAttachment
      .filter((item: any) => item.address)
      .map((item: any) => window.$SYS_CFG.apiPreviewURL + item.address);
  }

  return imgList;
});

const tableData = ref([]);
const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

function getTableData() {
  const params = {
    meetingId: info.value.id,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
  };

  search(getMeetingUserTable(params)).then((res: any) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}
getTableData();

// 导出
function handleExport() {
  const url = exportMeetingDetail(info.value.id);
  fileDownloader(url);
}
</script>

<style scoped lang="scss">
.detail-fileinfo {
  padding: 16px;
  margin: 20px;
  background-color: #fff;
  border-radius: 4px;
}

.header {
  @apply w-full flex justify-end mb-[20px];
}

.info-item {
  @apply flex flex-row w-full text-[16px] text-[#333] mb-[15px];
  line-height: 1.5em;

  .item-label {
    @apply w-[80px] mr-[8px] text-[#737373] pt-[4px];
    text-align: right;
  }
  .item-value {
    flex: 1;
    padding-top: 4px;
  }
}

.item-value_files {
  @apply flex flex-col;
}

.item-files {
  @apply flex flex-row items-center gap-[4px] py-[4px] px-[6px] cursor-pointer;

  &:hover {
    background-color: #eee;
    color: #18a058;
  }

  .file-icon {
    @apply text-[16px];
  }
}

.item-value_imgs {
  @apply flex flex-row flex-wrap items-center gap-[10px] flex-1;

  .img-item {
    @apply w-[100px] h-[100px] rounded-[4px] bg-[#fff] flex items-center justify-center;
    border: 1px solid #eee;
  }
  :deep(.n-image img) {
    height: 100%;
  }
}

.teble-wrap {
  margin-top: 40px;
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 10px;
  }
}
</style>
