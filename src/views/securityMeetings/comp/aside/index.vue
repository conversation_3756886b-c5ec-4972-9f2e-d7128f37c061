<template>
  <ComDrawerA
    :autoFocus="false"
    :footerPaddingBottom="25"
    :maskClosable="false"
    :show-action="!isDetail"
    :positiveLoading="ModifyService.submitLoading.value"
    @handle-negative="handleClose"
    @handle-positive="handleSubmit"
    class="!w-[550px]"
  >
    <Detail v-if="isDetail" />
    <Modify v-if="isUpdate" ref="ModifyRef" @action="actionHandle" />
    <FileForm v-if="isCHMX" ref="FileFormRef" @action="actionHandle" />
  </ComDrawerA>
</template>

<script lang="ts" setup>
import ComDrawerA from '@/components/drawer/ComDrawerA.vue';
import Modify from './modify/baseForm.vue';
import FileForm from './modify/fileForm.vue';
import Detail from './detail/index.vue';
import type { IActionData } from '../../type';
import { ACTION, PROVIDE_KEY } from '../../constant';
import { computed, inject, Ref, ref } from 'vue';
import { ModifyService } from './modify/formService';

defineOptions({ name: 'SercurityMeetingAside' });

const emits = defineEmits(['action']);

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const isDetail = computed(() => {
  return currentAction.value.action === ACTION.DETAIL;
});
const isUpdate = computed(() => {
  return [ACTION.ADD, ACTION.EDIT].includes(currentAction.value.action);
});
const isCHMX = computed(() => {
  return currentAction.value.action === ACTION.UPDATECHMX;
});

const ModifyRef = ref();
const FileFormRef = ref();

function handleSubmit() {
  if (isUpdate.value) {
    ModifyRef.value.formSubmit();
    return;
  }
  if (isCHMX.value) {
    FileFormRef.value.updateSubmit();
    return;
  }
}

function handleClose() {
  emits('update:show' as any, false); // any-屏蔽组件内透传的emit类型
  if (isUpdate.value) {
    ModifyRef.value.resetForm();
  }
  if (isCHMX.value) {
    FileFormRef.value.resetForm();
    return;
  }
}

function actionHandle(val: IActionData) {
  if (val.action === ACTION.UPDATESUCCESS) {
    handleClose();
  }
  emits('action', val);
}
</script>

<style module lang="scss"></style>
