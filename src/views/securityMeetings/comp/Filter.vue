<template>
  <div :class="$style['wrap']">
    <n-form :show-feedback="false" label-placement="left">
      <n-row gutter="12">
        <n-col :span="6">
          <n-form-item label="会议名称：">
            <n-input type="text" v-model:value="filterForm.meetingName" placeholder="请输入会议名称" clearable />
          </n-form-item>
        </n-col>
        <n-col :span="6">
          <n-form-item label="会议类型：">
            <n-select v-model:value="filterForm.meetingType" :options="meetingOpt" clearable />
          </n-form-item>
        </n-col>
        <n-col :span="8">
          <n-form-item label="选择日期：">
            <n-date-picker
              v-model:value="dataRange"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @update-formatted-value="onDatePickerUpdate"
              clearable
            ></n-date-picker> </n-form-item
        ></n-col>
        <n-col :span="4">
          <n-form-item class="flex justify-end">
            <div class="!w-[300px] flex justify-end gap-[10px]">
              <n-button type="primary" @click="doHandle(ACTION.ADD)">
                {{ ACTION_LABEL.ADD }}
              </n-button>

              <n-button type="error" @click="del">
                {{ ACTION_LABEL.MULTDELETE }}
              </n-button>
            </div>
          </n-form-item>
        </n-col>
      </n-row>
      <!-- <n-grid class="flex-1" :cols="4" :x-gap="15" :y-gap="15">
        <n-form-item-gi label="会议名称：">
          <n-input type="text" v-model:value="filterForm.meetingName" placeholder="请输入会议名称" clearable />
        </n-form-item-gi>
        <n-form-item-gi label="会议类型：">
          <n-select v-model:value="filterForm.meetingType" :options="meetingOpt" clearable />
        </n-form-item-gi>
        <n-form-item-gi label="选择日期：">
          <n-date-picker
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @update-formatted-value="onDatePickerUpdate"
            clearable
          ></n-date-picker>
        </n-form-item-gi>

        <n-form-item-gi class="flex justify-end">
          <div class="!w-[300px] flex justify-end gap-[10px]">
            <n-button type="primary" @click="doHandle(ACTION.ADD)">
              {{ ACTION_LABEL.ADD }}
            </n-button>

            <n-button type="error" @click="del">
              {{ ACTION_LABEL.MULTDELETE }}
            </n-button>
          </div>
        </n-form-item-gi>
      </n-grid> -->
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { ACTION, ACTION_LABEL } from '../constant';
import { trimObjNull } from '@/utils/obj';

const emits = defineEmits(['action', 'del']);
const dataRange = ref(null);
const meetingOpt = [
  {
    label: '线上会议',
    value: '1',
  },
  {
    label: '线下会议',
    value: '2',
  },
];

const filterForm = ref(initForm());
function initForm() {
  return {
    meetingName: '',
    meetingType: null,
    startTime: '',
    endTime: '',
  };
}

function onDatePickerUpdate(value: string[] | null) {
  filterForm.value.startTime = value ? value[0] + ' 00:00:00' : '';
  filterForm.value.endTime = value ? value[1] + ' 23:59:59' : '';
}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: trimObjNull(filterForm.value),
  });
}

function del() {
  emits('del');
}

watch(
  () => filterForm.value,
  () => {
    doHandle(ACTION.SEARCH);
  },
  {
    deep: true,
  }
);

function resetFilter() {
  filterForm.value = initForm();
  dataRange.value = null;
}
defineExpose({
  resetFilter,
});

defineOptions({ name: 'checkTempFilterComp' });
</script>
<style module lang="scss">
.wrap {
  width: 100%;
  padding: 16px 24px;
  background-color: #eef7ff;
}
:global(.com-table-filter) {
  @apply rounded-[0px] border-none pb-[0px];
}
</style>
