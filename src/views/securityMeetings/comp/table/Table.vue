<template>
  <div class="box">
    <n-data-table
      class="w-full h-full com-table"
      remote
      striped
      :columns="curColumns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :pagination="pagination"
      :loading="loading"
      :row-key="(row: any) => row.id"
      v-model:checked-row-keys="checkedKeys"
      :render-cell="useEmptyCell"
      :theme-overrides="themeOverrides"
      :scroll-x="2315"
    />
  </div>
</template>

<script lang="ts" setup>
import { useActionDivider } from '@/common/hooks/useActionDivider';
import { useAutoLoading } from '@/common/hooks/useAutoLoading';
import { useEmptyCell } from '@/common/hooks/useEmptyCell';
import { useNaivePagination } from '@/common/hooks/useNaivePagination';
import { $dialog } from '@/common/shareContext/useDialogCtx';
import { useStore } from '@/store';
import { IObj } from '@/types';
import mittBus from '@/utils/mittBus';
import { NButton, useMessage } from 'naive-ui';
import { h, ref, computed, toRaw, VNode, watch } from 'vue';
import { cols, ztCols } from '../../comp/table/columns';
import { ACTION, ACTION_LABEL } from '../../constant';
import { getTreeDataPerson, endMeeting, batchDelete } from '../../fetchData';
import type { IPageData } from '../../type';
import { MeetingService } from '../../meetingService';

const checkedKeys = ref<Array<string | number>>([]);
const emits = defineEmits(['action']);
const store = useStore();
const message = useMessage();

const delArray = () => {
  if (checkedKeys.value.length === 0) {
    message.warning('请勾选需要批量删除的数据');
  } else {
    $dialog.error({
      title: '删除',
      content: '是否删除此数据',
      positiveText: '确定',
      negativeText: '取消',
      transformOrigin: 'center',
      onPositiveClick: async () => {
        let res: any = await batchDelete({ id: checkedKeys.value.join(',') });
        if (res.code == 200) {
          if (tableData.value.length - 1 == 0) {
            pagination.page = pagination.page - 1;
          }
          checkedKeys.value = [];

          getTableData();
          message.success('删除成功');
        }
      },
      onNegativeClick: () => {
        checkedKeys.value = [];
      },
    });
  }
};
const themeOverrides = {
  tdColorStriped: '#dfeefc',
  tdColorStripedModal: 'red',
  thColor: '#BBCCF3',
  thTextColor: '#222',
};
mittBus.on('addUpdate', (v: any) => {
  pagination.page = v;
});

const columns = ref<any>([]);
const ztColumns = ref<any>([]);
const curColumns = computed(() => {
  return MeetingService.curMeeingType.value === '0' ? ztColumns.value : columns.value;
});

const tableData = ref<IPageData[]>([]);

const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

let filterData: IObj<any> = {}; // 搜索条件
const levelCode = ref<string>('');

const unitId = ref<string>('');

function getTableData() {
  const params = {
    levelPath: levelCode.value ? levelCode.value : store.userInfo.unitId,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    type: MeetingService.curMeeingType.value,
    unitId: unitId.value || store.userInfo.unitId,
    ...filterData,
  };

  search(getTreeDataPerson(params)).then((res: any) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

function setColumns() {
  const actionCol = {
    title: '操作',
    key: 'actions',
    width: 430,
    align: 'center',
    fixed: 'right',
    render(row: any) {
      return getActionBtn(row);
    },
  };

  columns.value.push(...cols, actionCol);
  ztColumns.value.push(...ztCols, actionCol);
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-edit-button',

          onClick: () => emits('action', { action: ACTION.SPACE, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.SPACE }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-edit-button',
          disabled: (() => {
            if (+row.meetingStatus == 2) {
              return true;
            } else {
              return ![store.userInfo.orgCode, store.userInfo.unitId].includes(row.createUnitId);
            }
          })(),
          onClick: () => handleEnd(row),
        },
        { default: () => ACTION_LABEL.ENDMETTING }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-edit-button',

          onClick: () => emits('action', { action: ACTION.DETAIL, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.DETAIL }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-edit-button ',
          disabled: (() => {
            if (+row.meetingStatus !== 0) {
              return true;
            } else {
              return ![store.userInfo.orgCode, store.userInfo.unitId].includes(row.createUnitId);
            }
          })(),
          onClick: () => emits('action', { action: ACTION.EDIT, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.EDIT }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-edit-button',
          disabled: (() => {
            if (row.noteUserId) {
              return row.noteUserId !== store.userInfo.id;
            } else {
              return ![store.userInfo.orgCode, store.userInfo.unitId].includes(row.createUnitId);
            }
          })(),
          onClick: () => emits('action', { action: ACTION.UPDATECHMX, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.UPDATECHMX }
      ),
    ],
  ];

  return useActionDivider(acList);
}

function handleEnd(row: any) {
  $dialog.warning({
    title: '结束会议',
    content: '确定结束会议吗？',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      let res: any = await endMeeting({ meetingId: row.id });
      if (res.code == 200) {
        getTableData();
        message.success('已结束');
      }
    },
  });
}

watch(
  () => [MeetingService.curMeeingType.value, MeetingService.treeLevel.value, MeetingService.treeId.value],
  (val) => {
    checkedKeys.value = [];
    levelCode.value = val[1];
    unitId.value = val[2];
    pagination.page = 1;
    getTableData();
  },
  { immediate: true }
);

// 初始化项目
function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: {},
  });
}

// on created
setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
  delArray,
});

defineOptions({ name: 'ExpertDatabaseTable' });
</script>

<style scoped lang="scss">
.btn {
  margin-left: 88%;
  margin-top: 15px;
  margin-bottom: 15px;
  width: 88px;
  height: 34px;
  background-color: #3e62eb;
  color: #fff;
  border-radius: 4px;
}

.btn:hover {
  background-color: #6889f7;
}
</style>
