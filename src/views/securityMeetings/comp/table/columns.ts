import { DataTableColumn } from 'naive-ui';
import { useStore } from '@/store';

export const cols: DataTableColumn[] = [
  {
    align: 'center',
    type: 'selection',
    width: 55,
    fixed: 'left',
    disabled(row) {
      const store = useStore();
      return ![store.userInfo.orgCode, store.userInfo.unitId].includes(row.createUnitId);
    },
  },
  {
    title: '所属单位',
    key: 'createUnitName',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '会议名称',
    key: 'meetingName',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '会议类型',
    key: 'meetingTypeName',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '会议状态',
    key: 'meetingStatusName',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '会议时间',
    key: 'meetingStartTime',
    align: 'center',
    width: 300,
    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      return row.meetingStartTime + '-' + row.meetingEndTime;
    },
  },
  {
    title: '会议发起人',
    key: 'createUserName',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '会议成员数',
    key: 'attendNum',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '会议地点',
    key: 'address',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
];
export const ztCols: DataTableColumn[] = [
  {
    align: 'center',
    type: 'selection',
    width: 55,
    fixed: 'left',
    disabled(row) {
      const store = useStore();
      return ![store.userInfo.orgCode, store.userInfo.unitId].includes(row.createUnitId);
    },
  },
  {
    title: '所属单位',
    key: 'createUnitName',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '安委会名称',
    key: 'awhMeetingName',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '会议名称',
    key: 'meetingName',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '会议类型',
    key: 'meetingTypeName',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '会议状态',
    key: 'meetingStatusName',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '会议时间',
    key: 'meetingStartTime',
    align: 'center',
    width: 320,
    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      return row.meetingStartTime + '-' + row.meetingEndTime;
    },
  },
  {
    title: '会议发起人',
    key: 'createUserName',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '会议成员数',
    key: 'attendNum',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '会议地点',
    key: 'address',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
];
