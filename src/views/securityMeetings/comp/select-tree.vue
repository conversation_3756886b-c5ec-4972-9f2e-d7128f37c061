<template>
  <n-modal :show="props.showModal">
    <n-card
      class="n_card"
      :title="props.title"
      :bordered="false"
      size="huge"
      preset="dialog"
      role="dialog"
      aria-modal="true"
    >
      <div class="content">
        <div class="org">选择组织</div>
        <div class="list">
          <n-tree
            ref="treeRef"
            class="px-4"
            :data="treeData"
            block-line
            key-field="id"
            label-field="text"
            :cancelable="false"
            :show-irrelevant-nodes="false"
            :render-label="renderLabel"
            :render-switcher-icon="renderSwitcherIconWithExpaned"
            children-field="children"
            :theme-overrides="themeOverrides"
            :default-selected-keys="defaultSelectedkeys"
            :default-expanded-keys="defaultExpandedkeys"
            :node-props="nodeProps"
          />
        </div>
      </div>
      <template #footer>
        <n-space>
          <n-button strong @click="cancel">关闭</n-button>
          <n-button strong type="primary" @click="onConfirm">确定</n-button>
        </n-space>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { TreeOption, NIcon } from 'naive-ui';
import { ref, watch, h, nextTick } from 'vue';
import { icon_chevron as iconChevronForward } from './assets/index';
import Dept from './assets/dept.png';
import YeWu from './assets/yewu.png';
import JianGuan from './assets/jianguan.png';
import YangChang from '@/assets/iconImg/yanchang.png';
import AnGang from '@/assets/iconImg/angang.png';
4;
import Zhongdanong from '@/assets/iconImg/zhongdanong.png';
import WyCh from '@/assets/iconImg/wych2.png';
import { useStore } from '@/store';
const store = useStore();
const treeRef = ref(null);

// 选中样式
const themeOverrides = {
  nodeHeight: '40px',
};
const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    // default: '选择人员',
  },
  dataList: {
    type: Array,
    default: () => [],
  },
  treeId: {
    type: String,
    default: '',
  },
});

const defaultExpandedkeys = ref<string[]>([]);
const defaultSelectedkeys = ref<string[]>([]);
function renderSwitcherIconWithExpaned({ option }: any) {
  if (option.root === 1) {
    return null;
  } else {
    if (option.children && option.children.length > 0) {
      return h(
        NIcon,
        {
          style: {
            width: '14px',
            height: '14px',
          },
        },
        { default: () => h(iconChevronForward) }
      );
    }
  }
}
function renderLabel(e: any) {
  // 如果节点没有子节点，不显示展开图标
  if (e.option.children && e.option.children.length === 0) {
    e.option.isLeaf = true;
    return h(
      'div',
      {
        style: {
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          padding: '10px 0 10px 0',
        },
      },
      [
        h('img', {
          style: {
            width: '16px',
            height: '16px',
            marginRight: '5px',
          },
          src:
            e.option.attributes?.orgType === '2'
              ? JianGuan
              : e.option.attributes?.orgType === '1'
                ? YeWu
                : Dept,
        }),
        h(
          'div',
          {
            style: {
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            },
            title: e.option.text,
          },
          e.option.text
        ),
      ]
    );
  } else {
    // 如果节点有子节点，正常显示
    if (e.option.root === 1) {
      // e.option.isLeaf = true;
      return h(
        'div',
        {
          style: {
            display: 'flex',
            alignItems: 'center',
            padding: '10px 0 10px 0',
          },
        },
        [
          h('img', {
            style: {
              width: '16px',
              height: '16px',
              marginRight: '10px',
              // marginLeft: '24px',
            },
            src: store.userInfo.logoPicUrl,
          }),
          h(
            'div',
            {
              style: {
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              },
              title: e.option.text,
            },
            e.option.text
          ),
        ]
      );
    } else {
      return h(
        'div',
        {
          style: {
            display: 'flex',
            alignItems: 'center',
            padding: '10px 0 10px 0',
            marginLeft: '-10px',
          },
        },
        [
          h('img', {
            style: {
              width: '16px',
              height: '16px',
              marginRight: '5px',
            },
            src:
              e.option.attributes?.orgType === '2'
                ? JianGuan
                : e.option.attributes?.orgType === '1'
                  ? YeWu
                  : Dept,
          }),
          h(
            'div',
            {
              style: {
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              },
              title: e.option.text,
            },
            e.option.text
          ),
        ]
      );
    }
  }
}
const emits = defineEmits(['close', 'success']);
const optionN = ref();
// #region 左侧树形结构
const nodeProps = ({ option }: { option: TreeOption }) => {
  return {
    onClick() {
      // 这里写点击组组织后的逻辑
      optionN.value = option;
    },
  };
};
const treeData = ref([]);

watch(
  () => props.showModal,
  (nv) => {
    if (nv) {
      let curKey = props.dataList[0].id as string;
      optionN.value = props.dataList[0];
      defaultExpandedkeys.value = [curKey];
      defaultSelectedkeys.value.push(curKey);
      treeData.value = props.dataList;
      treeData.value[0].root = 1;

      nextTick(() => {
        console.log(treeRef.value, '>>>>>>>>>>>>>>>>');
        const firstNodeSwitcher = treeRef.value.selfElRef.querySelector(
          '.n-tree-node-switcher'
        );
        console.log(firstNodeSwitcher.style, 'treeRef.value');
        if (firstNodeSwitcher) {
          // 设置display为none
          firstNodeSwitcher.style.display = 'none';
        }
      });
    }
  },
  { immediate: true }
);
// #endregion

// #region 右侧表格
interface Song {
  no: number;
  name: string;
  orgName: string;
  length: string;
  phone: string;
  post: string;
}
// #endregion
const cancel = () => {
  emits('close', false);
};
const onConfirm = () => {
  emits('success', optionN.value);
  emits('close', false);
};
</script>

<style lang="scss" scoped>
.n_card {
  width: 600px;
  height: 450px;
  background-color: #f8f8f8;
  :deep(.n-card-header .n-card-header__main::before) {
    content: '';
    display: inline-block;
    width: 4px;
    height: 15px;
    margin-right: 5px;
    background-color: #527cff;
  }
}
.list {
  height: 320px !important;
  overflow: auto;
}
.content {
  height: 100%;
  .header {
    display: flex;
    margin-bottom: 10px;

    .text {
      color: #606266;
    }

    .box {
      flex: 1;
      margin-left: 10px;
      min-height: 30px;
      border-radius: 3px;
      border: 1px solid #dcdfe6;
      background-color: #fff;
    }
  }

  .main {
    height: 550px;
    display: flex;
    justify-content: space-between;

    .left {
      width: 280px;
      margin-right: 10px;
      background-color: #fff;
      border-radius: 3px;
      border: 1px solid #e1e1e1;

      .org {
        height: 50px;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f7fa;
      }
    }

    .right {
      flex: 1;
      padding: 10px 10px;
      background-color: #fff;
      border-radius: 3px;
      border: 1px solid #e1e1e1;

      .search-box {
        width: 100%;
        margin-bottom: 10px;
        display: flex;
        justify-content: end;
      }
    }
  }
}
.n-space {
  position: absolute;
  bottom: 3%;
  right: 5%;
}
</style>
