import { ACTION } from './constant';
import type { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 分页列表数据
export interface IPageData {
  address: string;
  attendNum: number | string;
  awhMeetingName: string;
  chr: string;
  chsj: string;
  committeeManagementId: string;
  createUnitId: string;
  createUnitName: string;
  createUserName: string;
  id: string;
  meetingEndTime: string;
  meetingName: string;
  meetingStartTime: string;
  meetingStatus: string;
  meetingStatusName: string;
  meetingType: string;
  meetingTypeName: string;
  noteUserId: string;
  selfSign: string;
  type: string;
}
export type IPageDataRes = IPageRes<IPageData>;

export interface IDetail {
  areaCode: string;
  areaName: string;
  createdBy: string;
  createdTime: string;
  /**
   * 主键ID
   */
  id: string;
  isIot: string;
  /**
   * 单位辖区范围
   */
  jurisdiction: string;
  unitName: string;
  unitType: string;
  updatedBy: string;
  updatedTime: string;
}
export interface ICategory {
  id: string;
  name: string;
  type: string;
  parentId: string | null;
  status: string;
  createTime: string;
  updateTime: string;
  createUser: string;
  updateUser: string;
  delFlag: string;
}
export interface OrgTree {
  /**
   * 节点属性
   */
  attributes?: { [key: string]: any };
  /**
   * 点是否被选中
   */
  checked?: boolean;
  /**
   * 节点的子节点
   */
  children?: OrgTree[];
  hasChildren?: boolean;
  hasParent?: boolean;
  /**
   * 主键id
   */
  id?: string;
  /**
   * 层级
   */
  level?: number;
  /**
   * 父ID
   */
  parentId?: string;
  state?: string;
  /**
   * 节点名称
   */
  text?: string;
  /**
   * 树名
   */
  treeName?: string;
  /**
   * 节点类型
   */
  type?: string;
  /**
   * 节点id
   */
  typeId?: string;
  [property: string]: any;
}
