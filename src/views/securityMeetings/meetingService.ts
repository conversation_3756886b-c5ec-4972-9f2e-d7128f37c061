/*
 * @Author: xginger <EMAIL>
 * @Date: 2025-05-26 10:24:28
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-05-27 17:13:43
 * @FilePath: \ehs-org-alloc-mgr\src\views\securityMeetings\meetingService.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { ref } from 'vue';

export class MeetingService {
  static curMeeingType = ref('1');

  static treeLevel = ref<string>('');
  static treeId = ref<string>('');
}
