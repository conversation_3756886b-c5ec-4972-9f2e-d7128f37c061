<template>
  <div class="">
    <com-bread :data="breadData"></com-bread>

    <div class="flex flex-1">
      <transition name="slide-fade">
        <div class="h-full" v-show="formVisible">
          <comTree :dataList="treeData" @treeChange="treeChange" @action="actionFn"></comTree>
        </div>
      </transition>

      <div class="content-main">
        <img
          @click="formVisible = !formVisible"
          src="@/assets/open.png"
          style="position: absolute; left: -1%; top: 50%; width: 30px; cursor: pointer"
        />
        <RadioTab :tabList="tabList" :current="MeetingService.curMeeingType.value" @change="handleChange" />
        <FilterComp ref="filterRef" @action="actionFn" @del="del" />

        <Table
          class="com-table-container"
          style="border-radius: 0px 0px 5px 5px"
          ref="tableCompRef"
          @action="actionFn"
        />
      </div>
    </div>
    <AsideComp v-model:show="isShowAside" :title="actionLabel" @action="actionFn" />
  </div>
</template>

<script setup lang="ts">
import { useAutoLoading } from '@/common/hooks/useAutoLoading';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import RadioTab from '@/components/tab/ComRadioTabA2.vue';
import { computed, provide, Ref, ref, onMounted, watch } from 'vue';
import AsideComp from './comp/aside/index.vue';
import FilterComp from './comp/Filter.vue';
import Table from './comp/table/Table.vue';
import comTree from './comTree.vue';
import { getOrgTrees } from './fetchData';
import type { IActionData } from './type';
import { useStore } from '@/store';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from './constant';
import { MeetingService } from './meetingService';
import { removeEmptyChildren } from '@/utils/removeEmptyChildren';
import router from '@/router';
import { useRoute } from 'vue-router';
import { $toast } from '@/common/shareContext/useToastCtx';

const route: any = useRoute();
const currentAction = ref<IActionData>({
  action: ACTION.NONE,
  data: {},
});
const actionLabel = computed(() => (currentAction.value ? ACTION_LABEL[currentAction.value.action] : ''));
const [loading, search] = useAutoLoading(true);
const isShowAside = ref(false);
const store = useStore();
const formVisible = ref(true);
const tableCompRef = ref();

const filterRef = ref();

const breadData = ref([{ name: '安全会议' }, { name: '安全专题会议' }]);
// tabs
const tabList: any = [
  { name: '0', label: '安全专题会议11' },
  { name: '1', label: '安全例会22' },
];
onMounted(() => {
  console.log(MeetingService.curMeeingType.value);
});
function handleChange(name: '0' | '1') {
  MeetingService.curMeeingType.value = name;

  if (name === '0') {
    breadData.value = [{ name: '安全会议' }, { name: '安全专题会议' }];
  } else {
    breadData.value = [{ name: '安全会议' }, { name: '安全例会' }];
  }
  filterRef.value?.resetFilter();
}

const treeData = ref<any[]>([]);
//获取树结构数据
function QueryOrgTrees() {
  const params = {
    orgCode: store.userInfo.unitId,
    needChildUnit: '1',
    needself: '1',
    type: '2',
  };
  search(getOrgTrees(params)).then((res: any) => {
    if (res.code != 200) return;
    const _RES: any = removeEmptyChildren(res.data);
    treeData.value = _RES;
  });
}
const treeChange = (v: any) => {
  MeetingService.treeLevel.value = v.levelCode;
  MeetingService.treeId.value = v.id;
};

// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

function actionFn(val: IActionData) {
  currentAction.value = val;
  if (val.action === ACTION.SEARCH) {
    handleSearch(val.data);
    return;
  }

  if (val.action === ACTION.SPACE) {
    router.push({
      name: 'digitalSpace',
      query: {
        id: val.data.id,
      },
    });
    return;
  }

  if ([ACTION.ADD, ACTION.EDIT, ACTION.DETAIL, ACTION.UPDATECHMX].includes(val.action)) {
    isShowAside.value = true;
    return;
  }

  if (val.action === ACTION.UPDATESUCCESS) {
    $toast.success('操作成功');
    filterRef.value?.resetFilter();
    return;
  }
}
function handleSearch(data?: any) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}
const del = () => {
  tableCompRef.value.delArray();
};

watch(
  () => router.currentRoute.value.path,
  (newPath, oldPath) => {
    router.beforeEach((to, from, next) => {
      if (!from.fullPath.includes('digitalSpace') && !to.fullPath.includes('digitalSpace')) {
        MeetingService.curMeeingType.value = '1';
      }
      next();
    });
  },
  { immediate: true, deep: true }
);

watch(
  () => route.query,
  (val) => {
    console.log('val', val);
    if (val.id) {
      MeetingService.curMeeingType.value = val.tab || '1';
      currentAction.value.action = ACTION.DETAIL;
      currentAction.value.data = { id: val.id };
      isShowAside.value = true;
    }
  },
  { immediate: true, deep: true }
);

QueryOrgTrees();

defineOptions({ name: 'SecurityResponsibilitiesIndex' });
</script>
<style scoped lang="scss">
.content-main {
  @apply flex flex-col ml-[15px] relative flex-1 w-0;
}

.com-table-container {
  flex: 1;
  padding: 0 24px 16px;
}

.btn {
  margin-left: 88%;
  margin-top: 15px;
  margin-bottom: 15px;
  width: 88px;
  height: 34px;
  background-color: #3e62eb;
  color: #fff;
  border-radius: 4px;
}

.btn:hover {
  background-color: #6889f7;
}
</style>
