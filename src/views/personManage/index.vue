<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-15 17:54:11
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-06-03 20:52:20
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/views/personManage/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="">
    <!-- <ComBread :data="breadData" /> -->
    <!-- <div class="header" style="margin-top: 10px; margin-bottom: 15px; margin-left: 5px">人员管理</div> -->
    <com-bread :data="breadData"></com-bread>
    <!-- h-[calc(100%-86px)] -->
    <div class="flex h-[calc(100%)]">
      <transition name="slide-fade">
        <div class="h-[calc(100%)]" v-show="formVisible">
          <com-tree
            :title="titlee"
            :dataList="treeData"
            @handelChange="handelChange"
          ></com-tree>
        </div>
      </transition>

      <div class="!ml-[15px]" style="position: relative">
        <img
          @click="formVisible = !formVisible"
          src="@/assets/open.png"
          style="
            position: absolute;
            left: -1%;
            top: 50%;
            width: 30px;
            cursor: pointer;
          "
        />
        <RadioTab :tabList="tabList" :tab="curTab" @change="handleChange" />
        <Filter
          :getTree="getTree"
          class="com-table-filter"
          style="border-radius: 0 5px 0 0"
          @action="actionFn"
        />
        <TableList
          class="com-table-container h-[calc(100%-98px)]"
          :treeCode="treeLevelCode"
          :tab="curTab"
          :unitId="orgCode"
          ref="tableCompRef"
          @action="actionFn"
        />
      </div>
    </div>

    <SelectUserMore
      :unitId="orgCode"
      :curTab="curTab"
      v-model:show="isShowAside"
      :userType="1"
      :title="actionLabel"
      @close="isShowAside = false"
      @success="handleAdd"
    />

    <resumeComp
      v-model:show="showResume"
      :formData="formData"
      @close="showResume = false"
    />
    <resumeComp2
      v-model:show="showResume2"
      :formData="formData2"
      @close="showResume2 = false"
    />
  </div>
</template>

<script setup lang="ts">
import { toRaw, ref, onMounted } from 'vue';
import comTree from '@/components/tree/comTree.vue';
import Filter from './comp/Filter.vue';
import TableList from './comp/table/Table.vue';
import resumeComp from './resumeComp/index.vue';
import resumeComp2 from './resumeComp2/index.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import RadioTab from '@/components/tab/ComRadioTabA.vue';
import SelectUserMore from '@/components/select-user/SelectUserMore.vue';
import { getOrgTrees, addPersonManage, getTreeDataPerson } from './fetchData';
import { ACTION } from './constant';
import { IObj } from '@/types';
import { IActionData } from './type';
import { useRoute, useRouter } from 'vue-router';
import { TreeOption, useMessage } from 'naive-ui';
import { fileDownloader } from '@/utils/fileDownloader';
import { useStore } from '@/store';

const message = useMessage();
const breadData: any[] = [{ name: '人员管理' }];
const store = useStore();
const formVisible = ref(true);
const router = useRouter();
const route = useRoute();
const selectUserRef: any = ref(null);
const isShowAside = ref(false);
const actionLabel = ref('选择人员');
const showResume2 = ref(false);
const titlee = ref('人员管理');
// 人员管理树结构
const treeData = ref([]);
const treeId: any = ref(null);
const treeLevelCode: any = ref(null);
const orgCode: any = ref('');
//树结构点击后获取到的值
const handelChange = (v: TreeOption) => {
  console.log(v, '>>>>');
  orgCode.value = v.id;
  treeLevelCode.value = v.levelCode;
};
// tabs
const tabList = [
  { name: '1', label: '安全管理人员' },
  { name: '2', label: '特种作业人员' },
];

const curTab = ref<any>(route.query?.tab || '1');

function handleChange(name: number) {
  curTab.value = name;
  router.push({
    path: '/personManage',
    query: {
      tab: name,
    },
  });
}
const showResume = ref(false);
const formData = ref({});
const formData2 = ref({});
function actionFn(val: IActionData) {
  if (val.action === ACTION.ADD) {
    isShowAside.value = true;
  }
  if (val.action === ACTION.SEARCH) {
    return handleSearch(val.data);
  }
  if (val.action === ACTION.EXPORT) {
    return exportExcel(val.data);
  }
  if (val.action === ACTION.DELETE) {
  }
  // 安全履历
  if (val.action === ACTION.RESUME) {
    formData.value = val.data;
    return (showResume.value = true);
  }
  if (val.action === ACTION.RESUME2) {
    formData2.value = val.data;
    return (showResume2.value = true);
  }
}
function transform(obj: any) {
  if ((!obj) instanceof Object) {
    throw new Error('transform(): 传入参数不是对象');
  }
  var arr = [];
  for (var item in obj) {
    arr.push(item + '=' + obj[item]);
  }
  return arr.join('&');
}
// 导出
function exportExcel(val: any) {
  // console.log(treeLevelCode.value,'搜索')
  const params = {
    type: curTab.value,
    unitId: orgCode.value,
    levelCode: treeLevelCode.value ? treeLevelCode.value : '00-0001',
    keyWords: val.templateName,
    zh_id: store.userInfo.zhId,
  };
  fileDownloader(
    `${window.$SYS_CFG.apiBaseURL}/bw-clnt-org-person-service/personManage/exportPersonManageExcel?${transform(params)}`
  );
}
// 新增人员
function handleAdd(data: any, code: any) {
  const params = {
    type: curTab.value,
    orgUserList: toRaw(data),
    levelCode: code,
  };
  addPersonManage(params).then((res: any) => {
    if (res.code != 200) return message.error(res.message);
    message.success('保存成功');
    handleSearch();
  });
}
// 递归函数，使用map生成新的数组
function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };

    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }

    return newItem; // 返回新对象
  });
}
//获取树结构数据
function QueryOrgTrees() {
  const params = {
    orgCode: store.userInfo.unitId,
    needChildUnit: '1',
    needself: '1',
  };
  getOrgTrees(params).then((res: any) => {
    if (res.code != 200) return;
    const _RES: any = removeEmptyChildren(res.data);
    treeData.value = _RES;
    // if (treeData.value && treeData.value.length > 0) {
    //   // treeId.value = _RES[0].id;
    //   let curKey = _RES[0].id as string;
    // }
  });
}

const tableCompRef = ref();
function handleSearch(data?: IObj<any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}
// 删除

onMounted(() => {
  QueryOrgTrees();
  // handleSearch();
});

defineOptions({ name: 'personManage' });
</script>
