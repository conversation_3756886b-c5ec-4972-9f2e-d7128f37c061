/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-15 14:50:52
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-19 15:22:47
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/views/personManage/fetchData.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import type { IPageDataRes, OrgTree, GetOrgUserVo } from './type';
import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';

// 获取人员列表
export function getPersonManageList(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getPersonManageList);
  return $http.post<IPageDataRes>(url, {
    data: { _cfg: { showTip: false }, ...params },
  });
}
export function serviceLogin(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.serviceLogin);
  return $http.post<IPageDataRes>(url, {
    data: { _cfg: { showTip: false }, ...params },
  });
}

export function modelCalculation(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.modelCalculation, params);
  return $http.get(url, {
    data: { _cfg: { showTip: true } },
  });
}
// 获取树形结构
export function getOrgTrees(query: IObj<any>) {
  return $http.get<OrgTree>(api.getUrl(api.type.server, api.name.interface.getTreeData, query));
}

export function queryEvaluateResultDetail(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.queryEvaluateResultDetail);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}

export function analyseSumUp(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.analyseSumUp);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}
export function tyqwTalking(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.tyqwTalking);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}
export function queryResultByDimension(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.queryResultByDimension);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}
export function getTreeDataPerson(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getTreeDataPerson);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}
// 新增人员
export function addPersonManage(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.addPersonManage);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}

// 删除人员信息
export function deletePersonManage(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.deletePersonManage);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}
export function getHistory(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getPersonnelSafetyHistory, params);
  return $http.get(url, {
    data: { _cfg: { showTip: true } },
  });
}
export function getRecord(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getPersonRecord, params);
  return $http.get(url, {
    data: { _cfg: { showTip: true } },
  });
}
export function getRecordList(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getPersonRecordList, params);
  return $http.get(url, {
    data: { _cfg: { showTip: true } },
  });
}
export function getHistoryList(data: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getPersonHistoryList);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
export function getNum(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getRoutineNum, params);
  return $http.get(url, {
    data: { _cfg: { showTip: true } },
  });
}
export function getNumList(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getRoutineList, params);
  return $http.get(url, {
    data: { _cfg: { showTip: true } },
  });
}
// 管控职责-管控区域
export function getRegionalManagementData(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getRegionalManagementList, params);
  return $http.get(url, {
    data: { _cfg: { showTip: true } },
  });
}
// 管控职责-风险管控清单
export function getRiskIdentificationListByResponsiblerData(data: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getRiskIdentificationListByResponsibler);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
// 管控职责-风险管控统计
export function getStatisticsByResponsibleData(data: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getStatisticsByResponsible);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
// 获取管控区域负责人所管控区域的楼层信息;
export function getForeignKeyRegionalData(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getForeignKeyRegionalList, params);
  return $http.get(url, {
    data: { _cfg: { showTip: true } },
  });
}
// 获取安全作业记录;
export function getOperationBasePageData(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getOperationBasePageList, params);
  return $http.get(url, {
    data: { _cfg: { showTip: true } },
  });
}
// 获取安全作业记录;
export function getOperationComplianceStatisticsData(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getOperationComplianceStatisticsList, params);
  return $http.get(url, {
    data: { _cfg: { showTip: true } },
  });
}
// 获取事故记录统计项;
export function getAccidentStatistics(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getAccidentStatisticsList, params);
  return $http.get(url, {
    data: { _cfg: { showTip: true } },
  });
}
// 获取事故记录统计表;
export function getAccidentRecordData(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getAccidentRecordList, params);
  return $http.get(url, {
    data: { _cfg: { showTip: true } },
  });
}
// 获取隐患级别
export function gelevelData(data: any) {
  const url = api.getUrl(api.type.server, api.name.interface.gelevelList);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
// 获取隐患等级
export function getlevelStatisticsData(data: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getlevelStatisticsList);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
export function getStatisticsData(data: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getStatisticsList);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
export function getPageEventData(data: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getPageEventList);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
// 获取隐患是否超期;
export function getis0verdueDayData(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getis0verdueDayList, params);
  return $http.get(url, {
    data: { _cfg: { showTip: true } },
  });
}

/**
 * @description 获取人员履历信息
 * @param query
 */
export function queryResultList(query: any) {
  const url = api.getUrl(api.type.server, api.name.interface.queryResultList, query);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}

/**
 * @description 更新人员履历信息
 * @param params
 */
export function updateResult(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.updateResult);
  return $http.post(url, { data: { _cfg: { showTip: true }, ...params } });
}
