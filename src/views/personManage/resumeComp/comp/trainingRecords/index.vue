<template>
  <div class="records">
    <div class="header">
      <div class="text">
        <p>{{ (props && props.obj.userName) || '--' }}</p>
      </div>
      <div class="content">
        <div class="list1">
          <li :title="props && props.obj.unitName">企业：{{ (props && props.obj.unitName) || '--' }}</li>
          <li :title="props && props.obj.deptName">部门：{{ (props && props.obj.deptName) || '--' }}</li>
          <li :title="props && props.obj.postName">岗位：{{ (props && props.obj.postName) || '--' }}</li>
        </div>
        <div class="list2">
          <li>共培训 {{ (newObj && newObj.trainTimes) || '0' }}次</li>
          <li>签到次数 {{ (newObj && newObj.signInTimes) || '0' }}次</li>
          <li>考核通过 {{ (newObj && newObj.passTimes) || '0' }}次</li>
        </div>
      </div>
    </div>
    <div class="table" style="margin-top: 20px">
      <n-data-table
        :data="tableData"
        class="h-full"
        style="--n-merged-th-color: #bbccf3"
        remote
        striped
        :columns="cols"
        :bordered="false"
        :loading="loading"
        :render-cell="useEmptyCell"
        :theme-overrides="themeOverrides"
        :pagination="pagination"
        :max-height="480"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { cols } from './columns';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { getRecord, getRecordList } from '../../../fetchData.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
const { pagination, updateTotal } = useNaivePagination(getTableData);
const [loading, search] = useAutoLoading(true);
const props = defineProps({
  obj: {
    type: Object,
  },
});
const themeOverrides = {
  tdColorStriped: '#dfeefc',
  // tdColorHover: 'rgba(18, 83, 123, 0.35)',
  tdColorStripedModal: '#dfeefc',
  thColor: '#BBCCF3',

  thTextColor: '#222',
  // tdColorHoverModal: 'rgba(18, 83, 123, 0.35)',
  // tdColorHoverPopover: 'rgba(18, 83, 123, 0.35)',
};
const tableData = ref();
function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    userId: props.obj.userId,
    deptIds: props.obj.deptId,
  };
  search(getRecordList(params)).then((res: any) => {
    if (res.code != 200) return;
    tableData.value = res.data.personalTrainDetails || [];
    updateTotal(res.data.total || 0);
  });
  // tableData.value = [
  //   {
  //     id: 1,
  //     name: '国庆安全培训',
  //     time: '2024-10-10~2024-10-10',
  //     shichang: '0',
  //     people: '齐矿管理员',
  //     shijian: '',
  //     status: '否',
  //     num: '',
  //     qingkuang: '',
  //   },
  //   {
  //     id: 2,
  //     name: '国庆安全培训',
  //     time: '2024-10-10~2024-10-10',
  //     shichang: '0',
  //     people: '齐矿管理员',
  //     shijian: '',
  //     status: '否',
  //     num: '',
  //     qingkuang: '',
  //   },
  //   {
  //     id: 2,
  //     name: '国庆安全培训',
  //     time: '2024-10-10~2024-10-10',
  //     shichang: '0',
  //     people: '齐矿管理员',
  //     shijian: '',
  //     status: '否',
  //     num: '',
  //     qingkuang: '',
  //   },
  //   {
  //     id: 2,
  //     name: '国庆安全培训',
  //     time: '2024-10-10~2024-10-10',
  //     shichang: '0',
  //     people: '齐矿管理员',
  //     shijian: '',
  //     status: '否',
  //     num: '',
  //     qingkuang: '',
  //   },
  //   {
  //     id: 2,
  //     name: '国庆安全培训',
  //     time: '2024-10-10~2024-10-10',
  //     shichang: '0',
  //     people: '齐矿管理员',
  //     shijian: '',
  //     status: '否',
  //     num: '',
  //     qingkuang: '',
  //   },
  //   {
  //     id: 2,
  //     name: '国庆安全培训',
  //     time: '2024-10-10~2024-10-10',
  //     shichang: '0',
  //     people: '齐矿管理员',
  //     shijian: '',
  //     status: '否',
  //     num: '',
  //     qingkuang: '',
  //   },
  //   {
  //     id: 2,
  //     name: '国庆安全培训',
  //     time: '2024-10-10~2024-10-10',
  //     shichang: '0',
  //     people: '齐矿管理员',
  //     shijian: '',
  //     status: '否',
  //     num: '',
  //     qingkuang: '',
  //   },
  //   {
  //     id: 2,
  //     name: '国庆安全培训',
  //     time: '2024-10-10~2024-10-10',
  //     shichang: '0',
  //     people: '齐矿管理员',
  //     shijian: '',
  //     status: '否',
  //     num: '',
  //     qingkuang: '',
  //   },
  //   {
  //     id: 2,
  //     name: '国庆安全培训',
  //     time: '2024-10-10~2024-10-10',
  //     shichang: '0',
  //     people: '齐矿管理员',
  //     shijian: '',
  //     status: '否',
  //     num: '',
  //     qingkuang: '',
  //   },

  //   {
  //     id: 2,
  //     name: '国庆安全培训',
  //     time: '2024-10-10~2024-10-10',
  //     shichang: '0',
  //     people: '齐矿管理员',
  //     shijian: '',
  //     status: '否',
  //     num: '',
  //     qingkuang: '',
  //   },
  // ];
  // updateTotal(0);
}
const newObj = ref();
const getDetail = async () => {
  let res = await getRecord({
    userId: props.obj.userId,
    deptIds: props.obj.deptId,
  });
  newObj.value = res.data;
  console.log(res, '>>>>>>>>>>>');
};
onMounted(() => {
  getDetail();
  getTableData();
});
defineOptions({ name: 'trainingRecordsComp' });
</script>

<style lang="scss" scoped>
.records {
  width: 100%;
  height: 100%;
  .header {
    list-style: none;
    width: 100%;
    height: 150px;
    border-radius: 10px;
    // background-color: #5da0ff;
    color: white;
    display: flex;
    background-image: url('./bj.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .text {
      font-size: 26px;
      padding-left: 100px;
      padding-top: 50px;
    }
    .content {
      padding-top: 40px;
      .list1 {
        display: flex;
        margin-left: 10%;
        width: 900px;
        justify-content: space-around;
        li {
          width: 33.3%;
          overflow: hidden; /* 隐藏溢出部分 */
          text-overflow: ellipsis; /* 使用省略号表示溢出 */
          white-space: nowrap; /* 不允许文本换行 */
          display: block; /* 确保li块元素正常显示 */
          // margin-left: 70px;
        }
      }
      .list2 {
        display: flex;
        margin-top: 20px;
        margin-left: 10%;
        width: 900px;
        justify-content: space-around;
        li {
          width: 33.3%;
          overflow: hidden; /* 隐藏溢出部分 */
          text-overflow: ellipsis; /* 使用省略号表示溢出 */
          white-space: nowrap; /* 不允许文本换行 */
          display: block; /* 确保li块元素正常显示 */
          // margin-left: 70px;
        }
        li:nth-child(2) {
          // margin-left: 23%;
        }
        li:nth-child(3) {
          // margin-left: 12%;
        }
      }
    }
  }
}
</style>
