import { h } from 'vue';
import { defineComponent } from 'vue';

export const tagBgs = ['#F39600', '#00B578', '#527CFF'];

export const disposeState = defineComponent({
  props: {
    row: {
      type: Object,
      default: () => ({}),
    },
    field: {
      type: String,
      default: 'disposeStateName',
    },
  },
  setup(props) {
    return () =>
      h(
        'div',
        {
          className: 'inline-block w-[66px] leading-[30px] h-[30px]  text-center rounded-[4px] text-white',
          style: `background-color: ${tagBgs[props.row.disposeState]}`,
        },
        { default: () => props.row[props.field] }
      );
  },
});
