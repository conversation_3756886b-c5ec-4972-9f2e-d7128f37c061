<template>
  <div class="box">
    <!-- <img class="shi_li" src="@/views/personManage/resumeComp/comp/responsibility/shili.png" alt="" /> -->

    <div class="flex justify-start items-center w-full">
      <div class="other-card bg1">
        <div class="ml-[60px]">
          <div>隐患数量</div>
          <n-statistic tabular-nums style="--n-value-text-color: #ff9500">
            <n-number-animation :from="0" :to="statisticsData.total" />
          </n-statistic>
        </div>
      </div>
      <div class="other-card ml-3 bg2">
        <div style="z-index: 2">
          <div>整改中</div>
          <n-statistic tabular-nums style="--n-value-text-color: #00b578">
            <n-number-animation :from="0" :to="statisticsData.disposingNum" />
          </n-statistic>
        </div>
      </div>
      <div class="other-card ml-3 bg2">
        <div style="z-index: 2">
          <div>已整改</div>
          <n-statistic tabular-nums style="--n-value-text-color: #00b578">
            <n-number-animation :from="0" :to="statisticsData.disposedNum" />
          </n-statistic>
        </div>
      </div>
      <div class="other-card ml-3 bg3">
        <div style="z-index: 2">
          <div>待整改</div>

          <n-statistic tabular-nums style="--n-value-text-color: #fa5151">
            <n-number-animation :from="0" :to="statisticsData.unDisposedNum" />
          </n-statistic>
        </div>
      </div>
      <div class="other-card ml-3 bg4">
        <div style="z-index: 2">
          <div>超期数量</div>
          <n-statistic tabular-nums style="--n-value-text-color: #ff9500">
            <n-number-animation :from="0" :to="statisticsData.timeout" />
          </n-statistic>
        </div>
      </div>
      <div
        class="other-card other-card1 ml-3"
        style="width: 500px"
        v-if="levelStatisticsData && levelStatisticsData.length"
      >
        <div style="z-index: 2" class="flex justify-start">
          <div
            v-for="(item, idx) in levelStatisticsData"
            :key="idx"
            :style="{ width: `${100 / levelStatisticsData.length}%` }"
          >
            <div class="truncate-text" :title="item.hazardLevelName">
              {{ item.hazardLevelName }}
            </div>
            <n-statistic
              tabular-nums
              :style="{ '--n-value-text-color': colorList[idx] }"
            >
              <n-number-animation :from="0" :to="item.total" />
            </n-statistic>
          </div>
        </div>
      </div>
    </div>

    <div>
      <n-form class="my-[24px]" :show-feedback="false" label-placement="left">
        <n-flex :size="[20, 10]">
          <n-form-item label="隐患级别:">
            <n-select
              class="!w-[200px]"
              v-model:value="filterForm.hazardLevel"
              placeholder="请选择"
              clearable
              :options="gradeNameOptions"
              label-field="gradeName"
              value-field="id"
            />
          </n-form-item>
          <n-form-item label="整改状态:">
            <n-select
              class="!w-[200px]"
              v-model:value="filterForm.disposeState"
              clearable
              :options="disposeStateOptions"
            />
          </n-form-item>
          <n-form-item label="是否超期:">
            <n-select
              class="!w-[200px]"
              placeholder="请选择"
              v-model:value="filterForm.timeoutDays"
              clearable
              :options="hazardOverdueOptions"
            />
          </n-form-item>
          <n-form-item label="上报时间:">
            <n-date-picker
              class="!w-[280px]"
              v-model:formatted-value="timeRange"
              value-format="yyyy-MM-dd"
              type="daterange"
              clearable
            />
          </n-form-item>
        </n-flex>
      </n-form>

      <n-data-table
        class="h-full com-table mt-[10px]"
        style="--n-merged-th-color: #bbccf3"
        :theme-overrides="themeOverrides"
        :bordered="false"
        :columns="columns"
        remote
        striped
        :data="tableData"
        :render-cell="useEmptyCell"
        :loading="loading"
        :pagination="pagination"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h, onMounted, watch } from 'vue';
import { disposeState } from './disposeState.tsx';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import {
  getRegionalManagementData,
  getForeignKeyRegionalData,
  gelevelData,
  getlevelStatisticsData,
  getStatisticsData,
  getPageEventData,
  getis0verdueDayData,
} from '../../../fetchData';
const props: any = defineProps({
  obj: {
    type: Object,
  },
});
const [loading, search] = useAutoLoading(false);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const themeOverrides = {
  tdColorStriped: '#dfeefc',
  // tdColorHover: 'rgba(18, 83, 123, 0.35)',
  tdColorStripedModal: '#dfeefc',
  thColor: '#BBCCF3',

  thTextColor: '#222',
  // tdColorHoverModal: 'rgba(18, 83, 123, 0.35)',
  // tdColorHoverPopover: 'rgba(18, 83, 123, 0.35)',
};

const statisticsData = ref<any>({
  total: 0,
  disposedNum: 0,
  disposingNum: 0,
  unDisposedNum: 0,
  timeout: 0,
});
const levelStatisticsData = ref<any[]>([
  // {
  //   name: '重大隐患',
  //   total: 0,
  // },
  // {
  //   name: '较大隐患',
  //   total: 1,
  // },
  // {
  //   name: '一般隐患',
  //   total: 0,
  // },
]);

const colorList = ['#e23b50', '#f59a23', '#bfbf00', '#76b90e'];
const hazardOverdueOptions = ref([
  { label: '全部', value: '' },
  { label: '否', value: 0 },
  // { label: '3天', value: 3 },
  // { label: '7天', value: 7 },
  // { label: '30天', value: 30 },
]);

const disposeStateOptions = [
  { label: '全部', value: '' },
  { label: '待整改', value: 0 },
  { label: '已整改', value: 1 },
  { label: '整改中', value: 2 },
];

const filterForm = ref({
  hazardLevel: '',
  disposeState: '',
  timeoutDays: '',
  startTime: '',
  endTime: '',
  searchCount: 0,
  roleCodes: [null],
});
watch(
  () => filterForm.value.timeoutDays,
  (nv) => {
    // console.log(nv);
    if (nv === null) {
      filterForm.value.timeoutDays = '';
    }
  }
);
watch(
  () => filterForm.value.hazardLevel,
  (nv) => {
    console.log(nv);
    if (nv === null) {
      filterForm.value.hazardLevel = '';
    }
  }
);
watch(
  () => filterForm.value.disposeState,
  (nv) => {
    console.log(nv);
    if (nv === null) {
      filterForm.value.disposeState = '';
    }
  }
);
const timeRange = computed({
  get: () => {
    if (filterForm.value.startTime && filterForm.value.endTime) {
      return [filterForm.value.startTime, filterForm.value.endTime];
    }
    return undefined;
  },
  set: (value) => {
    if (!value) {
      filterForm.value.startTime = '';
      filterForm.value.endTime = '';
      return;
    }
    filterForm.value.startTime = value[0];
    filterForm.value.endTime = value[1];
  },
});
const getDefaultDateRange = (days = 30) => {
  // 获取今天的日期
  const today = new Date(+new Date() + 8 * 3600 * 1000);

  // 获取30天前的日期
  const thirtyDaysAgo = new Date(today);
  thirtyDaysAgo.setDate(today.getDate() - days);

  // 将日期格式化为字符串，例如："2023-09-23"
  const formattedToday = today.toISOString().split('T')[0];
  const formattedThirtyDaysAgo = thirtyDaysAgo.toISOString().split('T')[0];

  // 输出结果
  console.log(`[${formattedThirtyDaysAgo}, ${formattedToday}]`);
  return [formattedThirtyDaysAgo, formattedToday];
};

timeRange.value = getDefaultDateRange();
const columns = [
  {
    title: '隐患单位',
    key: 'unitName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患来源',
    key: 'hazardSourceName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患位置',
    key: 'hazardPosition',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患描述',
    key: 'hazardDesc',
    align: 'center',
    width: 180,
    ellipsis: {
      'line-clamp': 3,
    },
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患类别',
    key: 'hazardTypeName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患级别',
    key: 'hazardLevelName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '上报时间',
    key: 'eventTime',
    align: 'center',
    width: 180,

    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '整改状态',
    key: 'disposeStateName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => h(disposeState, { row }),
  },
  {
    title: '是否超期',
    key: 'timeoutDays',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      if (row.timeoutDays == 0) {
        return '否';
      } else if (!row.timeoutDays) {
        return '--';
      } else {
        return `已超期${row.timeoutDays}天`;
      }
    },
  },
];

const tableData = ref<any[]>([]);

const gradeNameOptions = ref([{ id: '', gradeName: '全部隐患' }]);
const getData = async () => {
  let res = await gelevelData({
    delFlag: 0,
    unitId: props.obj.unitId,
  });
  // console.log(res, '>>>>>>>>>>>>>>>>..');
  gradeNameOptions.value = [...gradeNameOptions.value, ...res.data];
  // console.log(gradeNameOptions.value, 'gradeNameOptions.value');
};
const getData3 = async () => {
  let res = await getis0verdueDayData({ unitId: props.obj.unitId });
  // console.log(res, '>>>>>>>>>>>>>>>>');
  const newArr = res.data.map((item) => {
    return {
      label: item.overdueDay + '天',
      value: item.overdueDay,
    };
  });
  hazardOverdueOptions.value = [...hazardOverdueOptions.value, ...newArr];
};
const buildIdd = ref('');
const buildingIds = ref('');
const floorIdd = ref('');
const floorIds = ref('');
const getData2 = async () => {
  getRegionalManagementData({
    id: props.obj.userId,
    pageNo: 0,
    pageSize: -1,
    unitId: props.obj.unitId,
  }).then(async (regionalData: any) => {
    if (regionalData.data.regionalManagementDtoList.length > 0) {
      const idString = regionalData.data.regionalManagementDtoList
        .map((item: any) => item.id)
        .join(',');
      getForeignKeyRegionalData({
        regionalManagementId: idString,
      }).then((foreignKeyData: any) => {
        if (foreignKeyData.data.foreignKeyRegionalVoList.length > 0) {
          buildIdd.value =
            foreignKeyData.data.foreignKeyRegionalVoList[0].buildId;
          buildingIds.value = foreignKeyData.data.foreignKeyRegionalVoList
            .map((item: any) => item.buildId)
            .join(',');
          floorIdd.value =
            foreignKeyData.data.foreignKeyRegionalVoList[0].floorId;
          floorIds.value = foreignKeyData.data.foreignKeyRegionalVoList
            .map((item: any) => item.floorId)
            .filter((id: string) => id !== '') // 过滤掉空字符串
            .join(','); // 连接成以逗号分隔的字符串
          // floorIds.value.forEach((item) => {
          //   if (item !== '') {
          //     floorIds.value.join(',');
          //   } else {
          //     floorIds.value = '';
          //   }
          // });
          // console.log(floorIds.value, '>>>>>>>>>>>>>>>.');
          // const params = {};
          getLevelData();
          getStatusData();
          getTableData();
        } else {
          getLevelData();
          getStatusData();
          getTableData();
        }
      });
    } else {
      getLevelData();
      getStatusData();
      getTableData();
    }
  });
};
const getLevelData = async () => {
  const obj = {
    pageNo: 0,
    pageSize: -1,
    buildingId: buildingIds.value,
    floorId: floorIds.value,
    reformUserId: props.obj.userId,
    unitId: props.obj.unitId,
    ...filterForm.value,
  };
  let res = await getlevelStatisticsData(obj);
  // console.log(res, '>>>>>>>>>>>>>>>>.');
  if (buildingIds.value === '' && floorIds.value === '') {
    levelStatisticsData.value = res.data.map((item) => {
      return {
        ...item,
        total: 0,
      };
    });
  } else {
    levelStatisticsData.value = res.data;
  }
};
// const statusObj: any = ref({});
const getStatusData = async () => {
  const obj = {
    pageNo: 0,
    pageSize: -1,
    buildingId: buildingIds.value,
    floorId: floorIds.value,
    reformUserId: props.obj.userId,
    unitId: props.obj.unitId,
    ...filterForm.value,
  };
  let res = await getStatisticsData(obj);
  if (buildingIds.value === '' && floorIds.value === '') {
    statisticsData.value = {
      total: 0,
      disposedNum: 0,
      disposingNum: 0,
      unDisposedNum: 0,
      timeout: 0,
    };
  } else {
    statisticsData.value = res.data;
  }
};
function getTableData() {
  const obj = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    buildingId: buildingIds.value,
    floorId: floorIds.value,
    reformUserId: props.obj.userId,
    unitId: props.obj.unitId,
    ...filterForm.value,
  };
  search(getPageEventData(obj)).then((res: any) => {
    if (buildingIds.value === '' && floorIds.value === '') {
      tableData.value = [];
      updateTotal(0);
    } else {
      tableData.value = res.data.hazardTaskRecordVoList || [];
      updateTotal(res.data.total || 0);
    }
    // console.log(re
  });
}
onMounted(() => {
  getData();
  getData2();
  getData3();
  // getTableData();
});
watch(
  () => filterForm.value,
  (nv) => {
    // console.log(nv, '>>>>>>>>>>>>');
    if (buildingIds.value !== '' || floorIds.value !== '') {
      pagination.page = 1;
      // const params = {
      //   reformUserId: props.obj.userId,
      //   unitId: props.obj.unitId,
      //   ...filterForm.value,
      // };
      getLevelData();
      getStatusData();
      getTableData();
    } else {
      return;
    }
  },
  { immediate: true, deep: true } // 添加 deep: true
);
defineOptions({ name: 'hiddenDanger' });
</script>
<style lang="scss" scoped>
.box {
  position: relative;
}
.shi_li {
  position: absolute;
  right: 12px;
  top: 12px;
  z-index: 9999;
}
::v-deep .n-statistic-value {
  margin-top: 0;
}
.bg1 {
  background-size: 100% 100%;
  background-image: url('@/views/personManage/resumeComp/comp/hiddenDanger/bg1.png');
}
.bg2 {
  background-size: 100% 100%;
  background-image: url('@/views/personManage/resumeComp/comp/hiddenDanger/bg2.png');
}
.bg3 {
  background-size: 100% 100%;
  background-image: url('@/views/personManage/resumeComp/comp/hiddenDanger/bg3.png');
}
.bg4 {
  background-size: 100% 100%;
  background-image: url('@/views/personManage/resumeComp/comp/hiddenDanger/bg4.png');
}

.other-card {
  position: relative;
  width: 207px;
  height: 80px;
  padding: 15px 32px 17px 20px;
}
.other-card1 {
  width: 465px;
  height: 78px;
  background: linear-gradient(180deg, #ffffff 0%, #e5ecff 100%);
  box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
  padding: 15px 32px 17px 32px;
  border-radius: 8px;
}
.truncate-text {
  white-space: nowrap; /* 禁止换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
  max-width: 200px; /* 设置最大宽度（按需调整） */
}
</style>
