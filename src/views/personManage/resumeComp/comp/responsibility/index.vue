<template>
  <div class="box">
    <!-- <img class="shi_li" src="@/views/personManage/resumeComp/comp/responsibility/shili.png" alt="" /> -->

    <n-card v-if="userInfo.zhId !== 'ycsyqt'">
      <ComHeaderB title="责任管控区域" :blueIcon="true" class="mb-[20px] cursor-pointer" />

      <n-data-table
        class="h-full com-table"
        remote
        striped
        style="--n-merged-th-color: #bbccf3"
        :render-cell="useEmptyCell"
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :bordered="false"
      />
    </n-card>

    <div class="flex justify-start items-center w-full my-[16px]">
      <div class="other-card flex justify-start items-center bg1">
        <div style="z-index: 2">
          <div>风险点总数</div>
          <n-statistic tabular-nums style="--n-value-text-color: #fff">
            <n-number-animation show-separator :from="0" :to="newObj && newObj.createdPoint" />
          </n-statistic>
        </div>
      </div>

      <div class="other-card flex justify-start items-center ml-[16px] bg2">
        <div style="z-index: 2">
          <div>危险源总数</div>
          <n-statistic tabular-nums style="--n-value-text-color: #fff">
            <n-number-animation show-separator :from="0" :to="newObj && newObj.identifiedDanger" />
          </n-statistic>
        </div>
      </div>

      <div class="other-card flex justify-start items-center ml-[16px] bg3">
        <div style="z-index: 2">
          <div>管控措施</div>
          <n-statistic tabular-nums style="--n-value-text-color: #fff">
            <n-number-animation show-separator :from="0" :to="newObj && newObj.identifiedMeasure" />
          </n-statistic>
        </div>
      </div>
    </div>

    <n-card>
      <ComHeaderB title="风险管控清单" :blueIcon="true" class="mb-[20px] cursor-pointer" />

      <n-data-table
        class="h-full com-table"
        style="--n-merged-th-color: #bbccf3"
        remote
        striped
        :loading="loading"
        :bordered="false"
        :columns="columns2"
        :render-cell="useEmptyCell"
        :data="tableData2"
        :pagination="pagination"
      />
    </n-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, h, render, onMounted } from 'vue';
import { NButton, NImage } from 'naive-ui';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import ComHeaderB from '@/components/header/ComHeaderB.vue';
import {
  getRegionalManagementData,
  getRiskIdentificationListByResponsiblerData,
  getStatisticsByResponsibleData,
} from '../../../fetchData';
import { useStore } from '@/store/index';
import { useNaivePagination1 } from '@/common/hooks/useNaivePagination2.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { Search } from '@vicons/ionicons5';
const { pagination1, updateTotal1 } = useNaivePagination1(getTableData);
const { pagination, updateTotal } = useNaivePagination(getTableData2);
const [loading, search] = useAutoLoading(false);
const { userInfo } = useStore();
const props = defineProps({
  obj: {
    type: Object,
  },
});

const columns = [
  {
    title: '区域名称',
    key: 'regionalName',
    width: 300,
    // ellipsis: {
    //   tooltip: true,
    // },
  },
  {
    title: '管控部门',
    key: 'controlDept',
    width: 300,
    // ellipsis: {
    //   tooltip: true,
    // },
  },
  {
    title: '区域信息',
    key: 'regionalMsg',
    width: 300,
    ellipsis: {
      tooltip: true,
    },
  },
];
const tableData = ref([
  // {
  //   id: '1',
  //   name: '区域名称',
  //   info: 'A 栋B 层、B 栋C 层…',
  // },
  // {
  //   id: '2',
  //   name: '区域名称',
  //   info: 'A 栋B 层、B 栋C 层…',
  // },
]);
function getTableData() {
  const params = {
    id: props.obj.userId,
    pageNo: 0,
    pageSize: -1,
    unitId: props.obj.unitId,
  };
  search(getRegionalManagementData(params)).then((res: any) => {
    // console.log(res, '>>>>>>>>>>>>>');
    if (res.code != 200) return;
    tableData.value = res.data.regionalManagementDtoList || [];
    updateTotal1(res.data.total || 0);
  });
}
const bg = {
  [4]: '#6e91ff',
  [1]: '#df293f',
  [2]: '#ec8830',
  [3]: '#ffdd00',
};

const columns2 = [
  //   {
  //     title: '序号',
  //     key: 'index',
  //     width: 65,
  //     align: 'center',
  //     ellipsis: {
  //       tooltip: true,
  //     },
  //     render: (_: any, index: number) => {
  //       return index + 1;
  //     },
  //   },
  {
    title: '风险点编号',
    key: 'riskPointCode',
    // ellipsis: {
    //   tooltip: true,
    // },
  },
  {
    title: '风险点名称',
    key: 'riskPointName',
    // ellipsis: {
    //   tooltip: true,
    // },
  },
  {
    title: '风险点类型',
    key: 'riskPointType',
    // ellipsis: {
    //   tooltip: true,
    // },
    render: (row) => {
      if (row.riskPointType === '1') {
        return '设备实施';
      } else if (row.riskPointType === '2') {
        return '部位场所';
      } else {
        return '作业活动';
      }
    },
  },
  {
    title: '辨识数据',
    key: 'bsData',
    // ellipsis: {
    //   tooltip: true,
    // },
    render(row: any, index: number) {
      if (row.bsData) {
        const items = row.bsData.split(',');
        // 创建一个新的数组以存储结果
        // const result = [];
        // 遍历每个项，按":"分割并将结果添加到平坦数组

        return h('div', {}, [h('div', null, items[0]), h('div', null, items[1]), h('div', null, items[2])]);
      }
      // 使用逗号分割字符串
    },
  },
  // {
  //   title: '图示',
  //   key: 'ossImageUrls',
  //   // ellipsis: {
  //   //   tooltip: true,
  //   // },
  //   render(row: any, index: number) {
  //     //  class: 'flex items-center'
  //     if (row.ossImageUrls) {
  //       return h(NImage, {
  //         src: row.ossImageUrls,
  //         style: 'width:40px;height:40px',
  //       });
  //     } else {
  //       return '--';
  //     }
  //   },
  // },
  {
    title: '固有风险',
    key: 'riskLevel',
    // ellipsis: {
    //   tooltip: true,
    // },
    render(row: any, index: number) {
      return h(
        'div',
        {
          className: 'inline-block w-[66px] leading-[30px] h-[30px]  text-center rounded-[4px] text-white',
          style: `background-color: ${bg[row.riskLevel]}`,
        },
        {
          default: () => {
            if (row.riskLevel === '1') {
              return '重大风险';
            } else if (row.riskLevel === '2') {
              return '较大风险';
            } else if (row.riskLevel === '3') {
              return '一般风险';
            } else {
              return '低风险';
            }
          },
        }
      );
    },
  },
  {
    title: '现状风险',
    key: 'currentRiskLevel',
    // ellipsis: {
    //   tooltip: true,
    // },
    render(row: any, index: number) {
      return h(
        'div',
        {
          className: 'inline-block w-[66px] leading-[30px] h-[30px]  text-center rounded-[4px] text-white',
          style: `background-color: ${bg[row.currentRiskLevel]}`,
        },
        {
          default: () => {
            if (row.currentRiskLevel === '1') {
              return '重大风险';
            } else if (row.currentRiskLevel === '2') {
              return '较大风险';
            } else if (row.currentRiskLevel === '3') {
              return '一般风险';
            } else {
              return '低风险';
            }
          },
        }
      );
    },
  },
  {
    title: '管控层级',
    key: 'levelName',
    // ellipsis: {
    //   tooltip: true,
    // },
  },
];
const tableData2 = ref([
  // {
  //   id: '1',
  //   code: 'FXD001',
  //   riskPointCode: '许东沟排岩场',
  //   typeName: '部位场所',
  //   pic: '',
  //   bsData: '步骤或子单元:1个,危险源:1个,管理措施:10条',
  //   riskLevel: 1,
  //   cj: '公司级',
  //   currentRiskLevel: 4,
  // },
  // {
  //   id: '2',
  //   code: 'FXD003',
  //   name: '哑巴岭排岩场',
  //   typeName: '部位场所',
  //   pic: '',
  //   cuoshi: 12,
  //   fxRisk: '低风险',
  //   cj: '部门级',
  // },
  // {
  //   id: '3',
  //   code: 'FXD004',
  //   name: '采场通勤',
  //   typeName: '部位场所',
  //   pic: '',
  //   cuoshi: 26,
  //   fxRisk: '重大风险',
  //   cj: '公司级',
  // },
  // {
  //   id: '2',
  //   code: 'FXD005',
  //   name: '2号锅炉',
  //   typeName: '部位场所',
  //   pic: '',
  //   cuoshi: 26,
  //   fxRisk: '较大风险',
  //   cj: '部门级',
  // },
]);
function getTableData2() {
  const params = {
    responsibleUserId: props.obj.userId,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    identificationStatus: '3',
    searchCount: 0,
  };
  search(getRiskIdentificationListByResponsiblerData(params)).then((res: any) => {
    // console.log(res, '>>>>>>>>>>>>>');
    if (res.code != 200) return;
    tableData2.value = res.data.riskIdentificationVOList || [];
    updateTotal(res.data.total || 0);
  });
}
const newObj = ref({});
const getTotal = async () => {
  const params = {
    responsibleUserId: props.obj.userId,
    pageNo: 0,
    pageSize: -1,
    identificationStatus: '3',
    searchCount: 0,
  };
  let res = await getStatisticsByResponsibleData(params);
  // console.log(res, '>>>>>>>>>>>>>>>>>>>>..');
  newObj.value = res.data;
};
onMounted(() => {
  getTableData();
  getTableData2();
  getTotal();
});
defineOptions({ name: 'responsibilityComp' });
</script>

<style lang="scss" scoped>
.box {
  position: relative;
}
.shi_li {
  position: absolute;
  right: 12px;
  top: 12px;
}
.bg1 {
  background-size: 100% 100%;
  background-image: url('@/views/personManage/resumeComp/comp/responsibility/icon1.png');
}
.bg2 {
  background-size: 100% 100%;
  background-image: url('@/views/personManage/resumeComp/comp/responsibility/icon2.png');
}

.bg3 {
  background-size: 100% 100%;
  background-image: url('@/views/personManage/resumeComp/comp/responsibility/icon3.png');
}

.other-card {
  position: relative;
  // width: 260px;
  // height: 80px;
  padding: 16px 0 14px 20px;
  width: 279px;
  height: 103px;
  color: #fff;
}
</style>
