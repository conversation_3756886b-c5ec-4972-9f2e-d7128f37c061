<template>
  <div class="resume-edit-wrap h-[710px]">
    <div class="table-wrap">
      <n-data-table
        remote
        max-height="600"
        :striped="false"
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :render-cell="useEmptyCell"
      />
    </div>
    <div class="flex justify-end gap-[20px]">
      <n-button @click="handleClose">取消</n-button>
      <n-button :disabled="!tableData.length" type="primary" @click="handleSubmit">确定</n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { renderCol } from './columns.ts';
import { queryResultList, updateResult } from '@/views/personManage/fetchData.ts';
import { IEvaluationResult } from './type.ts';
import { DataTableColumn } from 'naive-ui';
import { $toast } from '@/common/shareContext/useToastCtx.ts';

const [loading, search] = useAutoLoading(false);

const props = defineProps(['userData']);

const columns = ref<DataTableColumn[]>([]);

const tableData = ref<Partial<IEvaluationResult[]>>([]);

function getTableData() {
  const { userId, unitId } = props.userData;
  search(queryResultList({ userId, unitId })).then((res: any) => {
    if (+res.code !== 200) return;
    tableData.value = res.data || [];
  });
}

getTableData();

watch(
  () => tableData.value,
  (val: any[]) => {
    columns.value = renderCol(val);
  }
);

const emits = defineEmits(['close']);
function handleClose() {
  emits('close');
}
function handleSubmit() {
  updateResult({ presumeEvaluateDimensionVoList: tableData.value }).then((res: any) => {
    if (+res.code === 200) {
      $toast.success('更新成功！');
      handleClose();
    }
  });
}
defineOptions({ name: 'ResumeUpdate' });
</script>
<style scoped lang="scss">
.resume-edit-wrap {
  display: grid;
  grid-template-rows: 1fr auto;
  gap: 20px;
}
</style>
