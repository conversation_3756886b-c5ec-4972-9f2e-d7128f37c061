import { h } from 'vue';
import { DataTableColumn, NIcon, NInput } from 'naive-ui';
import UploadComp from './UploadComp.vue';
import { decimal } from '@/utils/inputValidate.ts';
export function renderCol(data: any) {
  return [
    {
      title: '评估维度',
      key: 'dimensionName',
      align: 'center',
      rowSpan: (row: any) => data.filter((item: any) => item.dimensionId === row.dimensionId).length || 1,
    },
    {
      title: '评估标准',
      key: 'standardName',
      align: 'center',
      rowSpan: (row: any) => data.filter((item: any) => item.standardId === row.standardId).length || 1,
    },
    {
      title: '评估指标',
      key: 'indexName',
      align: 'center',
    },
    {
      title: '评估指标履历说明',
      key: 'resultLabel',
      align: 'center',
      width: 200,
      render(row: any) {
        return h(NInput, {
          value: row.resultLabel,
          disabled: +row.evaluateType === 0,
          title: row.resultLabel,
          type: 'textarea',
          rows: 1,
          autosize: true,
          onUpdateValue(v) {
            row.resultLabel = v;
          },
        });
      },
    },
    {
      title: '累计数量',
      key: 'resultValue',
      align: 'center',
      width: 100,
      render: (row: any) => {
        return h(NInput, {
          value: row.resultValue,
          allowInput: decimal,
          disabled: +row.evaluateType === 0,
          onUpdateValue(v) {
            row.resultValue = v;
            row.realScore = row.standardScore * row.resultValue;
          },
        });
      },
    },
    {
      title: '评估分值',
      key: 'standardScore',
      align: 'center',
      width: 100,
    },
    {
      title: '评估指标得分',
      key: 'realScore',
      align: 'center',
      width: 110,
    },
    {
      title: '附件',
      key: 'attachmentList',
      align: 'center',
      render: (row: any) => {
        return h(
          NIcon,
          { size: 20 },
          h(UploadComp, {
            data: row.attachmentList,
            disabled: +row.evaluateType === 0,
            onUpdate: (fileList: any[]) => (row.attachmentList = [...fileList]),
          })
        );
      },
    },
  ] as DataTableColumn[];
}
