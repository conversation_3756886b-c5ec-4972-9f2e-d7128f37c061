/**
 * 履职评估结果
 */
export interface IEvaluationResult {
  /**
   * 附件内容
   */
  attachmentList?: IFileAttachment[];
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 创建人ID
   */
  createUserId: string;
  /**
   * 软删除标识：0正常使用，1已逻辑删除
   */
  delFlag: number;
  /**
   * 评估维度ID
   */
  dimensionId: string;
  /**
   * 评估维度名称
   */
  dimensionName: string;
  /**
   * 结果展示内容，例如：隐患整改率不足99%(85%)
   */
  evaluateItemVOList?: IEvaluateItem[];
  /**
   * 评估时间
   */
  evaluateTime: string;
  /**
   * 【冗余存储】评估方式: 0动态评估；1手动评估
   */
  evaluateType: number;
  /**
   * 主键
   */
  id: string;
  /**
   * 评估指标ID
   */
  indexId: string;
  /**
   * 评估指标名称
   */
  indexName: string;
  /**
   * 评估模型ID
   */
  modelId: string;
  /**
   * 评估模型名称
   */
  modelName: string;
  /**
   * 实际数量
   */
  realNum: number;
  /**
   * 实际得分，用于展示雷达图
   */
  realScore: number;
  /**
   * 结果展示内容，例如：隐患整改率不足99%(85%)
   */
  resultLabel: string;
  /**
   * 结果值，例如隐患整改率计算结果：85%
   */
  resultValue: string;
  /**
   * 评估标准ID
   */
  standardId: string;
  /**
   * 评估标准名称
   */
  standardName: string;
  /**
   * 【冗余存储】标准分值【负值表示扣分，正值表示加分】
   */
  standardScore: number;
  /**
   * 单位ID
   */
  unitId: string;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 更新人ID
   */
  updateUserId: string;
  /**
   * 用户ID
   */
  userId: string;
  /**
   * 租户ID
   */
  zhId: string;
}

/**
 * Attachment，附件表对象
 */
export interface IFileAttachment {
  /**
   * 附件地址
   */
  address: string;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 创建人用户id
   */
  createUserId: string;
  /**
   * 创建人
   */
  createUserName: string;
  /**
   * 附件ID
   */
  id: string;
  /**
   * 删除标识(0=正常，1=删除)
   */
  isDeleted: string;
  /**
   * 原始文件名
   */
  originalFilename: string;
  /**
   * 排序号
   */
  sortNum: number;
  /**
   * 对应附件表名称数据id
   */
  sourceId: string;
  /**
   * 附件类型:对应附件表名称
   */
  sourceType: string;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 更新人用户id
   */
  updateUserId: string;
  /**
   * 更新人
   */
  updateUserName: string;
}

/**
 * EvaluateItemVO，评估指标履历说明
 */
export interface IEvaluateItem {
  /**
   * 类型
   */
  itemType: string;
  /**
   * 值
   */
  itemValue: string;
}
