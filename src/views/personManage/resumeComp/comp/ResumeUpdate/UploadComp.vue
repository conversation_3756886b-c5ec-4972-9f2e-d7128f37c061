<template>
  <div class="flex justify-center items-center" :class="`gap-[${fileArr.length < maxFiles ? '20px' : '0'}]`">
    <n-popselect v-if="fileArr.length" :options="[]">
      <template #empty>
        <div class="flex flex-col gap-[5px]">
          <div class="file-li" v-for="file in fileArr" :key="file.id">
            <p class="flex-1 cursor-pointer" @click="previewFile(file)">{{ file.originalFilename }}</p>
            <n-icon
              class="cursor-pointer"
              :size="20"
              color="#ff0000"
              :component="DelIcon"
              @click="handleRemove(file)"
            />
          </div>
        </div>
      </template>
      <n-icon :component="EyeOn" />
    </n-popselect>

    <n-upload
      name="file"
      response-type="json"
      :action="actionURL"
      :show-file-list="false"
      :on-finish="handleUploadFinish"
      :on-before-upload="handleBeforeUpload"
      :disabled="props.disabled"
      :accept="accept"
      :max="maxFiles"
    >
      <n-icon v-if="fileArr.length < maxFiles && !props.disabled" :component="IconUpload" />
    </n-upload>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { AkEyeOpen as EyeOn, FaUpload as IconUpload, CaClose as DelIcon } from '@kalimahapps/vue-icons';
import { api } from '@/api';
import { Base64 } from 'js-base64';
import { UploadFileInfo } from 'naive-ui';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import { IFileAttachment } from './type.ts';

// 扩展IFileAttachment接口，添加业务需要的附加属性
interface ExFileAttachment extends IFileAttachment {
  url?: string;
  status?: string;
  res?: string;
}

interface Props {
  data: ExFileAttachment[];
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  disabled: false,
});
const emits = defineEmits(['update', 'remove']);

// 常量定义
const actionURL = api.getUrl(api.type.server, api.name.file.commonUploadFile);
const accept = '.pdf,.doc,.docx,.png,.jpg';
const maxFiles = 3;
const maxSizeMB = 10;
const fileArr = ref<ExFileAttachment[]>([]);

const addr = window.sessionStorage.getItem('ehs-org-alloc-mgr-address');
// 基本函数
const getFileURL = (filePath: string): string => window.$SYS_CFG.apiBase + filePath;

const getFileURL2 = (filePath: string): string => window.$SYS_CFG.apiPreviewURL.replace(addr, '') + filePath;

// 删除附件
const handleRemove = (file: ExFileAttachment) => {
  fileArr.value = fileArr.value.filter((item) => item.id !== file.id);
  emits('remove', file);
  emits('update', fileArr.value);
};

// 预览文件
const previewFile = (file: ExFileAttachment) => {
  if (file.url) window.open(file.url, '_blank');
};

// 上传前校验(文件大小、类型)
const handleBeforeUpload = ({ file }: { file: UploadFileInfo }): boolean => {
  if (!file.file) return false;

  const fileExt = file.name.slice(file.name.lastIndexOf('.') + 1).toLowerCase();
  const acceptedTypes = accept.split(',').map((type) => type.replace('.', '').toLowerCase());

  if (!acceptedTypes.includes(fileExt)) {
    $toast.error(`请上传 ${accept} 类型的文件!`);
    return false;
  }

  if (file.file.size / 1024 / 1024 > maxSizeMB) {
    $toast.error(`文件不能超过 ${maxSizeMB} MB，请重新上传!`);
    return false;
  }

  return true;
};

// 上传完成
const handleUploadFinish = ({ file, event }: { file: UploadFileInfo; event?: ProgressEvent }) => {
  if (!event?.target) return;

  const response = (event.target as any).response;
  if (!response?.data) return;

  const data = response.data as ExFileAttachment;
  fileArr.value.push(data);

  emits('update', fileArr.value);
};

// 监听props.data变化，更新fileArr
watch(
  () => props.data,
  (fileList) => {
    if (!fileList?.length) {
      fileArr.value = [];
      return;
    }

    fileArr.value = fileList
      .filter((item) => item !== ('/' as any))
      .map((file) => {
        const filename = file.originalFilename || '';
        const fileExt = filename.split('.').pop()?.toLowerCase() || '';
        const isWordDoc = fileExt === 'doc' || fileExt === 'docx';

        // 设置URL
        let url: string;
        if (isWordDoc) {
          const encodedUrl = Base64.encode(getFileURL2(file.address));
          url = window.$SYS_CFG.apiPreviewLink + encodeURIComponent(encodedUrl);
        } else {
          url = getFileURL(file.address);
        }

        return {
          ...file,
          url,
          status: 'finished',
          res: isWordDoc ? getFileURL2(file.address) : getFileURL(file.address),
        };
      });
  },
  { immediate: true, deep: true }
);

defineOptions({ name: 'UploadComp' });
</script>

<style scoped lang="scss">
.file-li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 30px;

  &:hover {
    color: #3e62eb;
  }
}
</style>
