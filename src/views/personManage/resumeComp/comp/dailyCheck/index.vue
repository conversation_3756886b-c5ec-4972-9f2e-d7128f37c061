<template>
  <div class="dailyCheck">
    <div class="header">
      <div class="flex justify-start items-center">
        <div class="trouble-number flex justify-start items-center">
          <div style="z-index: 2">
            <div>已完成检查任务</div>
            <n-statistic tabular-nums style="--n-value-text-color: white">
              <n-number-animation show-separator :from="0" :to="newObj && newObj.completionTaskNum" />
            </n-statistic>
          </div>
        </div>

        <div class="trouble-number flex justify-start items-center ml-[16px">
          <div style="z-index: 2">
            <div>检查出隐患</div>
            <n-statistic tabular-nums style="--n-value-text-color: white">
              <n-number-animation show-separator :from="0" :to="newObj && newObj.total" />
            </n-statistic>
          </div>
        </div>

        <div class="trouble-number flex justify-start items-center ml-[16px]">
          <div style="z-index: 2">
            <div>已整改</div>
            <n-statistic tabular-nums style="--n-value-text-color: white">
              <n-number-animation show-separator :from="0" :to="newObj && newObj.disposedNum" />
            </n-statistic>
          </div>
        </div>
        <div class="trouble-number flex justify-start items-center ml-[16px]">
          <div style="z-index: 2">
            <div>整改中</div>
            <n-statistic tabular-nums style="--n-value-text-color: white">
              <n-number-animation show-separator :from="0" :to="newObj && newObj.disposingNum" />
            </n-statistic>
          </div>
        </div>
        <div class="trouble-number flex justify-start items-center ml-[16px]">
          <div style="z-index: 2">
            <div>待整改</div>
            <n-statistic tabular-nums style="--n-value-text-color: white">
              <n-number-animation show-separator :from="0" :to="newObj && newObj.unDisposedNum" />
            </n-statistic>
          </div>
        </div>
      </div>
      <div class="table" style="margin-top: 20px">
        <n-data-table
          :data="tableData"
          class="h-full"
          style="--n-merged-th-color: #bbccf3"
          remote
          striped
          :columns="cols"
          :bordered="false"
          :loading="loading"
          :render-cell="useEmptyCell"
          :theme-overrides="themeOverrides"
          :pagination="pagination"
          :max-height="480"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { cols } from './columns';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { getNum, getNumList } from '../../../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
const { pagination, updateTotal } = useNaivePagination(getTableData);
const [loading, search] = useAutoLoading(false);
const props = defineProps({
  obj: {
    type: Object,
  },
});
const themeOverrides = {
  tdColorStriped: '#dfeefc',
  // tdColorHover: 'rgba(18, 83, 123, 0.35)',
  tdColorStripedModal: '#dfeefc',
  thColor: '#BBCCF3',

  thTextColor: '#222',
  // tdColorHoverModal: 'rgba(18, 83, 123, 0.35)',
  // tdColorHoverPopover: 'rgba(18, 83, 123, 0.35)',
};
const tableData = ref();
function getTableData() {
  const params = {
    userId: props.obj.userId,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    unitId: props.obj.unitId,
  };
  search(getNumList(params)).then((res: any) => {
    if (res.code != 200) return;
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
  // tableData.value = [
  //   {
  //     id: 1,
  //     name: '体验检查计划',
  //     type: '自行检查',
  //     object: '齐大山选矿厂',
  //     time: '2024-10-10~2024-10-10',
  //     status: '0',
  //     shixiao: '正常',
  //     num: '3',
  //   },
  // ];
}
const newObj = ref();
const getDetail = async () => {
  let res = await getNum({
    userId: props.obj.userId,
    unitId: props.obj.unitId,
  });
  newObj.value = res.data;
};
onMounted(() => {
  getDetail();
  getTableData();
});

defineOptions({ name: 'dailyCheckComp' });
</script>

<style scoped lang="scss">
.trouble-number:nth-child(1) {
  color: white;

  margin-right: 16px;
  width: 20%;
  height: 120px;
  padding: 20px 12px 17px 12px;
  // background: linear-gradient(180deg, #ffffff 0%, #e1e7fa 99%);
  // box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
  // border-radius: 8px;
  background-image: url('./img/png1.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.trouble-number:nth-child(2) {
  color: white;

  margin-right: 16px;
  width: 20%;
  height: 120px;
  padding: 20px 12px 17px 12px;
  // background: linear-gradient(180deg, #ffffff 0%, #e1e7fa 99%);
  // box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
  // border-radius: 8px;
  background-image: url('./img/png2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.trouble-number:nth-child(3) {
  color: white;

  margin-right: 16px;
  width: 20%;
  height: 120px;
  padding: 20px 12px 17px 12px;
  // background: linear-gradient(180deg, #ffffff 0%, #e1e7fa 99%);
  // box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
  // border-radius: 8px;
  background-image: url('./img/png3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.trouble-number:nth-child(4) {
  color: white;

  margin-right: 16px;
  width: 20%;
  height: 120px;
  padding: 20px 12px 17px 12px;
  // background: linear-gradient(180deg, #ffffff 0%, #e1e7fa 99%);
  // box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
  // border-radius: 8px;
  background-image: url('./img/png4.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.trouble-number:nth-child(5) {
  color: white;

  margin-right: 16px;
  width: 20%;
  height: 120px;
  padding: 20px 12px 17px 12px;
  // background: linear-gradient(180deg, #ffffff 0%, #e1e7fa 99%);
  // box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
  // border-radius: 8px;
  background-image: url('./img/png5.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>
