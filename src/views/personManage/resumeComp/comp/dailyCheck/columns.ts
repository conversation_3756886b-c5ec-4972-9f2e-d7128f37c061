/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-15 17:54:11
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-20 09:15:19
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/views/person-manage/comp/table/columns.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { DataTableColumn, NTag } from 'naive-ui';
import { h } from 'vue';
export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    width: 65,
    align: 'center',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '计划名称',
    key: 'planName',
    align: 'center',
    width: 140,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '检查类型',
    key: 'planTypeName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '检查对象',
    key: 'unitNames',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '任务起止时间',
    key: 'startEndTime',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '任务状态',
    key: 'taskStateName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      const status = row.taskStateName;
      console.log(status);
      const color = status === '已完成' ? '#00B578FF' : 'rgba(250, 81, 81, 1)';

      return h(
        NTag,
        {
          style: {
            color: 'white',
            background: color,
          },
        },
        () => row.taskStateName
      );
    },
  },
  {
    title: '任务时效',
    key: 'timeStateName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '检查隐患数',
    key: 'total',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      return row.total + '个';
    },
  },
];
