<template>
  <div class="certification">
    <div class="header" v-if="obj.resumeEvaluateFlag !== '1'">
      <ComHeaderB
        :blueIcon="true"
        title="基本信息"
        class="mb-[20px] cursor-pointer"
        style="margin-left: 20px; margin-top: 10px"
      />
      <div class="content">
        <div class="img">
          <img
            v-if="newObj && newObj.photoUrl === ''"
            class="image"
            src="./tou.png"
          />
          <n-image v-else :src="newObj && newObj.photoUrl" class="image" />
        </div>
        <div class="list1">
          <li>姓名：{{ (newObj && newObj.userName) || '--' }}</li>
          <li>单位：{{ (newObj && newObj.unitName) || '--' }}</li>
          <li>部门：{{ (newObj && newObj.deptName) || '--' }}</li>
          <!-- <li>
            部门2：{{ (newObj && newObj.userDeptPostVos.length === 2 && newObj.userDeptPostVos[1].deptName) || '--' }}
          </li> -->
        </div>
        <div class="list2">
          <li>手机号：{{ (newObj && newObj.userTelphone) || '--' }}</li>
          <li>角色：{{ newObj && newObj.roleNames }}</li>
          <li>岗位：{{ (newObj && newObj.postName) || '--' }}</li>
          <!-- <li>
            岗位2：{{
              (newObj &&
                newObj.userDeptPostVos.length === 2 &&
                newObj.userDeptPostVos[1].postName) ||
              '--'
            }}
          </li> -->
        </div>
      </div>
    </div>
    <div class="bottom">
      <ComHeaderB
        :blueIcon="true"
        title="持证情况"
        class="mb-[20px] cursor-pointer"
        style="margin-left: 20px; margin-top: 10px"
      />
      <div
        v-if="list && list.length === 0"
        style="
          padding-left: 50%;
          padding-top: 5%;
          padding-bottom: 10%;

          color: gray;
        "
      >
        <img src="./noData.png" />
        <p style="margin-left: 20px">暂无数据</p>
      </div>
      <div class="content" v-for="item in list" :key="item.id">
        <div class="img">
          <img v-if="item && item.img === ''" class="image" src="./zheng.png" />
          <n-image v-else :src="item.img" class="image" />
        </div>
        <div class="list1">
          <li>证书名称：{{ item.name || '--' }}</li>
          <li>证书类型：{{ item.typeName || '--' }}</li>
          <li>
            证书有效期：{{ item.issueTime }} ~
            {{ item.effectiveTimeEnd }}
          </li>
          <li>
            预警天数：{{
              item.certificateStatus == 1
                ? '--'
                : item.certificateStatus == 2
                  ? '已过期' + item.warningDays + '天'
                  : item.certificateStatus > 2
                    ? '还剩' + item.warningDays + '天'
                    : '--'
            }}
          </li>
        </div>
        <div class="list2">
          <li>证书编号：{{ item.code || '--' }}</li>
          <li>发证日期：{{ item.issueTime || '--' }}</li>
          <li>证书状态：{{ item.certificateStatusName || '--' }}</li>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getHistory, getHistoryList } from '../../../fetchData.ts';
import ComHeaderB from '@/components/header/ComHeaderB.vue';
const props = defineProps({
  obj: {
    type: Object,
  },
});
const addr = window.sessionStorage.getItem('ehs-org-alloc-mgr-address');
const newObj = ref();
const list = ref();
const getDetail = async () => {
  const res = await getHistory({ id: props.obj.id });
  newObj.value = res.data;
  // console.log(res.data, '>>>>>>>>>>>');
  let res2 = await getHistoryList({
    userId: props.obj.userId,
    userName: props.obj.userName,
    deptId: props.obj.deptId,
    // levelPath: props.obj.levelPath,
    phoneNumber: props.obj.phoneNumber,
  });
  list.value = res2.data.map((item) => {
    return {
      ...item,
      img: item.filePath
        ? `${window.$SYS_CFG.apiPreviewURL}${item.filePath}`
        : '', // 或者使用默认图片的 URL
    };
  });
  console.log(list.value, '>>>>>');
};
onMounted(() => {
  getDetail();
});
defineOptions({ name: 'certificationComp' });
</script>

<style lang="scss" scoped>
.certification {
  width: 100%;
  height: 100%;
  .header {
    width: 100%;
    height: 300px;
    border-radius: 2px;
    // border: 1px solid rgb(221, 217, 217);
    .content {
      list-style: none;
      display: flex;
      .img {
        padding-left: 20px;
        .image {
          width: 170px;
          height: 220px;
        }
      }
      .list1 {
        li {
          height: 60px;
          line-height: 60px;
          margin-left: 30px;
        }
      }
      .list2 {
        li {
          height: 60px;
          line-height: 60px;
          margin-left: 100px;
        }
      }
    }
  }
  .bottom {
    width: 100%;
    margin-top: 20px;
    height: auto;
    border-radius: 2px;
    // border: 1px solid rgb(221, 217, 217);
    .content {
      width: 98%;
      height: 250px;
      background-color: #eef4f9;
      margin: 0 auto;
      border-radius: 2px;
      //   height: 20px;
      list-style: none;
      //   padding-bottom: 0px;
      margin-bottom: 20px;

      display: flex;
      .img {
        padding-left: 50px;
        padding-top: 35px;
        .image {
          width: 140px;
          height: 180px;
        }
      }
      .list1 {
        margin-top: 20px;
        li {
          height: 50px;
          line-height: 50px;
          margin-left: 30px;
        }
      }
      .list2 {
        margin-top: 20px;
        li {
          height: 50px;
          line-height: 50px;
          margin-left: 100px;
        }
      }
    }
  }
}
</style>
