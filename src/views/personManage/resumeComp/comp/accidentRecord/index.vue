<template>
  <div class="box">
    <!-- <img
      class="shi_li"
      src="@/views/personManage/resumeComp/comp/responsibility/shili.png"
      alt=""
    /> -->

    <div class="mb-[24px] mt-[8px] wrapper">
      <swiper
        ref="swiperRef"
        :modules="[Navigation, A11y]"
        :slides-per-view="5"
        :space-between="50"
        :pagination="{ clickable: true }"
        navigation
      >
        <swiper-slide class="other-card" v-for="item in topData" :key="item.id">
          <div style="z-index: 2; position: relative">
            <img :src="item.url" />
            <div style="position: absolute; top: 10%; left: 5%">
              <div>{{ item.name }}</div>
              <n-statistic tabular-nums style="--n-value-text-color: #fff; --n-value-font-size: 28px">
                <n-number-animation show-separator :from="0" :to="item.count" />
              </n-statistic>
            </div>
          </div>
        </swiper-slide>
        <!-- <div
          class="swiper-button-prev swiper-buttono-black"
          slot="button--prev"
          @click="prev"
        >
          <img src="@/assets/open.png" />
        </div>
        <div
          class="swiper-button-next swiper-buttono-black"
          slot="button--next"
          @click="next"
        >
          <img src="@/assets/open.png" />
        </div> -->
      </swiper>
    </div>

    <n-data-table
      remote
      striped
      class="h-full com-table"
      style="--n-merged-th-color: #bbccf3"
      :bordered="false"
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :pagination="pagination"
      :theme-overrides="themeOverrides"
      :render-cell="useEmptyCell"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, h, render, onMounted } from 'vue';
import { NButton, NIcon } from 'naive-ui';
import { Navigation, Pagination, Scrollbar, A11y } from 'swiper/modules';
// Import Swiper Vue.js components
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/scrollbar';
import {
  getRegionalManagementData,
  getForeignKeyRegionalData,
  getAccidentStatistics,
  getAccidentRecordData,
} from '../../../fetchData';
import { CdFilePdf } from '@kalimahapps/vue-icons';
import fileSvg from './file.svg';
import bg1 from '@/views/personManage/resumeComp/comp/accidentRecord/bg1.png';
import bg2 from '@/views/personManage/resumeComp/comp/accidentRecord/bg2.png';
import bg3 from '@/views/personManage/resumeComp/comp/accidentRecord/bg3.png';
import bg4 from '@/views/personManage/resumeComp/comp/accidentRecord/bg4.png';
import bg5 from '@/views/personManage/resumeComp/comp/accidentRecord/bg5.png';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { Base64 } from 'js-base64';
const props = defineProps({
  obj: {
    type: Object,
  },
});
const [loading, search] = useAutoLoading(false);
const swiperRef = ref(null);
const { pagination, updateTotal } = useNaivePagination(getTableData);
const imgData = ref([bg1, bg2, bg3, bg4, bg5]);
const topData = ref([
  {
    id: 1,
    name: '事故总数',
    count: 0,
    url: imgData.value[0],
  },

  // {
  //   id: 2,
  //   url: '',
  //   name: '特别重大事故',
  //   count: 0,
  // },
  // {
  //   id: 3,
  //   url: '',
  //   name: '重大事故',
  //   count: 0,
  // },
  // {
  //   id: 4,
  //   url: '',
  //   name: '较大事故',
  //   count: 2,
  // },
  // {
  //   id: 5,
  //   url: '',
  //   name: '一般事故',
  //   count: 4,
  // },

  // {
  //   id: 6,
  //   url: '',
  //   name: '严重事故',
  //   count: 4,
  // },
]);

const themeOverrides = {
  tdColorStriped: '#dfeefc',
  tdColorHover: '#dfeefc',
  tdColorStripedModal: '#dfeefc',
  thColor: '#BBCCF3',
  thTextColor: '#222',
  //   tdColorHoverModal: '#222',
  // tdColorHoverPopover: 'rgba(18, 83, 123, 0.35)',
};

const columns = [
  {
    title: '序号',
    key: 'index',
    width: 65,
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '事发单位',
    key: 'deptName',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '事故名称',
    key: 'name',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '事故位置',
    key: 'accidentAddr',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '事故发生时间',
    key: 'accidentTime',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '事故等级',
    key: 'accidentLevel',
    // ellipsis: {
    //   tooltip: true,
    // },
  },
  {
    title: '事故报告',
    key: 'reportName',
    ellipsis: {
      tooltip: true,
    },
    width: 300,
    // render: (row: any) => {
    //   return row.documentList.map((document) =>
    //     // 每个 document 生成一个包含按钮和名称的 div
    //     h(
    //       'div',
    //       {
    //         key: document.id,
    //         style: {
    //           display: 'flex',
    //           alignItems: 'center',
    //           marginRight: '8px',
    //         },
    //       },
    //       [
    //         h(
    //           NButton,
    //           {
    //             quaternary: true,
    //             circle: true,
    //             size: 'small',
    //             onClick: () => {
    //               // 您可以在这里处理按钮点击事件，例如查看文档
    //               // console.log(document);
    //               window.open(document.documentAddr);
    //             },
    //             style: 'color:rgb(255,100,100)',
    //           },
    //           {
    //             icon: () =>
    //               h(NIcon, null, {
    //                 default: () =>
    //                   h('img', {
    //                     src: fileSvg,
    //                   }),
    //               }),
    //           }
    //         ),
    //         h(
    //           'div',
    //           {
    //             title: document.documentName,
    //             style: {
    //               transform: 'translateY(-2x)',
    //               display: 'inline-block',
    //               marginLeft: '8px', // 添加一些间距
    //               whiteSpace: 'nowrap', // 不允许换行
    //               overflow: 'hidden', // 隐藏超出部分
    //               textOverflow: 'ellipsis', // 超出部分使用省略号表示
    //               cursor: 'pointer',
    //             },
    //             onClick: () => {
    //               const type = document.documentAddr.split('.');

    //               const typeName = type[type.length - 1];
    //               console.log(typeName, '>>>>>>>>>>>');
    //               // if (typeName === 'pdf') {
    //               //   var b64Encoded = Base64.encode(document.documentAddr);
    //               //   window.open(window.$SYS_CFG.apiPreviewLink + encodeURIComponent(b64Encoded));
    //               // } else {
    //               //   window.open(document.documentAddr);
    //               // }
    //               var b64Encoded = Base64.encode(document.documentAddr);
    //               window.open(
    //                 window.$SYS_CFG.apiPreviewLink + encodeURIComponent(b64Encoded)
    //               );
    //               // console.log(document, '>>>>>>>.');
    //               // 您可以在这里处理按钮点击事件，例如查看文档
    //               // window.open(document.documentAddr);
    //               // const newTab = window.open('', '_blank');
    //               // newTab.location.href = document.documentAddr;
    //             },
    //           },
    //           document.documentName // 显示 documentName
    //         ),
    //       ]
    //     )
    //   );
    // },
    render: (row: any) => {
      return h(
        'div',
        {
          key: row.id,
          style: {
            display: 'flex',
            alignItems: 'center',
            marginRight: '8px',
          },
        },
        [
          h(
            NButton,
            {
              quaternary: true,
              circle: true,
              size: 'small',
              onClick: () => {
                // 您可以在这里处理按钮点击事件，例如查看文档
                // console.log(document);
                const url = row.reportAddr.substring(0, 4);

                if (url === 'http') {
                  var b64Encoded = Base64.encode(row.reportAddr);

                  window.open(window.$SYS_CFG.apiPreviewLink + encodeURIComponent(b64Encoded));
                } else {
                  var b64Encoded = Base64.encode(window.$SYS_CFG.apiPreviewURL + row.reportAddr);
                  console.log(window.$SYS_CFG.apiPreviewURL + row.reportAddr, '<<<<<<<<<<<<');
                  window.open(window.$SYS_CFG.apiPreviewLink + encodeURIComponent(b64Encoded));
                }
              },
              style: 'color:rgb(255,100,100)',
            },
            {
              icon: () =>
                h(NIcon, null, {
                  default: () =>
                    h('img', {
                      src: fileSvg,
                    }),
                }),
            }
          ),
          h(
            'div',
            {
              title: row.reportName,
              style: {
                transform: 'translateY(-2x)',
                display: 'inline-block',
                marginLeft: '8px', // 添加一些间距
                whiteSpace: 'nowrap', // 不允许换行
                overflow: 'hidden', // 隐藏超出部分
                textOverflow: 'ellipsis', // 超出部分使用省略号表示
                cursor: 'pointer',
              },
              onClick: () => {
                const url = row.reportAddr.substring(0, 4);

                if (url === 'http') {
                  var b64Encoded = Base64.encode(row.reportAddr);

                  window.open(window.$SYS_CFG.apiPreviewLink + encodeURIComponent(b64Encoded));
                } else {
                  var b64Encoded = Base64.encode(window.$SYS_CFG.apiPreviewURL + row.reportAddr);
                  console.log(window.$SYS_CFG.apiPreviewURL + row.reportAddr, '<<<<<<<<<<<<');
                  window.open(window.$SYS_CFG.apiPreviewLink + encodeURIComponent(b64Encoded));
                }

                // console.log(document, '>>>>>>>.');
                // 您可以在这里处理按钮点击事件，例如查看文档
                // window.open(document.documentAddr);
                // const newTab = window.open('', '_blank');
                // newTab.location.href = document.documentAddr;
              },
            },
            row.reportName // 显示 documentName
          ),
        ]
      );
    },
  },
  {
    title: '更新时间',
    key: 'updateTime',
    ellipsis: {
      tooltip: true,
    },
  },
];
const buildingIds = ref('');
const floorIds = ref('');
const getData = async () => {
  try {
    const regionalData = await getRegionalManagementData({
      id: props.obj.userId,
      pageNo: 0,
      pageSize: -1,
      unitId: props.obj.unitId,
    });

    const idString = regionalData.data.regionalManagementDtoList
      ? regionalData.data.regionalManagementDtoList.map((item) => item.id).join(',')
      : '';
    const foreignKeyData = await getForeignKeyRegionalData({
      regionalManagementId: idString,
    });

    buildingIds.value = foreignKeyData.data.foreignKeyRegionalVoList
      ? foreignKeyData.data.foreignKeyRegionalVoList.map((item) => item.buildId).join(',')
      : '';
    floorIds.value = foreignKeyData.data.foreignKeyRegionalVoList
      ? foreignKeyData.data.foreignKeyRegionalVoList.map((item) => item.floorId).join(',')
      : '';

    await Promise.all([
      getTableData(),
      getAccidentStatistics({
        buildingId: buildingIds.value,
        floorId: floorIds.value,
        unitId: props.obj.unitId,
      }),
    ]).then(([tableDataResponse, accidentStatsResponse]) => {
      // 处理事故统计数据
      accidentStatsResponse.data.forEach((item) => {
        topData.value[0].count += item.count; // 累加 count
      });

      // 合并并处理 topData
      topData.value = [...topData.value, ...accidentStatsResponse.data];

      // 更新 URL
      topData.value.forEach((item, index) => {
        if (index === 0) {
          item.url = imgData.value[0]; // 第一项
        } else if (index < 5) {
          item.url = imgData.value[index]; // 从 imgData[1] 到 imgData[4]
        } else {
          item.url = imgData.value[1 + ((index - 5) % 4)]; // 从第六项开始循环
        }
      });
    });
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};
// 进行循环的分配;

const tableData = ref<any[]>([]);

function getTableData() {
  const params = {
    buildingId: buildingIds.value,
    floorId: floorIds.value,
    unitId: props.obj.unitId,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
  };
  // 如果没选检查对象，默认登录用户自己的
  search(getAccidentRecordData(params)).then((res) => {
    console.log(res, '>>>>>>>>>>>');
    tableData.value = res.data.accidentReportVoList || [];
    updateTotal(res.data.total || 0);
  });
  // tableData.value = [
  //   {
  //     id: '1',
  //     deptName: 'xxx 单位',
  //     name: '8.23高空坠落事故',
  //     address: 'xxx 作业区',
  //     date: '2024-08-23  10：00',
  //     level: '一般事故',
  //     flieName: '8.23高空坠落事故报告.pdf',
  //     updateTime: '2024-08-23  10：00',
  //   },
  //   {
  //     id: '2',
  //     deptName: 'xxx 单位',
  //     name: ' 9.24踩踏事故',
  //     address: 'xxx 作业区',
  //     date: '2024-09-24  11：00',
  //     level: '一般事故',
  //     flieName: ' 9.24踩踏事故报告.pdf',
  //     updateTime: '2024-09-24  11：00',
  //   },
  //   {
  //     id: '3',
  //     deptName: 'xxx 单位',
  //     name: ' 物体打击事故',
  //     address: 'xxx 作业区',
  //     date: '2023-08-24  10：00',
  //     level: '一般事故',
  //     flieName: ' 物体打击事故报告.pdf',
  //     updateTime: '2023-08-24  10：00',
  //   },
  //   {
  //     id: '4',
  //     deptName: 'xxx 单位',
  //     name: ' 高空坠落事故',
  //     address: 'xxx 作业区',
  //     date: '2023-07-23  15：00',
  //     level: '一般事故',
  //     flieName: ' 高空坠落事故报告.pdf',
  //     updateTime: '2023-07-23  15：00',
  //   },
  // ];
}

onMounted(() => {
  // getTableData();
  getData();
});

defineOptions({ name: 'responsibilityComp' });
</script>

<style lang="scss" scoped>
.wrapper {
  --swiper-navigation-size: 18px;
  // --swiper-navigation-color: red;
  // position: relative;
  :deep(.swiper-button-prev) {
    background-image: url('@/assets/open.png');
    position: fixed !important;
    top: 127px !important;
    left: 2px;
  }
  :deep(.swiper-button-next) {
    position: fixed !important;
    top: 130px !important;
    right: 2px;
  }
}
.box {
  position: relative;
}
// .shi_li {
//   position: absolute;
//   right: 12px;
//   top: 12px;
//   z-index: 9999;
// }

.box-content {
  //   margin-bottom: 16px;
  //   border-radius: 8px;
  //   background: rgb(200, 236, 252);
}
// .bg1 {
//   background-size: 100% 100%;
//   background-image: url('@/views/personManage/resumeComp/comp/accidentRecord/bg1.png');
// }
// .bg2 {
//   background-size: 100% 100%;
//   background-image: url('@/views/personManage/resumeComp/comp/accidentRecord/bg2.png');
// }

// .bg3 {
//   background-size: 100% 100%;
//   background-image: url('@/views/personManage/resumeComp/comp/accidentRecord/bg3.png');
// }
// .bg4 {
//   background-size: 100% 100%;
//   background-image: url('@/views/personManage/resumeComp/comp/accidentRecord/bg4.png');
// }
// .bg5 {
//   background-size: 100% 100%;
//   background-image: url('@/views/personManage/resumeComp/comp/accidentRecord/bg5.png');
// }

.other-card {
  // position: relative;
  // // width: 260px;
  // // height: 80px;
  // padding: 16px 0 14px 20px;
  width: 260px;
  height: 116px;
  color: #fff;

  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 500;
  font-size: 18px;
}
/*先去掉默认样式*/
</style>
