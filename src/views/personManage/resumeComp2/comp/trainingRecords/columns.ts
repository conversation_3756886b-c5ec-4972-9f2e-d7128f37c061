/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-15 17:54:11
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-20 09:15:19
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/views/person-manage/comp/table/columns.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    width: 65,
    align: 'center',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '培训任务名',
    key: 'taskName',
    align: 'center',
    width: 140,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '培训时间',
    key: 'planStartTime',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '培训时长',
    key: 'cosTime',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '培训实施人',
    key: 'trainUserName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '签到时间点',
    key: 'signTime',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '是否考试',
    key: 'isExamName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '考试分数',
    key: 'score',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '考试通过情况',
    key: 'isPassName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
];
