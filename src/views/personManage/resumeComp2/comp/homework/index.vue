<template>
  <div class="dailyCheck">
    <div class="header">
      <div class="flex justify-start items-center">
        <div class="trouble-number flex justify-start items-center">
          <div style="z-index: 2">
            <div>作业总次数（次）</div>
            <n-statistic tabular-nums style="--n-value-text-color: white">
              <n-number-animation show-separator :from="0" :to="newObj && newObj.totalCount" />
            </n-statistic>
          </div>
        </div>

        <div class="trouble-number flex justify-start items-center ml-[16px">
          <div style="z-index: 2">
            <div>合规次数（次）</div>
            <n-statistic tabular-nums style="--n-value-text-color: white">
              <n-number-animation show-separator :from="0" :to="newObj && newObj.complianceCount" />
            </n-statistic>
          </div>
        </div>

        <div class="trouble-number flex justify-start items-center ml-[16px]">
          <div style="z-index: 2">
            <div>隐患次数（次）</div>
            <n-statistic tabular-nums style="--n-value-text-color: white">
              <n-number-animation show-separator :from="0" :to="newObj && newObj.nonComplianceCount" />
            </n-statistic>
          </div>
        </div>
      </div>

      <div class="table" style="margin-top: 20px" v-for="item in tableData" :key="item.id">
        <li>
          <p>作业时间: {{ item.planStartTime || '--' }} ~{{ item.planEndTime || '--' }}</p>
          <p>作业单位: {{ item.responsibleUnitName || '--' }}</p>
          <p>作业位置: {{ item.operationLocationPart || '--' }}</p>
          <P>作业编号: {{ item.operationNo || '--' }}</P>
          <p>作业等级: {{ item.operationLevelName || '--' }}</p>
          <div class="bottom">
            <div>
              <span :style="{ background: item.bgColor }">{{ item.operationStatus || '--' }}</span>
              <span>{{ item.operationTypeName || '--' }}</span>
              <span :style="item.compliance ? { background: '#e39e48' } : { background: '##d12d2b' }">{{
                item.compliance ? '合规' : '有隐患' || '--'
              }}</span>
            </div>
            <div>
              <n-button type="primary" @click="open(item.pdfUrl)">查看作业票</n-button>
            </div>
          </div>
        </li>
      </div>
      <div v-show="tableData && tableData.length === 0" style="height: 200px; position: relative">
        <div style="position: absolute; left: 50%; top: 30%">
          <img src="./img/noData.png" />
          <p style="font-size: 14px; color: #c2c2c2; text-align: center">暂无数据</p>
        </div>
      </div>
      <div class="pagination">
        <div></div>
        <div class="right">
          <div style="margin-right: 10px; margin-top: 2px">共 {{ tableData && tableData.length }} 条</div>
          <n-pagination
            v-model:page="naviePagination.page"
            v-model:page-size="naviePagination.pageSize"
            :item-count="total"
            show-size-picker
            :page-sizes="[10, 20, 50, 100]"
            show-quick-jumper
            :disabled="disabled"
            :on-update:page="handlePage"
            :on-update:page-size="handlePageSize"
          />
        </div>
      </div>
      <!-- <n-space vertical> </n-space> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { getOperationBasePageData, getOperationComplianceStatisticsData } from '../../../fetchData';
// const { pagination, updateTotal } = useNaivePagination(getTableData);
const [loading, search] = useAutoLoading(false);
const props = defineProps({
  obj: {
    type: Object,
  },
});
const tableData = ref([]);
const total = ref();
const naviePagination = ref({
  page: 1,
  pageSize: 10,
});
function getTableData() {
  const params = {
    userId: props.obj.userId,
    pageNo: naviePagination.value.page,
    pageSize: naviePagination.value.pageSize,
    unitId: props.obj.unitId,
  };
  search(getOperationBasePageData(params)).then((res: any) => {
    if (res.code != 200) return;
    tableData.value =
      res.data.operationApiVoList.map((item) => {
        if (item.operationStatus === '2') {
          item.operationStatus = '申请中';
          item.bgColor = '#e4b275'; // 设置背景颜色为绿色
        } else if (item.operationStatus === '3') {
          item.operationStatus = '作业中';
          item.bgColor = '#54b080'; // 其他状态设置为红色
        } else {
          item.operationStatus = '已完成';
          item.bgColor = '#919697'; // 其他状态设置为红色
        }
        return item;
      }) || [];

    total.value = res.data.total;
    // updateTotal(res.data.total || 0);
  });
}
const newObj = ref({});
const getData = () => {
  const params = {
    userId: props.obj.userId,
    unitId: props.obj.unitId,
  };

  getOperationComplianceStatisticsData(params).then((res) => {
    newObj.value = res.data;
    // console.log(res, '>>>>>>>>>>>>>');
  });
};
const open = (url) => {
  window.open(window.$SYS_CFG.apiBase + url);
};
const handlePage = (value: any) => {
  naviePagination.value.page = value;
  getTableData();
};
const handlePageSize = (value: any) => {
  naviePagination.value.pageSize = value;
  getTableData();
};
// const newObj = ref();
// const getDetail = async () => {
//   let res = await getNum({
//     userId: props.obj.userId,
//     unitId: props.obj.unitId,
//   });
//   newObj.value = res.data;
// };
onMounted(() => {
  // getDetail();
  getTableData();
  getData();
});

defineOptions({ name: 'dailyCheckComp' });
</script>

<style scoped lang="scss">
.trouble-number:nth-child(1) {
  color: white;

  margin-right: 16px;
  width: 33.3%;
  height: 120px;
  padding: 20px 12px 17px 12px;
  // background: linear-gradient(180deg, #ffffff 0%, #e1e7fa 99%);
  // box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
  // border-radius: 8px;
  background-image: url('./img/img1.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.trouble-number:nth-child(2) {
  color: white;

  margin-right: 16px;
  width: 33.3%;
  height: 120px;
  padding: 20px 12px 17px 12px;
  // background: linear-gradient(180deg, #ffffff 0%, #e1e7fa 99%);
  // box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
  // border-radius: 8px;
  background-image: url('./img/img2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.trouble-number:nth-child(3) {
  color: white;

  margin-right: 16px;
  width: 33.3%;
  height: 120px;
  padding: 20px 12px 17px 12px;
  // background: linear-gradient(180deg, #ffffff 0%, #e1e7fa 99%);
  // box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
  // border-radius: 8px;
  background-image: url('./img/img3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.table {
  width: 100%;
  list-style: none;
  font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
  font-weight: 400;
  font-size: 14px;
  color: #212121;
  li {
    width: 100%;
    height: 243px;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #d7dae0;
    p {
      margin-top: 15px;
      margin-left: 20px;
    }
    .bottom {
      width: 96%;
      margin-top: 5px;
      margin-left: 20px;
      display: flex;
      span {
        display: inline-block;
        padding: 3px 10px 0px 10px;
        margin-right: 20px;
        height: 30px;
        color: white;
        background: #527cff;
        border-radius: 4px 4px 4px 4px;
      }
      justify-content: space-between;
    }
  }
}
.pagination {
  width: 100%;
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  .right {
    display: flex;
    // justify-content: space-between;
  }
}
</style>
