<template>
  <div class="certification">
    <div class="header">
      <ComHeaderB
        :blueIcon="true"
        title="基本信息"
        class="mb-[20px] cursor-pointer"
        style="margin-left: 20px; margin-top: 10px"
      />
      <div class="content">
        <div class="img">
          <img v-if="newObj && newObj.photoUrl === ''" class="image" src="./tou.png" />
          <n-image v-else :src="newObj && newObj.photoUrl" class="image" />
        </div>
        <div class="list1">
          <li>姓名：{{ (newObj && newObj.userName) || '--' }}</li>

          <li>部门：{{ (newObj && newObj.deptName) || '--' }}</li>
          <!-- <li>
            部门2：{{ (newObj && newObj.userDeptPostVos.length === 2 && newObj.userDeptPostVos[1].deptName) || '--' }}
          </li> -->
        </div>
        <div class="list2">
          <li>手机号：{{ (newObj && newObj.userTelphone) || '--' }}</li>
          <li>角色：{{ newObj && newObj.roleNames }}</li>

          <!-- <li>
            岗位2：{{
              (newObj &&
                newObj.userDeptPostVos.length === 2 &&
                newObj.userDeptPostVos[1].postName) ||
              '--'
            }}
          </li> -->
        </div>
        <div class="list2">
          <li>单位：{{ (newObj && newObj.unitName) || '--' }}</li>
          <li>岗位：{{ (newObj && newObj.postName) || '--' }}</li>
        </div>
      </div>
    </div>
    <div class="bottom">
      <div class="bottom_h">
        <ComHeaderB
          :blueIcon="true"
          title="安全履职能力分析"
          class="mb-[20px] cursor-pointer"
          style="margin-left: 20px; margin-top: 10px"
        />
        <!-- <n-button
          type="primary"
          style="position: absolute; right: 110px; top: 190px"
          @click="fen"
          >分析</n-button
        > -->
        <n-button type="primary" style="position: absolute; right: 20px; top: 190px" @click="showDialog = true">
          更新履历
        </n-button>
      </div>
      <div class="bottom_c">
        <div>
          <RadarChart style="width: 400px; height: 300px" :echartsData="echartsData" :indicator="indicator" />
          <div class="summary">
            <div class="summary_h">能力总结分析</div>
            <div class="border"></div>
            <n-scrollbar style="height: 500px">
              <div class="summary_c">
                <LLMDrawer ref="LLMDrawerRef" :quession="quessionContent" />
              </div>
            </n-scrollbar>
          </div>
        </div>
        <div class="list">
          <n-scrollbar style="height: 1030px">
            <div class="list_li" v-for="item in dataList" :key="item.dimensionId">
              <div class="list_li_h"><span></span>&nbsp;{{ item.dimensionName }}</div>
              <div class="border"></div>
              <div class="list_children" v-for="i in item.childList" :key="i.standardId">
                <div>
                  <div>
                    <span>{{ i.standardName }}</span>
                  </div>
                  <div style="margin-top: 10px" v-if="i.childList.length > 0">
                    <span
                      v-for="ii in i.childList"
                      :key="ii.indexId"
                      :style="{
                        background: ii.showFlag === '3' ? '' : ii.showFlag === '1' ? '#dbeafe' : '#fedbdb',
                        color: ii.showFlag === '3' ? '' : ii.showFlag === '1' ? '#527CFF' : '#F12B2B',
                      }"
                      class="tag"
                    >
                      {{ ii.resultLabel }}
                    </span>
                  </div>
                </div>

                <div>
                  <img
                    v-if="i.childList.length > 0 && i.flag === '1'"
                    src="./done.png"
                    style="width: 16px; height: 16px; margin-top: 20px"
                  />
                  <img
                    v-else-if="i.childList.length > 0 && i.flag === '2'"
                    src="./unDone.png"
                    style="width: 16px; height: 16px; margin-top: 20px"
                  />
                  <div v-else></div>
                </div>
              </div>
            </div>
          </n-scrollbar>
        </div>
      </div>
    </div>

    <!--更新履历-->
    <ComDialog v-model:show="showDialog" :mask-closable="false" title="更新履历" :width="1400" :height="800">
      <ResumeUpdate :userData="userData" @close="showDialog = false" />
    </ComDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import {
  queryResultByDimension,
  getHistory,
  queryEvaluateResultDetail,
  analyseSumUp,
  modelCalculation,
} from '../../../fetchData.ts';
import ComEmpty from '@/components/empty/index.vue';
import LLMDrawer from '@/components/llm/index.vue';
import ComHeaderB from '@/components/header/ComHeaderB.vue';
import RadarChart from '@/components/echarts/radarChart.vue';
import ResumeUpdate from '@/views/personManage/resumeComp/comp/ResumeUpdate/index.vue';
import ComDialog from '@/components/dialog/ComDialog.vue';
const LLMDrawerRef = ref(null);
const props = defineProps({
  obj: {
    type: Object,
  },
});
const radarData = ref<any[]>([]);
const userData = computed(() => props.obj);
const showDialog = ref(false);
const echartsData = computed(() => {
  return [
    {
      name: '评估维度',
      value: radarData.value.map((item) => item.realScore),
    },
  ];
});
const quessionContent = ref('');
const indicator = computed(() =>
  radarData.value.map((item) => ({
    name: item.dimensionName,
    max: item.totalScore,
  }))
);
const fen = async () => {
  let res = await modelCalculation({ userId: props.obj.userId });
  console.log(res, '>>>>');
  if (res.code === '200') {
    getContent();
    getData();
  }
};
const getContent = async () => {
  let res = await analyseSumUp({
    userId: props.obj.userId,
    unitId: props.obj.unitId,
  });

  if (res.data.aiSummary === '') {
    LLMDrawerRef.value.getLLMAnswer(res.data.evaluateSummary, true);
  } else {
    LLMDrawerRef.value.getLLMAnswer(res.data.aiSummary, false);
  }
};
const getData = async () => {
  let res2 = await queryEvaluateResultDetail({
    userId: props.obj.userId,
    unitId: props.obj.unitId,
  });
  dataList.value = splitThirdLevelChildren2(splitThirdLevelChildren(res2.data));
  dataList.value.forEach((dimension) => {
    dimension.childList.forEach((child) => {
      // 删除showFlag为'3'的child
      child.childList = child.childList.filter((thirdChild) => thirdChild.showFlag !== '3');
    });

    // 在这里重新获取过滤后的第三层childList的realScore
    dimension.childList.forEach((child) => {
      const scores = child.childList.map((thirdChild) => thirdChild.showFlag);

      // 检查是否全部大于0
      const allScoresPositive = scores.every((score) => score === '1');
      // 检查是否全部等于0
      const allScoresZero = scores.every((score) => score === '3');

      // 根据检查结果添加flag字段
      if (allScoresPositive) {
        child.flag = '1';
      } else if (allScoresZero) {
        child.flag = '3';
      } else {
        child.flag = '2';
      }
    });
  });
  console.log(dataList.value, 'dataList');
  radarData.value = [];
  // console.log(dataList.value, '>>>>>');
  let res = await queryResultByDimension({
    userId: props.obj.userId,
    unitId: props.obj.unitId,
  });

  // console.log(res, '>>>>>');
  radarData.value = res.data || [];
};

function splitThirdLevelChildren(array) {
  return array.map((item) => {
    // 递归处理第一层和第二层的 children
    if (item.childList) {
      item.childList = splitThirdLevelChildren(item.childList);

      // 处理第三层 children
      item.childList.forEach((child) => {
        if (child.childList) {
          const newChildren = [];
          child.childList.forEach((grandChild) => {
            if (grandChild.resultLabel.includes(',')) {
              const names = grandChild.resultLabel.split(',');

              names.forEach((name, index) => {
                newChildren.push({
                  id: grandChild.indexId, // 生成新的 id
                  resultLabel: name.trim(), // 去除多余的空格
                  realScore: grandChild.realScore,
                  standardScore: grandChild.standardScore,
                });
              });
            } else {
              newChildren.push(grandChild);
            }
          });
          child.childList = newChildren; // 替换为新的 children
        }
      });
    }
    return item;
  });
}
function splitThirdLevelChildren2(array) {
  return array.map((item) => {
    // 递归处理第一层和第二层的 children
    if (item.childList) {
      item.childList = splitThirdLevelChildren2(item.childList);

      // 处理第三层 children
      item.childList.forEach((child) => {
        if (child.childList) {
          const newChildren = [];
          child.childList.forEach((grandChild) => {
            const resultLabel = grandChild.resultLabel.trim(); // 去除多余的空格

            // 判断条件
            if (
              (grandChild.resultLabel !== '' && grandChild.standardScore > 0 && grandChild.realScore > 0) ||
              (grandChild.resultLabel !== '' && grandChild.standardScore < 0 && grandChild.realScore === 0)
            ) {
              newChildren.push({
                id: grandChild.indexId, // 使用正确生成的 id
                resultLabel: resultLabel,
                realScore: grandChild.realScore,
                standardScore: grandChild.standardScore,
                showFlag: '1',
              });
            } else if (
              (grandChild.resultLabel !== '' && grandChild.standardScore > 0 && grandChild.realScore === 0) ||
              (grandChild.resultLabel !== '' && grandChild.standardScore < 0 && grandChild.realScore < 0)
            ) {
              newChildren.push({
                id: grandChild.indexId,
                resultLabel: resultLabel,
                realScore: grandChild.realScore,
                standardScore: grandChild.standardScore,
                showFlag: '2', // 'showFlag' 应该对应需求
              });
            } else if (grandChild.resultLabel === '') {
              newChildren.push({
                id: grandChild.indexId,
                resultLabel: '', // 去除多余的空格
                realScore: grandChild.realScore,
                standardScore: grandChild.standardScore,
                showFlag: '3',
              });
            }
          });

          child.childList = newChildren; // 替换为新的 children
        }
      });
    }
    return item;
  });
}
const dataList = ref([]);

// console.log(dataList.value, 'dataList.value');

const newObj = ref();
const list = ref();
const getDetail = async () => {
  const res = await getHistory({ id: props.obj.id });
  newObj.value = res.data;
  // console.log(res.data, '>>>>>>>>>>>');
};
onMounted(() => {
  getContent();
  getDetail();
  getData();
});
defineOptions({ name: 'certificationComp' });
</script>

<style lang="scss" scoped>
.certification {
  position: relative;
  width: 100%;
  height: 100%;
  .header {
    padding-top: 2px;
    width: 100%;
    height: 300px;
    border-radius: 2px;
    background-image: url('./bj2.png');
    background-repeat: no-repeat;
    background-size: 100% 300px;
    // border: 1px solid rgb(221, 217, 217);
    .content {
      list-style: none;
      display: flex;
      .img {
        padding-left: 20px;
        .image {
          width: 170px;
          height: 220px;
        }
      }
      .list1 {
        li {
          height: 60px;
          line-height: 60px;
          margin-left: 30px;
        }
      }
      .list2 {
        li {
          height: 60px;
          line-height: 60px;
          margin-left: 100px;
        }
      }
    }
  }
  .bottom {
    width: 100%;
    margin-top: 20px;
    height: auto;
    border-radius: 2px;
    // border: 1px solid rgb(221, 217, 217);

    .bottom_h {
      width: 100%;
      justify-content: space-between;
      display: flex;
    }
    .bottom_c {
      width: 100%;
      display: flex;
      .list {
        position: absolute;

        margin-left: 20px;
        width: 61.5%;
        left: 37%;
        top: 240px;
        // position: absolute;
        .list_li {
          width: 100%;
          // margin-bottom: 15px;
          .list_li_h {
            // margin-bottom: 15px;
            font-size: 18px;
            span {
              display: inline-block;
              width: 10px;

              height: 10px;

              border-radius: 50%;
              background: #527cff;
            }
            // font-weight: 600;
          }
          .border {
            width: 30px;
            margin-bottom: 15px;
            height: 5px;
            background: #527cff;
          }
          .list_children {
            display: flex;
            justify-content: space-between;
            width: 98%;
            // height: 80px;
            padding-left: 20px;
            padding-top: 10px;
            padding-bottom: 10px;
            margin-top: 15px;
            padding-right: 10px;
            border-radius: 6px;
            // font-weight: 600;
            background: #f9fafb;

            // line-height: 80px;
            .tag {
              // height: 20px;
              display: inline-block;
              // width:70px;+
              color: #3a5abb;
              padding-left: 10px;
              padding-top: 2px;
              font-weight: 500;
              padding-bottom: 4px;
              border-radius: 4px;
              padding-right: 10px;
              padding-top: 4px;
              margin-top: 2px;
              margin-left: 5px;
              // margin-top: 10px;
            }
          }
        }
      }
      .summary {
        background-image: url('./bj.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 500px;
        // height: ;
        // max-height: 500px;
        overflow: hidden;
        color: #3a5abb;
        padding-top: 10px;

        padding-bottom: 40px;
        padding-left: 20px;
        padding-right: 20px;
        // background-color: #e3ecfb;
        margin-left: 10px;

        // border-radius: 5px;
        margin-bottom: 20px;
        .summary_h {
          font-weight: 600;
          font-size: 18px;
          color: #3d3f40;
        }
        .border {
          width: 30px;
          margin-bottom: 10px;
          height: 5px;
          background: #527cff;
        }
      }
    }
  }
}
</style>
