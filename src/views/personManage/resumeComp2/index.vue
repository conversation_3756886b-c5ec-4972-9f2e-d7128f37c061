<template>
  <ComDrawerA
    :show="props.show"
    title="安全履历"
    ref="drawerRef"
    :autoFocus="false"
    :footerPaddingBottom="25"
    :maskClosable="false"
    :show-action="false"
    class="!w-[1432px]"
    @handleNegative="handleClose"
  >
    <!-- <<<<<<< HEAD
    <n-tabs type="line" animated class="px-[24px] py-[8px]">
      <n-tab-pane name="1" tab="持证情况"> <certification :obj="formData" /></n-tab-pane>
      <n-tab-pane name="2" tab="管控责任">
        <responsibilityView />
      </n-tab-pane>
      <n-tab-pane name="3" tab="培训记录"><trainingRecords /> </n-tab-pane>
      <n-tab-pane name="4" tab="事故记录"> 事故记录 </n-tab-pane>
      <n-tab-pane name="5" tab="隐患整改"> 隐患整改 </n-tab-pane>
      <n-tab-pane name="6" tab="日常检查">`
        <dailyCheck />
      </n-tab-pane>
      <n-tab-pane name="7" tab="体检记录"> 体检记录 </n-tab-pane>
    </n-tabs>
======= -->
    <template #header>
      <n-tabs type="line" animated class="px-[24px] pt-[22px]" v-model:value="current" :on-update:value="change">
        <n-tab-pane :name="item.name" :tab="item.label" v-for="(item, index) of tabList" :key="index" />
      </n-tabs>
    </template>

    <div class="p-[16px]">
      <!-- 持证情况 -->
      <performDuties :obj="formData" v-if="current == 0" />
      <certification :obj="formData" v-if="current == 1" />
      <trainingRecords :obj="formData" v-if="current == 2" />
      <homework :obj="formData" v-if="current == 3" />
    </div>
  </ComDrawerA>
</template>
<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import ComDrawerA from '@/components/drawer/ComDrawerA.vue';
import trainingRecords from './comp/trainingRecords/index.vue';
import certification from './comp/certification/index.vue';
import performDuties from './comp/performDuties/index.vue';
import homework from './comp/homework/index.vue';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import { useStore } from '@/store/index';
import { curry } from 'lodash-es';
const { userInfo } = useStore();

const emits = defineEmits(['close']);

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({}),
  },
  show: {
    type: Boolean,
    default: false,
  },
});

const change = (nv: number) => {
  current.value = nv;
};
watch(
  () => props.formData,
  (nv) => {
    if (nv) {
      console.log(nv, current.value);
      current.value = 1;
    }
  },
  { deep: true }
);
const tabList = ref([
  {
    label: '持证情况',
    name: 1,
  },

  {
    label: '培训记录',
    name: 2,
  },
  {
    label: '作业记录',
    name: 3,
  },
]);
watch(
  () => props.formData,
  (nv) => {
    if (nv.resumeEvaluateFlag === '1') {
      current.value = 0;
      tabList.value = [
        {
          label: '履职能力分析',
          name: 0,
        },
        {
          label: '持证情况',
          name: 1,
        },

        {
          label: '培训记录',
          name: 2,
        },
        {
          label: '作业记录',
          name: 3,
        },
      ];
    } else {
      current.value = 1;
      tabList.value = [
        {
          label: '持证情况',
          name: 1,
        },

        {
          label: '培训记录',
          name: 2,
        },
        {
          label: '作业记录',
          name: 3,
        },
      ];
    }
  },
  { deep: true }
);
const current = ref();

const handleClose = () => {
  emits('close');
};

defineOptions({ name: 'resumeComp2' });
</script>
