/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-15 14:50:49
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-17 11:46:11
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/views/home/<USER>/checklist-conf/check-template/constant.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { ref } from 'vue';
export const enum PROVIDE_KEY {
  currentAction = 'currentAction',
}

export const enum ACTION {
  SEARCH = 'SEARCH',
  ADD = 'ADD',
  EXPORT = 'EXPORT',
  DELETE = 'DELETE',
  // 安全履历
  RESUME = 'RESUME',
  RESUME2 = 'RESUME2',
}

export const ACTION_LABEL: { [key in ACTION]: string } = {
  [ACTION.SEARCH]: '搜索',
  [ACTION.ADD]: '新增',
  [ACTION.EXPORT]: '导出',
  [ACTION.DELETE]: '删除',
  [ACTION.RESUME]: '安全履历',
  [ACTION.RESUME2]: '安全履历',
};

export const curTab = ref<number>(1);
