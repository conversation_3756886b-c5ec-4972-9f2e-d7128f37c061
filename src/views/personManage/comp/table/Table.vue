<template>
  <!-- class="h-full" -->
  <div class="box">
    <n-data-table
      class="h-full com-table"
      remote
      striped
      :columns="columns"
      :treeCode="treeCode"
      :tab="tab"
      :data="tableData"
      :bordered="false"
      :loading="loading"
      :flex-height="true"
      :pagination="pagination"
      :render-cell="useEmptyCell"
    />
  </div>
</template>
<script setup lang="ts">
import { h, ref, VNode, toRaw, watch, onMounted } from 'vue';
import type { IPageData } from '../../type';
import { DataTableColumns, NButton, useMessage } from 'naive-ui';
import { cols } from './columns';
import { ACTION, ACTION_LABEL } from '../../constant';
import { useRouter, useRoute } from 'vue-router';
import { getPersonManageList, deletePersonManage } from '../../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { useStore } from '@/store';
const message = useMessage();
const store = useStore();
const route = useRoute();
const router = useRouter();
const emits = defineEmits(['action']);

const props = defineProps({
  useArray: {
    type: Array<string | number | undefined>,
    default: () => [],
  },
  tab: {
    type: Number,
  },
  treeCode: {
    type: String,
  },
  unitId: {
    type: String,
  },
});

const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const columns = ref<DataTableColumns>([]);
const tableData = ref<IPageData[]>([]);
const personType = ref(1);
function setColumns() {
  columns.value.push(...cols);
  columns.value = [...cols];
  // 添加操作栏 action
  columns.value.splice(cols.length, 0, {
    title: '操作',
    key: 'actions',
    align: 'center',
    width: 240,
    render(row) {
      return route.query?.tab == '2' ? getActionBtn2(row) : getActionBtn1(row);
    },
  });
}

function getActionBtn1(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'jzy-person',
          disabled: row.userType && row.userType != 1,
          onClick: () =>
            emits('action', {
              action: ACTION.RESUME,
              data: toRaw({ ...row }),
            }),
        },
        { default: () => ACTION_LABEL.RESUME }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-del-button ',
          disabled: row.createUnitId && store.userInfo.orgCode != row.createUnitId,
          onClick: () => handleDelete(row),
        },
        { default: () => '移除' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

function getActionBtn2(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'jzy-person',
          disabled: row.userType && row.userType != 1,
          onClick: () => emits('action', { action: ACTION.RESUME2, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.RESUME2 }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-del-button ',
          disabled: row.createUnitId && store.userInfo.orgCode != row.createUnitId,
          onClick: () => handleDelete(row),
        },
        { default: () => '移除' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

const searchData = ref<string>('');
const levelCode = ref<string>('');
function getTableData() {
  setColumns();
  const params = {
    levelCode: levelCode.value || '',
    keyWords: searchData.value,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    type: personType.value,
    unitId: props.unitId,
  };
  search(getPersonManageList(params)).then((res: any) => {
    if (res.code != 200) return;
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  searchData.value = data.templateName;
  pagination.page = 1;
  getTableData();
}
// 删除
function handleDelete(row: any) {
  $dialog.error({
    title: '移除',
    content: `确定移除吗？`,
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      deletePersonManage({ id: row.id }).then((res: any) => {
        if (res.code == 200) {
          if (tableData.value.length - 1 == 0) {
            pagination.page = pagination.page - 1;
          }
          getTableData();
          message.success('移除成功');
        }
      });
    },
  });
}
watch(
  () => [props.tab, props.treeCode],
  (val) => {
    if (val[1] != null) {
      personType.value = val[0];
      levelCode.value = val[1];
      pagination.page = 1;
      getTableData();
    }
  },
  { immediate: true }
);

defineExpose({ getTableDataWrap, getTableData });
defineOptions({ name: 'personManageTable' });
</script>
<style lang="scss" scoped>
.box {
  --com-border-radius: 5px;
}
//
</style>
