<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-15 09:18:47
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-19 14:04:16
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/views/home/<USER>/checklist-conf/check-template/comp/Filter.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <n-form :show-feedback="false" label-placement="left">
    <div class="flex justify-between">
      <n-form-item>
        <n-input
          style="width: 300px"
          placeholder="请输入姓名、单位、部门或岗位关键词"
          v-model:value="filterForm.templateName"
          clearable
          @input="doHandle(ACTION.SEARCH)"
        >
          <template #suffix><IconSearch /></template>
        </n-input>
      </n-form-item>
      <div class="w-[12%] flex justify-end">
        <n-button type="primary" @click="doHandlee(ACTION.ADD)"> 新增 </n-button>
        <n-button type="primary" @click="doHandle(ACTION.EXPORT)" style="margin-left: 10px">导出</n-button>
      </div>
    </div>
  </n-form>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { ACTION } from '../constant';
import { BySearch as IconSearch } from '@kalimahapps/vue-icons';
import { trimObjNull } from '@/utils/obj.ts';

const emits = defineEmits(['action']);
const props: any = defineProps({
  getTree: {
    type: Function,
  },
});
const filterForm = ref(initForm());
function initForm() {
  return { templateName: '' };
}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: trimObjNull(filterForm.value),
  });
}

function doHandlee(action: ACTION) {
  emits('action', {
    action: action,
    data: trimObjNull(filterForm.value),
  });
  // props.getTree();
}
// watch(filterForm.value, () => {
//   doHandle(ACTION.SEARCH);
// });

onMounted(() => {
  // doHandle(ACTION.SEARCH);
});

defineOptions({ name: 'checkTempFilterComp' });
</script>
<style module lang="scss">
:global(.com-table-filter) {
  @apply rounded-tl-none border-none pb-[0px];
}
</style>
