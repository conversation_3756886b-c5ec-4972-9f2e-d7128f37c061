<template>
  <n-modal :show="props.showModal">
    <n-card
      class="n_card"
      :title="title"
      :tab="tab"
      :bordered="false"
      size="huge"
      preset="dialog"
      role="dialog"
      aria-modal="true"
    >
      <template #header-extra>
        <span class="sp" @click="cancel">x</span>
      </template>
      <div class="content">
        <div class="header">
          <div class="text">已选人员:</div>
          <div class="box">
            <n-tag
              :color="{
                color: '#fff',
                textColor: '#2080f0',
                borderColor: '#2080f0',
              }"
              v-for="(item, index) in checkedDatas"
              :key="index"
              closable
              size="small"
              @close="handleClose(item._id, item.id)"
            >
              {{ item.userName }}
            </n-tag>
          </div>
        </div>
        <div class="search-box">
          <div class="text" style="margin-top: 5px">关键字搜索：</div>
          <n-input
            placeholder="请输入员工姓名或岗位或手机号搜索1"
            :style="{ width: '50%' }"
            v-model:value="searchValue"
            @input="getTabData1"
          />
        </div>
        <div class="main">
          <div class="left">
            <div class="list">
              <comtwo-tree :data="treeData" @update="treeChange"></comtwo-tree>
            </div>
          </div>
          <div class="right">
            <!-- <div class="search-box">
              <n-input
                placeholder="请输入员工姓名或岗位或手机号搜索"
                :style="{ width: '50%' }"
                v-model:value="searchValue"
                @input="getTabData1"
              />
            </div> -->
            <div>
              <n-data-table
                remote
                striped
                :max-height="420"
                :columns="columns"
                :data="tableData"
                :bordered="false"
                :pagination="pagination"
                :loading="loading"
                :render-cell="useEmptyCell"
                v-model:checked-row-keys="checkedKeys"
                :row-key="(row: any) => row.id"
                @update:checked-row-keys="handleCheck"
                :scroll-x="410"
              />
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <n-space>
          <n-button strong @click="cancel">取消</n-button>
          <n-button strong type="primary" @click="onConfirm">保存</n-button>
        </n-space>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { ref, h, reactive, onMounted, watch, toRaw } from 'vue';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { CaCloseOutline as CloseOutline, BySearch as Search } from '@kalimahapps/vue-icons';
import { useStore } from '@/store';
const store = useStore();
import { NTag, NIcon, TreeOption, DataTableRowKey, NButton } from 'naive-ui';
import ComtwoTree from '@/components/tree/comTree2.vue';
import { getOrgTrees, getTreeDataPerson } from '../fetchData.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
const { pagination, updateTotal } = useNaivePagination(getTabData);
pagination.pageSlot = 5;
const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  tab: {
    type: Number,
    default: 1,
  },
  useArray: {
    type: Array<string | number | undefined>,
    default: () => [],
  },
  parentId: {
    type: String,
  },
  treeCode: {
    type: String,
  },
});

const emits = defineEmits(['close', 'success']);
const orgCode = ref('10000'); // 登录的orgcode
const [loading, search] = useAutoLoading(true);
const treeId: any = ref('');
// 选择项id合集
const checkedKeys = ref<Array<string | number>>([]);
// 选择项具体数据合集
const checkedDatas = ref<Array<any>>([]);
// #region 右侧表格
interface Song {
  id: number;
  userName: string;
  loginName: string;
  userTelphone: string;
  userTypeName: string;
}
const searchValue = ref<string>('');
const columns = <any[]>[
  {
    type: 'selection',
    disabled(row: any) {
      return row.isConfig == 1;
    },
  },
  {
    title: '序号',
    key: 'index',
    width: 60,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '人员姓名',
    key: 'userName',
    width: 120,
  },
  {
    title: '部门',
    key: 'deptName',
    width: 150,
  },
  {
    title: '岗位',
    key: 'postName',
  },
  {
    title: '员工类别',
    key: 'userTypeName',
    width: 120,
  },
];
const tableData = ref<Song[]>([]);

// const pagination = reactive({
//   page: 1,
//   pageSize: 10,
//   showSizePicker: true,
//   pageSizes: [10, 20, 50, 100],
//   onChange: (page: number) => {
//     pagination.page = page;
//   },
//   onUpdatePageSize: (pageSize: number) => {
//     pagination.pageSize = pageSize;
//     pagination.page = 1;
//   },
// });

const treeData = ref([]);
//监听树结构点击
const treeChange = (v: TreeOption) => {
  console.log('🚀 ~ treeChange ~ v:', v);
  pagination.page = 1;
  treeId.value = v;
  getTabData();
};
function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };

    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }

    return newItem; // 返回新对象
  });
}
const idd: any = ref('');
const levelCode: any = ref('');
//获取树结构数据
function QueryOrgTrees() {
  const params = {
    orgCode: idd.value ? idd.value : store.userInfo.unitId,
    needChildUnit: '1',
    needself: '1',
  };
  search(getOrgTrees(params)).then((res) => {
    if (res.code != 200) return;
    const _RES: any = removeEmptyChildren(res.data);
    treeData.value = _RES;
  });
}

function getTabData1(val = null) {
  pagination.page = 1;
  const params = {
    // orgCode: store.userInfo.orgCode,
    orgCode: treeId.value ? treeId.value : idd.value,
    keyWords: searchValue.value,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    type: props.tab,
  };
  // console.log('🚀 ~ getTabData ~ params:');
  // 获取树结构人员（总）
  //  function getTreeDataPerson(params: any) {
  //   const url = api.getUrl(api.type.server, api.name.interface.getTreeDataPerson);
  //   return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...params } });
  // }

  search(getTreeDataPerson(params)).then((res: any) => {
    if (res.code != 200) return;
    const _Data: any = res.data.rows ? res.data.rows : [];
    if (_Data.length === 0) {
      tableData.value = [];
      updateTotal(res.data.total || 0);
      return;
    }
    _Data.forEach((item: any) => {
      item._id = item.id + item.deptId;
    });
    tableData.value = _Data || [];
    updateTotal(res.data.total || 0);
  });
}
// 获取列表
function getTabData() {
  const params = {
    // orgCode: store.userInfo.orgCode,
    orgCode: treeId.value ? treeId.value : idd.value,
    keyWords: searchValue.value,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    type: props.tab,
  };
  // console.log('🚀 ~ getTabData ~ params:');
  search(getTreeDataPerson(params)).then((res: any) => {
    if (res.code != 200) return;
    const _Data: any = res.data.rows;
    if (_Data.length === 0) {
      tableData.value = [];
      return;
    }
    _Data.forEach((item: any) => {
      item._id = item.id + item.deptId;
    });
    tableData.value = _Data || [];
    updateTotal(res.data.total || 0);
  });
}

//勾选事件
function handleCheck(rowKeys: DataTableRowKey[], data: any, meta: any) {
  // console.log(rowKeys,data)
  const arr: any = [];
  for (const key of rowKeys) {
    for (const checkedData of checkedDatas.value) {
      if (key === checkedData.id) {
        const obj = arr.find((item: any) => {
          return item.id === checkedData.id;
        });
        if (obj === undefined) {
          arr.push(checkedData);
        }
      }
    }
    for (const item of data) {
      if (item !== undefined) {
        const obj = arr.find((itemObj: any) => {
          return itemObj.id === item.id;
        });
        if (obj === undefined) {
          arr.push(item);
        }
      }
    }
  }

  // arr[0].levelCode = code.value
  checkedDatas.value = arr;
}
// 取消选择事件
const handleClose = (value: any, id: any) => {
  checkedDatas.value = checkedDatas.value.filter((item) => {
    return item.id !== id;
  });
  checkedKeys.value = checkedKeys.value.filter((item) => {
    return item !== value;
  });
};
const getTreeAndTable = () => {
  QueryOrgTrees();
  getTabData();
  // getTabData1();
};
defineExpose({
  getTreeAndTable,
});
const cancel = () => {
  emits('close');
  // 置空id合集
  checkedKeys.value = [];
  checkedDatas.value = [];
  pagination.pageNo = 1;
  pagination.pageSize = 10;
  emits('close');
};
//输出id合集
const onConfirm = () => {
  checkedDatas.value = checkedDatas.value.filter((item) => {
    return item.id !== undefined;
  });
  if (checkedDatas.value.length > 0) {
    emits('success', checkedDatas.value);
    pagination.pageNo = 1;
    pagination.pageSize = 10;
    cancel();
  } else {
    message.error('请选择人员');
  }
};

watch(
  () => props.showModal,
  (nv) => {
    searchValue.value = '';
    if (!props.showModal) return;
    const _checkedKeys = toRaw(props.useArray);
    if (_checkedKeys.length) {
      checkedDatas.value = _checkedKeys;
      _checkedKeys.filter((item: any) => checkedKeys.value.push(item.id));
    }
  },
  { immediate: true }
);

watch(
  () => [props.parentId, props.treeCode],
  (val) => {
    if (val[1] != null) {
      idd.value = val[0];
      levelCode.value = val[1];

      // getTabData();
      // getTabData1();
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
// :deep(.n-data-table.n-data-table--bordered .n-data-table-wrapper) {
//   overflow: auto;
// }
.sp {
  // font-family: '宋体';
  font-size: 24px;
}
.sp:hover {
  cursor: pointer;
}
.n_card {
  width: 1000px;
  height: 810px;
  background-color: #f8f8f8;
  :deep(.n-card-header .n-card-header__main::before) {
    content: '';
    display: inline-block;
    width: 4px;
    height: 15px;
    margin-right: 5px;
    background-color: #527cff;
  }
}
.content {
  height: 100%;

  .header {
    display: flex;
    margin-bottom: 10px;

    .text {
      color: #606266;
    }

    .box {
      flex: 1;
      margin-left: 10px;
      max-height: 60px;
      height: 70px;
      border-radius: 3px;
      border: 1px solid #dcdfe6;
      background-color: #fff;
      overflow-y: auto;
    }
  }

  .main {
    height: 550px;
    display: flex;
    justify-content: space-between;

    .left {
      width: 280px;
      margin-right: 10px;
      background-color: #fff;
      border-radius: 3px;
      border: 1px solid #e1e1e1;

      .list {
        height: 100%;
        overflow-y: auto;
      }

      .org {
        height: 50px;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f7fa;
      }
    }

    .right {
      flex: 1;
      padding: 10px 10px;
      background-color: #fff;
      border-radius: 3px;
      border: 1px solid #e1e1e1;

      // .search-box {
      //   width: 100%;
      //   margin-bottom: 10px;
      //   display: flex;
      //   justify-content: end;
      // }
    }
  }
}

.search-box {
  width: 100%;
  margin-bottom: 10px;
  display: flex;
  justify-content: end;
}
.n-space {
  position: absolute;
  bottom: 1%;
  right: 5%;
}
:deep(.n-card__footer) {
  display: flex;
  justify-content: flex-end;
  padding-top: 30px !important;
}
:deep(.n-tag) {
  padding: 10px 12px !important;
  margin: 5px 5px 0 5px !important;
  border-radius: 4px;
}
:deep(.n-base-close) {
  color: #2080f0ed;
}

:deep(.n-checkbox.n-checkbox--disabled .n-checkbox-box) {
  background-color: #a8a29e;
}
</style>
