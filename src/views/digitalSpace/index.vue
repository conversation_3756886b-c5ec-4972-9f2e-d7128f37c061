<template>
  <div class="digitalSpace w-full h-full">
    <!-- 顶部 -->
    <div class="digitalTop">
      <div class="left"></div>
      <div class="center">{{ ui.zhName }}</div>
      <div class="right flex items-center">
        {{ formatted }}
        <n-icon class="ml-[10px] cursor-pointer" color="#29A2DD" :size="20" @click="router.go(-1)">
          <SwitchButton />
        </n-icon>
      </div>
    </div>
    <!-- 中部内容 -->
    <div class="digitalMiddle">
      <!-- 地球 -->
      <div class="earth">
        <div class="total">
          <span class="text">已签到人数</span>
          <span class="number"
            ><span class="text-3.5vw">{{ signInSheet.signed }}</span
            >/{{ signInSheet.tableInsert }}</span
          >
        </div>

        <div class="middle-show-box">
          <div class="middle-show-item">
            <div class="icon"></div>
            <div class="content">会议类型：{{ meetingInfo.meetingType == 1 ? '线上会议' : '线下会议' }}</div>
          </div>
          <div class="middle-show-item2">
            <div class="icon"></div>
            <div class="content">发起人：{{ meetingInfo.createUserName }}</div>
          </div>
          <div class="middle-show-item3">
            <div class="icon"></div>
            <div class="content">会议时间：{{ meetingInfo.chsj }}</div>
          </div>
          <div class="middle-show-item4">
            <div class="icon"></div>
            <div class="content">会议时长：{{ findTime(+meetingInfo.meetingDuration * 60) }}</div>
          </div>
        </div>
      </div>
      <!-- 二维码 -->
      <div class="qr-code">
        <div class="sign-in">
          <div class="sign-in-code">
            <qrcode-vue v-if="meetingInfo.ewmxx" :width="240" :height="240" :value="meetingInfo.ewmxx" :margin="4" />
          </div>
          <div class="sign-in-type"></div>
        </div>
      </div>
    </div>
    <!-- 标题 -->
    <n-popover placement="top-start" trigger="hover">
      <template #trigger>
        <div class="mainTitle">{{ meetingInfo.meetingName }}</div>
      </template>
      <span>{{ meetingInfo.meetingName }}</span>
    </n-popover>

    <!-- 底部头像区域 -->
    <div class="mainAvatar" v-loading="isLoading" element-loading-text="正在刷新...">
      <div v-if="+signInSheet.meetingSignVos > 12" class="avatar-box" ref="scrollRef">
        <div class="avatar-box-item" id="scrollBox">
          <template v-for="item in signInSheet.meetingSignVos" :key="item.userId">
            <div class="avatar">
              <n-image
                preview-disabled
                v-if="item.sfchCode == 1"
                style="width: 4vw; height: 4vw"
                :src="avatar"
                fit="cover"
              />
              <n-image preview-disabled v-else style="width: 4vw; height: 4vw" :src="avatarNull" fit="cover" />
              <div v-if="item.sfchCode == 1" class="name">
                <n-popover trigger="hover">
                  <template #trigger>
                    {{ item.userName.slice(0, 5) }}
                  </template>
                  {{ item.userName }}
                </n-popover>
              </div>
            </div>
          </template>
        </div>
        <div class="avatar-box-item mt-[16px]">
          <template v-for="item in signInSheet.meetingSignVos" :key="item.userId">
            <div class="avatar">
              <n-image
                preview-disabled
                v-if="item.sfchCode == 1"
                style="width: 4vw; height: 4vw"
                :src="avatar"
                fit="cover"
              />
              <n-image preview-disabled v-else style="width: 4vw; height: 4vw" :src="avatarNull" fit="cover" />
              <div v-if="item.sfchCode == 1" class="name">
                <n-popover trigger="hover">
                  <template #trigger>
                    {{ item.userName.slice(0, 5) }}
                  </template>
                  {{ item.userName }}
                </n-popover>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div v-else class="avatar-box-not-enough">
        <div class="avatar-box-item">
          <template v-for="item in signInSheet.meetingSignVos" :key="item.userId">
            <div class="avatar">
              <n-image
                preview-disabled
                v-if="item.sfchCode == 1"
                style="width: 4vw; height: 4vw"
                :src="avatar"
                fit="cover"
              />
              <n-image preview-disabled v-else style="width: 4vw; height: 4vw" :src="avatarNull" fit="cover" />

              <div v-if="item.sfchCode == 1" class="name">
                <n-popover trigger="hover">
                  <template #trigger>
                    {{ item.userName.slice(0, 5) }}
                  </template>
                  {{ item.userName }}
                </n-popover>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onUnmounted, ref } from 'vue';
import avatarNull from '../../assets/image/digitalspace/avatar-null.png';
import avatar from '../../assets/image/digitalspace/avatar.png';
import { SwitchButton } from '@element-plus/icons-vue';
import { useDateFormat, useNow } from '@vueuse/core';
import QrcodeVue from 'qrcode-vue3';
import { useRoute, useRouter } from 'vue-router';
import { getMeetingDetail, getMeetingSignList } from '@/views/securityMeetings/fetchData';
import { useStore } from '@/store';

defineOptions({ name: 'DigitalSpaceIndex' });

const store = useStore();
const ui = store.userInfo;
const route = useRoute();
const router = useRouter();
const isLoading = ref(false);
const scrollRef = ref();

const formatted = useDateFormat(useNow(), 'YYYY-MM-DD HH:mm:ss');
const signInSheet = ref<any>({});
const meetingInfo = ref<any>({
  meetingType: '',
  createUserName: '',
  chsj: '',
  meetingDuration: '',
});
const avatarBoxHeight = ref('-530px');
const getDate = () => {
  const params = {
    meetingId: route.query.id,
  };
  getMeetingSignList(params).then((res: any) => {
    signInSheet.value = res.data;
    if (!(signInSheet.value && signInSheet.value.meetingSignVos)) {
      signInSheet.value = {
        signed: 0,
        tableInsert: 0,
        meetingSignVos: [],
      };
    }
    handleMeetingSignVos();
  });
};

function handleMeetingSignVos() {
  let num = 12 - signInSheet.value.meetingSignVos.length;
  if (num > 0) {
    for (let i = 0; i < num; i++) {
      const obj = {
        userId: 'userId' + i,
        userName: 'userName',
      };
      signInSheet.value.meetingSignVos.push(obj);
    }
  }
}

function getMeetingDetailById() {
  const params = {
    id: route.query.id,
  };
  getMeetingDetail(params).then((res: any) => {
    meetingInfo.value = res.data;
  });
}

// 定时刷新签到人数
const timer = setInterval(() => {
  getDate();
  // 如果签到人数达到总人数，则停止定时器
  if (+signInSheet.value.signed === +signInSheet.value.tableInsert) {
    clearInterval(timer);
  }
}, 3000);

const findTime = (num: number) => {
  if (num < 1) {
    return '0';
  }
  const numToStr = (m: number, unit: any) =>
    m > 0
      ? ` ${m}
   ${unit}}`
      : '';
  const oneMinute = 60;
  const oneHour = oneMinute * 60;
  const oneDay = oneHour * 24;
  const oneYear = oneDay * 365;
  const times: any = {
    年: ~~(num / oneYear),
    天: ~~((num % oneYear) / oneDay),
    小时: ~~((num % oneDay) / oneHour),
    分钟: ~~((num % oneHour) / oneMinute),
    秒: num % oneMinute,
  };
  let str = '';
  const searchRegExp = /}/gi;
  const searchRegExp2 = /{/gi;
  const replaceWith = '';
  for (let [key, value] of Object.entries(times)) {
    console.log(value);
    str += numToStr(times[key], key);
    str = str.replace(searchRegExp, replaceWith);
    str = str.replace(searchRegExp2, replaceWith);
  }
  const arr = str.trim().split(' ');
  const res: any[] = [];
  arr.forEach((x, i) => {
    if (i % 2 === 0 && i !== 0) res.push(i === arr.length - 2 ? '' : '');
    res.push(x);
  });

  return res.join(' ').replace(/\s,/g, '');
};

getDate();
getMeetingDetailById();

onUnmounted(() => {
  clearInterval(timer); // 退出页面关闭轮询
});
</script>

<style lang="scss" scoped>
.digitalSpace {
  height: 100vh;
  position: relative;
  background: url('../../assets/image/digitalspace/digital.png') no-repeat center;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;

  .digitalTop {
    height: 77px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 50px;
    background: url('../../assets/image/header1.png') no-repeat center/cover;
    background-size: 100% 100%;

    .left,
    .right {
      color: #fff;
      font-size: 14px;
      width: 200px;
      height: 55px;
      line-height: 55px;
    }

    .center {
      text-align: center;
      display: flex;
      justify-content: center;
      font-family: MyCustomFont, sans-serif;
      font-weight: 400;
      font-size: 2.305rem;
      padding-top: 5px;
      color: #000000;
      background: linear-gradient(180deg, #e8ebee 0%, #63a8dd 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .digitalMiddle {
    position: relative;
    width: 100%;
    height: 100%;
    flex: 1;

    .earth {
      position: absolute;
      top: 5vh;
      left: 50px;
      width: 36vw;
      height: 33vw;
      background: url('../../assets/image/digitalspace/earth.png') no-repeat center;
      background-size: 100% 100%;

      .total {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 21.8vw;
        height: 12vw;
        border-top: 1px solid;
        border-bottom: 1px solid;
        border-image: linear-gradient(to right, transparent 0%, #35afff 10%, #35afff 90%, transparent) 1;
        /* 边框渐变 */
        background: linear-gradient(
          to right,
          transparent 0%,
          rgba(52, 172, 252, 0.2) 10%,
          rgba(52, 172, 252, 0.2) 90%,
          transparent 100%
        );
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .text {
          font-family: Alibaba PuHuiTi;
          font-weight: bold;
          font-size: 3vw;
          color: #ffffff;
        }

        .number {
          font-family: Alibaba PuHuiTi;
          font-weight: bold;
          font-size: 2.5vw;
          color: #ffffff;
        }

        .qiandao-cont {
          position: relative;
          top: 193px;
          width: 13vw;
          height: 10vw;
          background: url('../../assets/image/digitalspace/qddetail.png') no-repeat center center;
          background-size: cover;
          display: flex;
          justify-content: center;
          align-items: center;
          display: flex;
          justify-content: center;
          align-items: center;

          .text_qiandao {
            color: #fff;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
          }
        }
      }

      .middle-show-box {
        position: absolute;
        top: 57%;
        right: -58%;
        width: 25vw;
        height: 21vw;
        transform: translateY(-50%);
        background: url('../../assets/image/digitalspace/content.png') no-repeat center;
        background-size: 100% 100%;

        .middle-show-item,
        .middle-show-item2,
        .middle-show-item3,
        .middle-show-item4 {
          position: absolute;
          top: 0;
          left: 6vw;
          width: 40vw;
          height: 5vw;
          display: flex;
          align-items: center;

          .icon {
            margin-right: 20px;
            width: 3vw;
            height: 3vw;
            background: url('../../assets/image/digitalspace/type.png') no-repeat center/cover;
          }

          .content {
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 1.2vw;
            color: #ffffff;
            line-height: 133px;
            text-shadow: 0px 3px 4px rgba(166, 250, 255, 0.69);
          }
        }

        .middle-show-item2 {
          top: 24%;
          left: 7vw;

          .icon {
            background: url('../../assets/image/digitalspace/style.png') no-repeat center/cover;
          }
        }

        .middle-show-item3 {
          top: 50%;
          left: 7vw;

          .icon {
            background: url('../../assets/image/digitalspace/time.png') no-repeat center/cover;
          }
        }

        .middle-show-item4 {
          top: 75%;
          left: 5vw;

          .icon {
            background: url('../../assets/image/digitalspace/duration.png') no-repeat center/cover;
          }
        }
      }
    }

    .qr-code {
      position: absolute;
      top: 0;
      right: 20px;
      height: 100%;
      width: 20vw;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;

      .sign-in,
      .exam {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        height: 23vw;

        .sign-in-code,
        .exam-code {
          width: 10vw;
          height: 10vw;
          background-color: #fff;

          .exam-code-null {
            width: 10vw;
            height: 10vw;
            background: url('../../assets/image/digitalspace/mohu.png') no-repeat center;
            background-size: 95% 95%;
            position: relative;

            &::before {
              content: '点击扫码考试';
              width: 10vw;
              // height: 10%;
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              background-color: rgba(0, 0, 0, 0.5);
              color: #fff;
              text-align: center;
              padding: 10px;
            }
          }
        }

        .sign-in-type,
        .exam-type {
          width: 100%;
          height: 7vw;
          background: url('../../assets/image/digitalspace/signInCode.png') no-repeat center/cover;
        }

        .exam-type {
          background: url('../../assets/image/digitalspace/examCode.png') no-repeat center/cover;
        }
      }
    }
  }

  .mainTitle {
    position: absolute;
    top: 7vw;
    left: 60%;
    transform: translate(-50%);
    width: 45vw;
    font-family: Source Han Sans CN;
    font-weight: bold;
    font-size: 3vw;
    color: #ffffff;
    text-shadow: 0px 3px 4px rgba(166, 250, 255, 0.69);
    // 不换行，且超出使用省略号
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .mainAvatar {
    padding: 10px;
    width: 40vw;
    height: 20vh;
    position: absolute;
    left: 58%;
    bottom: 2%;
    transform: translate(-50%);
    background: linear-gradient(
      to right,
      transparent 0%,
      rgba(52, 172, 252, 0.2) 10%,
      rgba(52, 172, 252, 0.2) 90%,
      transparent 100%
    );

    border-top: 1px solid;
    border-image: linear-gradient(to right, transparent 0%, #35afff 10%, #35afff 90%, transparent) 1;
    /* 边框渐变 */
    border-bottom: 1px solid;
    overflow: hidden;

    .avatar-box,
    .avatar-box-not-enough {
      width: 100%;
      animation: move 10s 0s linear infinite;

      .avatar-box-item {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        justify-items: center;
        row-gap: 16px;

        .avatar {
          width: 4vw;
          height: 4vw;
          // background: fixed url('../assets/image/digitalspace/avatar.png') no-repeat center/cover;
          color: #fff;
          position: relative;

          img {
            width: 4vw;
            height: 4vw;
          }

          .name {
            position: absolute;
            bottom: 1px;
            width: 100%;
            font-size: 0.65vw;
            text-align: center;
          }
        }
      }
    }

    .avatar-box-not-enough {
      animation: none;
    }
  }

  // 动画
  @keyframes move {
    from {
      transform: translate(0, 0);
    }

    to {
      transform: translate(0, v-bind(avatarBoxHeight));
    }
  }
}

.v-mini-weather-icon {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}

.v-weather {
  display: flex;
  align-items: center;
}

/*修改图标样式*/
.v-mini-weather-icon {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}

/*修改文本样式*/
.weather-text {
  color: #000;
}

.dio-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 3px;
  background: linear-gradient(90deg, rgba(0, 155, 236, 1), rgba(0, 155, 236, 0.6));
}

.dio-header2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #4ba4ff;
  border-bottom: none;
  padding: 0 3px;
  background: linear-gradient(90deg, rgba(8, 109, 170), rgba(5, 33, 60));
}

.main-dio {
  width: 100%;
  min-height: 300px;
  background: linear-gradient(90deg, rgba(5, 25, 48, 0.8), rgba(0, 9, 32, 0.6));
  border: 1px solid #4ba4ff;
  border-top: none;
  padding: 20px;
  box-shadow: inset 0 -8px 30px 1px rgba(3, 145, 252, 0.5);

  .qdtable {
    width: 100%;
    color: #fff;

    .title {
      margin: 10px;
      padding-left: 5px;
    }

    .qd-cont {
      display: flex;
      flex-wrap: wrap;
      height: 200px;
      overflow-y: auto;

      .one-cont {
        width: 101px;
        height: 38px;
        text-align: center;
        background: #091b2c;
        line-height: 38px;
        margin: 5px 3px;
        border-radius: 4px;
        border: 1px solid #4ba4ff;
      }

      &::-webkit-scrollbar {
        display: none;
      }
    }
  }
}

.one-cont2 {
  display: inline-block;
  width: 180px;
  height: 38px;
  text-align: center;
  background: transparent;
  line-height: 38px;
  margin: 5px 15px;
  border-radius: 4px;
  border: 1px solid #4ba4ff !important;
  font-family: Alibaba PuHuiTi;
  font-weight: 500;
  font-size: 14px;
  color: #ffffff;
  cursor: pointer;
}

.one-cont3 {
  border: 3px solid #8bd1e6;
  background: rgba(31, 143, 212, 0.9);
}
</style>
