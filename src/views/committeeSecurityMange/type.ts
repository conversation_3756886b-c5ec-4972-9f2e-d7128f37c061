import { ACTION } from './constant';
import type { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 分页列表数据
export interface IPageData {
  /**
   * 行政区划编码
   */
  areaCode: string;
  /**
   * 行政区划名称
   */
  areaName: string;
  createdBy: string;
  createdTime: string;
  id: string;
  isIot: string;
  /**
   * 是否建设物联网
   */
  isIotName: string;
  jurisdiction: string;
  /**
   * 被管辖范围
   */
  jurisdictionName: string;
  /**
   * 单位名称
   */
  unitName: string;
  unittype: string;
  /**
   * 单位类型中文
   */
  unitTypeName: string;
  updatedBy: string;
  updatedTime: string;
}
export type IPageDataRes = IPageRes<IPageData>;

export interface IDetail {
  areaCode: string;
  areaName: string;
  createdBy: string;
  createdTime: string;
  /**
   * 主键ID
   */
  id: string;
  isIot: string;
  /**
   * 单位辖区范围
   */
  jurisdiction: string;
  unitName: string;
  unitType: string;
  updatedBy: string;
  updatedTime: string;
}
export interface ICategory {
  unitName?: string;
  id?: string;
  unitTypeName?: string;
  type?: string;
  bumen?: string;
  phone?: number;
  jianjie?: string;
  chengyuan?: (string | number | boolean)[];
}
export interface ApifoxModel {
  code?: string;
  data?: SafetyCommitteeVo[];
  dataType?: string;
  message?: { [key: string]: any };
  status?: string;
  token?: string;
  [property: string]: any;
}

export interface SafetyCommitteeVo {
  /**
   * 附件
   */
  attachments?: AttachmentVo[];
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人Id
   */
  createUserId?: string;
  /**
   * 创建人
   */
  createUserName?: string;
  /**
   * 负责单位/部门
   */
  dept?: string;
  /**
   * 安委会id
   */
  id?: string;
  /**
   * 成员人数
   */
  memberNum?: number;
  /**
   * 安委会名称
   */
  name?: string;
  /**
   * 联系电话
   */
  phone?: string;
  /**
   * 简介
   */
  profile?: string;
  /**
   * 安委会级别 0:集团；1:单位；2:部门
   */
  securityCommissionType?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 更新人id
   */
  updateUserId?: string;
  /**
   * 更新人
   */
  updateUserName?: string;
  [property: string]: any;
}
/**
 * AttachmentVo
 */
export interface AttachmentVo {
  /**
   * 附件地址
   */
  address?: string;
  /**
   * 附件ID
   */
  id?: string;
  [property: string]: any;
}
export interface OrgTree {
  /**
   * 节点属性
   */
  attributes?: { [key: string]: any };
  /**
   * 点是否被选中
   */
  checked?: boolean;
  /**
   * 节点的子节点
   */
  children?: OrgTree[];
  hasChildren?: boolean;
  hasParent?: boolean;
  /**
   * 主键id
   */
  id?: string;
  /**
   * 层级
   */
  level?: number;
  /**
   * 父ID
   */
  parentId?: string;
  state?: string;
  /**
   * 节点名称
   */
  text?: string;
  /**
   * 树名
   */
  treeName?: string;
  /**
   * 节点类型
   */
  type?: string;
  /**
   * 节点id
   */
  typeId?: string;
  [property: string]: any;
}
