/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-15 09:18:47
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-15 17:57:46
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/views/configure-mgr/checklist-conf/check-template/comp/table/columns.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',

    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '姓名',
    key: 'userName',
    align: 'center',

    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '手机号',
    key: 'userTelphone',
    align: 'center',

    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '单位',
    key: 'unitName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '部门',
    key: 'deptName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '岗位',
    key: 'postName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
];
