import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '安委会名称',
    key: 'name',
    align: 'center',

    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '级别',
    key: 'securityCommissionType',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '负责单位/部门',
    key: 'dept',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '联系电话',
    key: 'phone',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '简介',
    key: 'profile',
    align: 'center',
    width: 200,
    ellipsis: {
      'line-clamp': 3,
    },
  },
  {
    title: '成员',
    key: 'memberNum',
    align: 'center',

    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      return row.memberNum + '人';
    },
  },
];
