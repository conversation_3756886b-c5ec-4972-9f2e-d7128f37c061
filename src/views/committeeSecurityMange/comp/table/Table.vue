<template>
  <div class="box">
    <n-data-table
      class="h-full com-table"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :pagination="pagination"
      :loading="loading"
      :render-cell="useEmptyCell"
      :theme-overrides="themeOverrides"
    />
  </div>
</template>

<script lang="ts" setup>
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useStore } from '@/store';
import { IObj } from '@/types';
import mittBus from '@/utils/mittBus';
import { Base64 } from 'js-base64';
import { DataTableColumns, NButton } from 'naive-ui';
import { h, ref, toRaw, VNode } from 'vue';
import { cols } from '../../comp/table/columns';
import { ACTION } from '../../constant';
import { getSafeTyData } from '../../fetchData';
import type { IPageData } from '../../type';

const store = useStore();

const props: any = defineProps({
  treeId: {
    type: String,
  },
});
const emits = defineEmits(['action']);
const themeOverrides = {
  tdColorStriped: '#dfeefc',
  // tdColorHover: 'rgba(18, 83, 123, 0.35)',
  thColor: '#BBCCF3',
  thTextColor: '#222',
  tdColorStripedModal: 'red',
  // tdColorHoverModal: 'rgba(18, 83, 123, 0.35)',
  // tdColorHoverPopover: 'rgba(18, 83, 123, 0.35)',
};
const [loading, search] = useAutoLoading(true);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IPageData[]>([]);

let filterData: IObj<any> = {}; // 搜索条件
const { pagination, updateTotal } = useNaivePagination(getTableData);
mittBus.on('addUpdate', (v: any) => {
  pagination.page = v;
});
function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };
  console.log(params, 'params=======');
  search(getSafeTyData(params)).then((res: any) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}
function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

function setColumns() {
  columns.value.push(...cols);

  // 添加操作栏 action
  columns.value.push(
    {
      title: '附件',
      key: 'attachments',
      width: '100',
      align: 'center',
      render: (row: any) => {
        if (!row.attachments.length) {
          return '--';
        }
        // 假设 row.attachments 是一个包含附件信息的数组
        return row.attachments.map((attachment: any) => {
          // 根据附件类型生成相应的虚拟DOM元素
          const name = attachment.address.split('/');
          const fileName = name[name.length - 1];
          const type = fileName.split('.');
          const typeName = type[type.length - 1];

          if (typeName == 'doc' || typeName == 'docx') {
            console.log(
              window.$SYS_CFG.apiPreviewURL + attachment.address,
              'word'
            );
            var b64Encoded = Base64.encode(
              window.$SYS_CFG.apiPreviewURL + attachment.address
            );
            return h(
              'a',
              {
                href:
                  window.$SYS_CFG.apiPreviewLink +
                  encodeURIComponent(b64Encoded),
                title: fileName,
                target: '_blank',
                data: window.$SYS_CFG.apiPreviewURL + attachment.address,
                style: {
                  color: '#02a7f0',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                  overflow: 'hidden',
                  width: '120px',
                  display: 'inline-block',
                },
              },
              fileName
            );
          } else {
            return h(
              'a',
              {
                href: window.$SYS_CFG.apiPreviewURL + attachment.address,
                title: fileName,
                target: '_blank',
                data: window.$SYS_CFG.apiPreviewURL + attachment.addres,
                style: {
                  color: '#02a7f0',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                  overflow: 'hidden',
                  width: '120px',
                  display: 'inline-block',
                },
              },
              fileName
            );
          }
        });
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 290,
      align: 'center',
      render(row) {
        return getActionBtn(row);
      },
    }
  );
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-edit-button',
          disabled: row.createUnitId != store.userInfo.orgCode,
          onClick: () =>
            emits('action', { action: ACTION.EDIT, data: toRaw(row) }),
        },
        { default: () => '编辑' }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-del-button',
          disabled: row.createUnitId != store.userInfo.orgCode,
          onClick: () =>
            emits('action', { action: ACTION.DELET, data: toRaw(row) }),
        },
        { default: () => '删除' }
      ),
    ],
  ];

  return useActionDivider(acList);
}
// on created
setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
});

defineOptions({ name: 'CommitteeSecurityMangeTable' });
</script>

<style module lang="scss"></style>
