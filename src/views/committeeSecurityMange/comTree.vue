<template>
  <div
    class="bg-[#EEF7FF] w-[323px] h-full"
    style="border-radius: 5px; border-right: 1px solid #eee"
  >
    <!-- 采用menu的形式 可以自由选择-->
    <!-- <n-menu :options="menuOptions" :default-expanded-keys="defaultExpandedKeys" accordion default-value="test1" /> -->
    <!-- 采用表单树的形式 -->
    <!-- <h3 class="p-4 pb-[0]"></h3> -->
    <div class="flex justify-start items-center mb-4 pt-5 pl-5">
      <img src="@/assets/icon.png" style="width: 18px" alt="" />
      <div style="font-size: 16px; font-weight: 600; margin-left: 10px">
        所属单位
      </div>
    </div>
    <div style="padding-left: 15px; padding-right: 15px; margin-bottom: 5px">
      <n-input v-model:value="pattern" placeholder="搜索" clearable>
        <template #suffix>
          <BySearch :class="$style['icon']" />
        </template>
      </n-input>
    </div>
    <n-scrollbar style="height: calc(100vh - 171px)">
      <n-tree
        ref="treeRef"
        class="px-4"
        block-line
        :data="treeData"
        :pattern="pattern"
        selectable
        key-field="id"
        :cancelable="false"
        label-field="text"
        :show-irrelevant-nodes="false"
        children-field="children"
        :node-props="nodeProps"
        :default-expanded-keys="defaultExpandedkeys"
        :render-switcher-icon="renderSwitcherIconWithExpaned"
        :default-selected-keys="defaultSelectedkeys"
        :render-label="renderLabel"
        :theme-overrides="themeOverrides"
        :override-default-node-click-behavior="override"
      />
    </n-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, h, reactive, watch, ref, nextTick } from 'vue';
// import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
// import type { MenuOption } from 'naive-ui';
import {
  CaDataStructured as structure,
  CaEnterprise as unit,
} from '@kalimahapps/vue-icons';
import { BySearch } from '@kalimahapps/vue-icons';
import type { Component } from 'vue';
import { NIcon, TreeOption } from 'naive-ui';
import { getTreeDataPerson } from './fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { icon_chevron as iconChevronForward } from './assets/index';
import Dept from './assets/dept.png';
import YeWu from './assets/yewu.png';
import JianGuan from './assets/jianguan.png';
import Zhongdanong from '@/assets/iconImg/zhongdanong.png';
import YangChang from '@/assets/iconImg/yanchang.png';
import AnGang from '@/assets/iconImg/angang.png';
import WyCh from '@/assets/iconImg/wych2.png';
import { useStore } from '@/store';
const store = useStore();
const treeRef = ref(null);
const [loading, search] = useAutoLoading(true);
const defaultExpandedkeys = ref<string[]>([]);
const defaultSelectedkeys = ref<string[]>([]);
// 选中样式

const themeOverrides = {
  nodeHeight: '40px',
};
const pattern = ref('');
function renderSwitcherIconWithExpaned({ option }: any) {
  if (option.root === 1) {
    return null;
  } else {
    if (option.children && option.children.length > 0) {
      return h(
        NIcon,
        {
          style: {
            width: '14px',
            height: '14px',
          },
        },
        { default: () => h(iconChevronForward) }
      );
    }
  }
}
function renderLabel(e: any) {
  // 如果节点没有子节点，不显示展开图标
  if (e.option.children && e.option.children.length === 0) {
    e.option.isLeaf = true;
    return h(
      'div',
      {
        style: {
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          padding: '10px 0 10px 0',
        },
      },
      [
        h('img', {
          style: {
            width: '16px',
            height: '16px',
            marginRight: '5px',
          },
          src:
            e.option.attributes?.orgType === '2'
              ? JianGuan
              : e.option.attributes?.orgType === '1'
                ? YeWu
                : Dept,
        }),
        h(
          'div',
          {
            style: {
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            },
            title: e.option.text,
          },
          e.option.text
        ),
      ]
    );
  } else {
    // 如果节点有子节点，正常显示
    if (e.option.root === 1) {
      // e.option.isLeaf = true;
      return h(
        'div',
        {
          style: {
            display: 'flex',
            alignItems: 'center',
            padding: '10px 0 10px 0',
          },
        },
        [
          h('img', {
            style: {
              width: '16px',
              height: '16px',
              marginRight: '10px',
              // marginLeft: '24px',
            },
            src: store.userInfo.logoPicUrl,
          }),
          h(
            'div',
            {
              style: {
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              },
              title: e.option.text,
            },
            e.option.text
          ),
        ]
      );
    } else {
      return h(
        'div',
        {
          style: {
            display: 'flex',
            alignItems: 'center',
            padding: '10px 0 10px 0',
            marginLeft: '-10px',
          },
        },
        [
          h('img', {
            style: {
              width: '16px',
              height: '16px',
              marginRight: '5px',
            },
            src:
              e.option.attributes?.orgType === '2'
                ? JianGuan
                : e.option.attributes?.orgType === '1'
                  ? YeWu
                  : Dept,
          }),
          h(
            'div',
            {
              style: {
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              },
              title: e.option.text,
            },
            e.option.text
          ),
        ]
      );
    }
  }
}
// function renderPrefix(info: {
//   option: TreeOption;
//   checked: boolean;
//   selected: boolean;
// }) {
//   function traverseOptions(options: any): any {
//     for (const key in options) {
//       let value = options[key]; // 获取对应的值
//       if (key === 'attributes') {
//         if (value && typeof value === 'object' && 'orgType' in value) {
//           if (value.orgType === '0') {
//             return h(NIcon, null, {
//               default: () =>
//                 h('img', {
//                   src: Dept,
//                 }),
//             }); // 返回 unit 组件
//           } else if (value.orgType === '1') {
//             return h(NIcon, null, {
//               default: () =>
//                 h('img', {
//                   src: YeWu,
//                 }),
//             }); // 返回 unit 组件
//           } else if (value.orgType === '2') {
//             return h(NIcon, null, {
//               default: () =>
//                 h('img', {
//                   src: JianGuan,
//                 }),
//             }); // 返回 unit 组件
//           }
//         } else {
//           // console.log(`"attributes" 键存在，但没有 orgType 属性`);
//         }
//       }

//       // 处理 children 数组或嵌套对象
//       if (Array.isArray(value)) {
//         for (const item of value) {
//           const result = traverseOptions(item);
//           if (result) {
//             return result; // 找到后返回结果
//           }
//         }
//       } else if (typeof value === 'object' && value !== null) {
//         const result = traverseOptions(value);
//         if (result) {
//           return result; // 找到后返回结果
//         }
//       }
//     }
//     return null; // 如果没有找到，返回 null
//   }

//   return traverseOptions(info.option) || null; // 调用递归函数
// }
const emits = defineEmits(['action', 'treeChange']);

const props = defineProps({
  dataList: {
    type: Array,
    default: () => [],
  },
});

// const [loading, search] = useAutoLoading(false);
// const defaultExpandedKeys = ['test'];
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 15, 20, 30, 50, 100],
  onChange: (page: number) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  },
});
const nodeProps = ({ option }: { option: TreeOption }) => {
  // return {
  //   onClick() {
  //     // emits('getTableData', '11111');
  //     // 这里写点击组组织后的逻辑
  //     const params = {
  //       // unitId: treeId.value ? treeId.value : riskUnitID.value,
  //       levelPath: option.levelCode,
  //       pageNo: pagination.page,
  //       pageSize: pagination.pageSize,
  //       type: 1,
  //     };
  //     emits('getTableData', params);
  //   },
  // };
};
const treeData = ref([]);
watch(
  () => props.dataList,
  (nv) => {
    if (props.dataList?.length) {
      // console.log('🚀 ~ nv.value:2727777777', nv);
      let curKey = props.dataList[0].id as string;
      defaultExpandedkeys.value = [curKey];
      defaultSelectedkeys.value.push(curKey);
      treeData.value = props.dataList;
      treeData.value[0].root = 1;
      nextTick(() => {
        const firstNodeSwitcher = treeRef.value.selfElRef.querySelector(
          '.n-tree-node-switcher'
        );
        if (firstNodeSwitcher) {
          // 设置display为none
          firstNodeSwitcher.style.display = 'none';
        }
      });
      // console.log('🚀 ~ defaultSelectedkeys.value:2727777777', defaultExpandedkeys.value);
      const params = {
        levelPath: props.dataList[0].levelCode,
        pageNo: pagination.page,
        pageSize: pagination.pageSize,
        type: 1,
      };
      // emits('getTableData', params);
      emits('treeChange', props.dataList[0]);
      calleArr(props.dataList);
    }
  },
  { immediate: true }
);
function calleArr(array: Array<any>) {
  var data = array;
  if (data[0].children?.length) {
    calleArr(data[0].children);
  } else {
    // console.log(data, ':::::::::--------别来烦我');
    // emits('treeChange', data[0]);
    // defaultSelectedkeys.value.push(data[0].id);
    // localStorage.setItem('_riskUnitID', data[0].id);
    // localStorage.setItem('_riskUnitName', data[0].text);
  }
}
function override({ option }: { option: TreeOption }) {
  const op: any = option;
  emits('treeChange', option);
}

onMounted(() => {});

defineOptions({ name: 'comTree' });
</script>

<style module lang="scss">
.icon {
  color: #c0c4cc;
}
</style>
