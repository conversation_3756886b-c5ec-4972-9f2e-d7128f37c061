import { api } from '@/api';
import { IObj } from '@/types';
import { $http } from '@tanzerfe/http';
import type { IDetail } from './type';

// 获取详情
export function getDetail(id: string) {
  const url = api.getUrl(api.type.demo, api.name.demo.jurisdictionDetail, {
    id,
  });
  return $http.get<IDetail>(url, { data: { _cfg: { showTip: true } } });
}

// 更新
export function postUpdate(data: { id: string; jurisdiction: string }) {
  const url = api.getUrl(api.type.demo, api.name.demo.jurisdictionUpdate);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...data },
  });
}
// 获取树形结构
export function getOrgTrees(query: IObj<any>) {
  const url = api.getUrl(api.type.server, api.name.interface.getTreeData, query);
  return $http.get(url, { data: { _cfg: { showTip: false } } });
}
// 获取树结构列表
export function getTreeDataPerson(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getTreeDataPerson);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}

// 获取安委会列表
export function getSafeTyData(data: object) {
  const url = api.getUrl(api.type.server, api.name.interface.getSafeTyList, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...data },
  });
}
// 新增安委会
export function addSafyData(params: object) {
  const url = api.getUrl(api.type.server, api.name.interface.addSafeTyList);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}
// 安委会详情
export function detailSafyData(id: any) {
  const url = api.getUrl(api.type.server, api.name.interface.detailSafeTyList, {
    id,
  });
  return $http.post<IDetail>(url, { data: { _cfg: { showTip: true } } });
}
// 获取安委会人员列表
export function getMemberData(committeeId: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getMemberList, {
    committeeId,
  });
  return $http.post<IDetail>(url, { data: { _cfg: { showTip: true } } });
}
// 编辑安委会
export function editSafyData(params: object) {
  const url = api.getUrl(api.type.server, api.name.interface.editSafeTyList);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}
// 获取机构列表

export function getSafyData(params: object) {
  const url = api.getUrl(api.type.server, api.name.interface.getCommitteeList, params);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}

export function delSafety(id: { id: string }) {
  const url = api.getUrl(api.type.server, api.name.interface.delSafetyPerson, {
    id,
  });
  return $http.delete<any>(url, {
    data: { _cfg: { showTip: true, showOkTip: false } },
  });
}
// 删除安委会
export function delCommittee(id: { id: string }) {
  const url = api.getUrl(api.type.server, api.name.interface.deleteCommitteeById, {
    id,
  });
  return $http.post<any>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}

// 配置管理-新增
export function addConfig(params: object) {
  const url = api.getUrl(api.type.server, api.name.interface.addConfig);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}

// 配置管理-获取列表
export function getConfigList(params: object) {
  const url = api.getUrl(api.type.server, api.name.interface.getConfigList, params);
  return $http.get(url, {
    data: { _cfg: { showTip: true, showOkTip: false } },
  });
}
// 配置管理-获取详情
export function getConfigDetail(id: string) {
  const url = api.getUrl(api.type.server, api.name.interface.getConfigDetail, {
    id,
  });
  return $http.get(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
// 配置管理-编辑
export function editConfig(params: object) {
  const url = api.getUrl(api.type.server, api.name.interface.editConfig);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...params },
  });
}

// 配置管理-删除
export function delConfig(id: string) {
  const url = api.getUrl(api.type.server, api.name.interface.delConfig, {
    id,
  });
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
