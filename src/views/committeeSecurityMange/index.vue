<!-- <template>
  <div class="com-g-row-a1">
    <com-bread :data="breadData"></com-bread>
    <div class="com-g-row-a1">
      <filter-comp class="com-table-filter" style="border-radius: 5px 5px 0 0" @action="actionFn" />
      <table-comp
        class="com-table-container"
        style="border-radius: 0 0 5px 5px"
        ref="tableCompRef"
        @action="actionFn"
      />
      <AsideComp ref="asideRef" v-model:show="isShowAside" :title="actionLabel" :getData="getData" />
    </div>
  </div>
</template> -->
<template>
  <div class="">
    <com-bread :data="breadData"></com-bread>
    <!--  h-[calc(100%-86px)] -->
    <div class="flex h-[calc(100%-75px)]">
      <transition name="slide-fade">
        <div class="h-[calc(100%+30px)]" v-show="formVisible">
          <comTree
            :dataList="treeData"
            @treeChange="treeChange"
            @action="actionFn"
          ></comTree>
        </div>
      </transition>
      <div class="!ml-[15px]" style="position: relative">
        <img
          @click="formVisible = !formVisible"
          src="@/assets/open.png"
          style="
            position: absolute;
            left: -1%;
            top: 50%;
            width: 30px;
            cursor: pointer;
          "
        />
        <!-- <RadioTab :tabList="tabList" :tab="curTab" @change="handleChange" /> -->
        <div
          style="
            background-color: #eef7ff;
            display: flex;
            justify-content: space-between;
          "
        >
          <span></span>
          <button
            style="margin-right: 24px"
            class="btn"
            @click="
              actionFn({
                action: curTab === '1' ? ACTION.ADD : ACTION.ADD2,
                data: {},
              })
            "
          >
            新增
          </button>
        </div>
        <table-comp
          v-if="curTab === '1'"
          class="com-table-container h-[calc(100%-34px)]"
          style="border-radius: 0 0 5px 5px"
          ref="tableCompRef"
          :treeId="treeId"
          @action="actionFn"
        />
        <!-- </div> -->
        <AsideComp
          ref="asideRef"
          v-model:show="isShowAside"
          :title="actionLabel"
          :getData="getData"
          :unitId="treeId"
          :levelCode="treeLevelCode"
        />
      </div>
    </div>
    <n-drawer
      v-model:show="isShowAside1"
      class="models !w-[550px]"
      :autoFocus="false"
    >
      <n-drawer-content closable>
        <template #header>
          <div class="flex flex-row items-center">
            <img
              class="w-[17px] h-[12px] mr-[20px]"
              src="@/components/header/assets/icon-title-arrow3.png"
            />
            <div class="text-[16px] text-[#222222] font-bold">
              {{ actionLabel }}
            </div>
          </div>
        </template>
        <n-form
          ref="formRef"
          :model="modelValue"
          :rules="rules"
          label-placement="left"
          label-width="100px"
          require-mark-placement="right-hanging"
          :style="{
            maxWidth: '640px',
          }"
        >
          <n-form-item label="安委会级别" path="levelName">
            <n-input
              v-model:value="modelValue.levelName"
              placeholder="请输入"
              maxlength="20"
              show-count
            />
          </n-form-item>
        </n-form>
        <template #footer>
          <div class="flex justify-end items-center">
            <n-button @click="isShowAside1 = false">取消</n-button>
            <n-button
              type="primary"
              @click="handleValidateClick"
              style="margin-left: 10px"
            >
              保存
            </n-button>
          </div>
        </template>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script lang="ts" setup>
import { useAutoLoading } from '@/common/hooks/useAutoLoading';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { useStore } from '@/store';
import { computed, nextTick, onMounted, provide, Ref, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import AsideComp from './comp/aside/index.vue';
import TableComp from './comp/table/Table.vue';
import comTree from './comTree.vue';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from './constant';
import {
  addConfig,
  delCommittee,
  editConfig,
  getConfigDetail,
  getOrgTrees,
} from './fetchData';
import type { IActionData } from './type';

const router = useRouter();
const route = useRoute();
const store = useStore();
const asideRef: any = ref(null);
// const breadData: IBreadData[] = [{ name: '配置管理' }, { name: '管辖范围配置' }];
const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);
const isShowAside = ref(false);
const tableCompRef = ref();
const tableCompRef1 = ref();
const breadData: any[] = [{ name: '安委会管理' }];
const tabList = [
  { name: '1', label: '安委会管理' },
  { name: '2', label: '配置' },
];

const curTab = ref<any>(route.query?.tab || '1');
function handleChange(name: number) {
  curTab.value = name;
  // 添加nextTick确保组件已经渲染
  nextTick(() => {
    if (treeId.value && treeLevelCode.value) {
      handleSearch({ unitId: treeId.value, levelCode: treeLevelCode.value });
    } else {
      // 如果没有选中树节点，直接获取数据
      handleSearch();
    }
  });
  router.push({
    path: '/committeeSecuitymange',
    query: {
      tab: name,
    },
  });
}
const isShowAside1 = ref(false);
// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);
// 获取安委会配置数据
function getEditFormData(id: string) {
  console.log(id, 'id');
  // 调用接口获取数据
  search(getConfigDetail(id)).then((res: any) => {
    if (res.code != 200) return;
    modelValue.value = res.data;
  });
}
const formVisible = ref(true);
function actionFn(val: IActionData) {
  currentAction.value = val;

  if (val.action === ACTION.SEARCH) {
    handleSearch(val.data);
  } else if (val.action === ACTION.EDIT) {
    isShowAside.value = val.action === ACTION.EDIT;
    asideRef.value?.getEditFormData(val.data.id);
  } else if (val.action === ACTION.EDIT2) {
    isShowAside1.value = val.action === ACTION.EDIT2;
    getEditFormData(val.data.id);
  } else if (val.action === ACTION.DELET) {
    handleDelete(val.data.id);
  } else {
    if (curTab.value == '1') {
      isShowAside.value = val.action === ACTION.ADD;
    } else {
      modelValue.value = {
        levelName: '',
      };
      isShowAside1.value = val.action === ACTION.ADD2;
    }
  }
}
// 搜索
function handleSearch(data?: Record<string, any>) {
  if (data) {
    if (curTab.value == '1') {
      tableCompRef.value?.getTableDataWrap(data);
    } else {
      tableCompRef1.value?.getTableDataWrap(data);
    }
  } else {
    if (curTab.value == '1') {
      tableCompRef.value?.getTableData();
    } else {
      tableCompRef1.value?.getTableData();
    }
  }
}
const getData = () => {
  tableCompRef.value?.getTableData();
};
const treeData = ref<any[]>([]);
const [loading, search] = useAutoLoading(true);
const unitId = ref('10000'); // 登录的orgcode
// 递归函数，使用map生成新的数组
function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };

    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }

    return newItem; // 返回新对象
  });
}
onMounted(() => {
  QueryOrgTrees();
});
//获取树结构数据
function QueryOrgTrees() {
  const params = {
    orgCode: store.userInfo.unitId,
    needChildUnit: '1',
    needself: '1',
  };
  search(getOrgTrees(params)).then((res: any) => {
    if (res.code != 200) return;
    const _RES: any = removeEmptyChildren(res.data);
    treeData.value = _RES;
  });
}
const treeLevelCode: any = ref(null);
const treeId = ref('');
const treeChange = (v: any) => {
  console.log(v, 'v');
  treeLevelCode.value = v.levelCode;
  treeId.value = v.id;
  handleSearch({ unitId: v.id, levelCode: v.levelCode });
};

const formRef = ref();
const modelValue = ref({ levelName: '' });
const rules: any = {
  levelName: [
    {
      required: true,
      message: '请输入安委会级别',
      trigger: ['blur'],
    },
  ],
};
// 新增 编辑配置
function handleValidateClick() {
  console.log(modelValue.value);
  formRef.value?.validate((error: boolean) => {
    if (!error) {
      let params = {
        levelName: modelValue.value.levelName,
        id: '',
      };
      // 判断是新增还是编辑
      if (currentAction.value.action === ACTION.ADD2) {
        // 调用新增接口
        search(addConfig(params)).then((res: any) => {
          if (res.code != 200) return;
          tableCompRef1.value?.getTableData();
          isShowAside1.value = false;
          $toast.success('新增成功');
        });
      } else if (currentAction.value.action === ACTION.EDIT2) {
        // 编辑操作，需要添加id
        params = {
          ...params,
          id: currentAction.value.data.id,
        };
        // 调用编辑接口
        search(editConfig(params)).then((res: any) => {
          if (res.code != 200) return;
          tableCompRef1.value?.getTableData();
          isShowAside1.value = false;
        });
      }
    } else {
      console.log('验证失败');
    }
  });
}

// 删除
function handleDelete(row: any) {
  console.log('row', row);
  $dialog.error({
    title: '删除',
    content: '确定删除吗?',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      search(delCommittee(row)).then((res: any) => {
        if (res.code != 200) return;
        tableCompRef.value?.getTableData();
      });
    },
  });
}
defineOptions({ name: 'CommitteeSecurityMangeIndex' });
</script>

<style scoped lang="scss">
.com-table-container {
  padding: 0 24px 16px;
}

.btn {
  margin-left: 88%;
  margin-top: 15px;
  margin-bottom: 15px;
  width: 88px;
  height: 34px;
  background-color: #3e62eb;
  color: #fff;
  border-radius: 4px;
}
.btn:hover {
  background-color: #6889f7;
}
</style>
