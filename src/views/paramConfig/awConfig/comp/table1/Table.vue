<template>
  <div class="box">
    <n-data-table
      class="h-full com-table"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :pagination="pagination"
      :loading="loading"
      :render-cell="useEmptyCell"
      :theme-overrides="themeOverrides"
    />
  </div>
</template>

<script lang="ts" setup>
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { useStore } from '@/store';
import { IObj } from '@/types';
import { DataTableColumns, NButton } from 'naive-ui';
import { h, ref, toRaw, VNode } from 'vue';
import { cols } from '../../comp/table1/columns';
import { ACTION } from '../../constant';
import { delConfig, getConfigList } from '../../fetchData';
import type { IPageData } from '../../type';

const store = useStore();

const emits = defineEmits(['action']);
const props = defineProps({
  treeId: {
    type: String,
    default: '',
  },
});
const themeOverrides = {
  tdColorStriped: '#dfeefc',
  // tdColorHover: 'rgba(18, 83, 123, 0.35)',
  thColor: '#BBCCF3',
  thTextColor: '#222',
  tdColorStripedModal: 'red',
  // tdColorHoverModal: 'rgba(18, 83, 123, 0.35)',
  // tdColorHoverPopover: 'rgba(18, 83, 123, 0.35)',
};
const [loading, search] = useAutoLoading(true);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IPageData[]>([]);

let filterData: IObj<any> = {}; // 搜索条件
const { pagination, updateTotal } = useNaivePagination(getTableData);
function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };
  console.log(params, 'params=======');
  search(getConfigList(params)).then((res: any) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}
function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

function setColumns() {
  columns.value.push(...cols);

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 290,
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-edit-button',
          onClick: () => emits('action', { action: ACTION.EDIT2, data: toRaw(row) }),
        },
        { default: () => '编辑' }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-del-button',
          onClick: () => handleDelete(row),
        },
        { default: () => '删除' }
      ),
    ],
  ];

  return useActionDivider(acList);
}

// 删除
function handleDelete(row: any) {
  console.log('row', row);
  $dialog.error({
    title: '删除',
    content: '确定删除吗?',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      search(delConfig(row.id)).then((res: any) => {
        if (res.code != 200) return;
        if (tableData.value.length - 1 == 0) {
          pagination.page = pagination.page - 1;
        }
        getTableData();
      });
    },
  });
}
// on created
setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
  pagination,
});

defineOptions({ name: 'CommitteeSecurityMangeTable1' });
</script>

<style module lang="scss"></style>
