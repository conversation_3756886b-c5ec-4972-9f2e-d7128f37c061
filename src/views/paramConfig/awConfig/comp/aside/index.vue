<template>
  <ComDrawerA
    :autoFocus="false"
    :footerPaddingBottom="25"
    :maskClosable="false"
    :show-action="isEdit || isADD"
    @handle-negative="handleClose"
    @handle-positive="handleSubmit"
    class="!w-[550px]"
  >
    <Edit
      ref="editRef"
      v-if="isEdit"
      :handleClose="handleClose"
      :getDataa="getData"
      :id="id"
      @submitted="handleSubmitted"
      :unitId="unitId"
      :levelCode="levelCode"
    />
    <Add
      ref="addRef"
      :getDataa="getData"
      v-if="isADD"
      @submitted="handleSubmitted"
      :handleClose="handleClose"
      :unitId="unitId"
      :levelCode="levelCode"
    />
  </ComDrawerA>
</template>

<script lang="ts" setup>
import ComDrawerA from '@/components/drawer/ComDrawerA.vue';
import { computed, inject, Ref, ref } from 'vue';
import { ACTION, PROVIDE_KEY } from '../../constant';
import type { IActionData } from '../../type';
import Add from './Add.vue';
import Edit from './Edit.vue';
const props: any = defineProps({
  getData: {
    type: Function,
  },
  unitId: {
    type: String,
    default: '',
  },
  levelCode: {
    type: String,
  },
});
const emits = defineEmits(['action', 'getData']);
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);
const isADD = computed(() => currentAction.value.action === ACTION.ADD);
const editRef = ref();
const addRef = ref();
function handleSubmit() {
  if (currentAction.value.action === ACTION.ADD) {
    addRef.value?.handleSubmit();
  } else {
    editRef.value?.handleSubmit();
  }
}
const id = ref('');
// 获取修改表单详情
const getEditFormData = async (idd: string) => {
  // let res = await detailSafyData(idd);
  id.value = idd;
};
function handleSubmitted() {
  emits('action', { action: ACTION.SEARCH });
  handleClose();
}

function handleClose() {
  emits('update:show' as any, false); // any-屏蔽组件内透传的emit类型
}
defineExpose({
  getEditFormData,
});
defineOptions({ name: 'CommitteeSecurityMangeAside' });
</script>

<style module lang="scss"></style>
