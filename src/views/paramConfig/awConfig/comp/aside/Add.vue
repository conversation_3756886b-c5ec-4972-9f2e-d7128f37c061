<template>
  <div :class="$style.wrap">
    <n-spin :show="loading" :delay="500">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="120"
        require-mark-placement="left"
      >
        <n-form-item label="安委会名称" path="name">
          <n-input v-model:value="formData.name" show-count clearable maxlength="30" placeholder="请输入安委会名称" />
        </n-form-item>
        <n-form-item label="安委会级别" path="safetyCommitteeLevelId">
          <n-select
            placeholder="请选择安委会级别"
            v-model:value="formData.safetyCommitteeLevelId"
            :options="categoryOpt"
            label-field="levelName"
            value-field="id"
          />
        </n-form-item>
        <n-form-item label="负责单位/部门" ref="deptRef" path="dept">
          <n-input
            v-model:value="formData.dept"
            readonly="true"
            type="input"
            @click="xuan2"
            placeholder="请从组织架构选择单位或部门"
          />
          <n-button style="margin-left: 10px" @click="xuan2()">选择</n-button>
        </n-form-item>
        <n-form-item label="联系电话" style="position: relative">
          <n-input
            v-model:value="formData.phone"
            @input="validatePhoneNumber"
            :status="phoneStatus"
            placeholder="请输入联系电话"
          />
          <div v-if="phoneError" style="position: absolute; top: 100%; color: #f56c6c">
            {{ phoneError }}
          </div>
          <div v-if="!phoneError && formData.phone" style="color: green; margin-top: 5px"></div>
        </n-form-item>

        <n-form-item label="简介">
          <n-input
            v-model:value="formData.profile"
            type="textarea"
            show-count
            placeholder="请输入安委会简介"
            maxlength="200"
          />
        </n-form-item>
        <n-form-item style="margin-left: 16%">
          <file-upload
            accept=".docx, .pdf,.doc"
            :data="fileData"
            @update="handleUpdate"
            :max="3"
            :size="30"
          ></file-upload>
        </n-form-item>
        <n-form-item>
          <template #label> <span style="color: red">*</span> 安委会成员 </template>
          <n-button type="primary" style="margin-left: 300px" @click="xuan">添加成员</n-button>
        </n-form-item>
        <div>
          <n-data-table
            class="h-full"
            :data="formData.members"
            :columns="columns"
            size="small"
            :max-height="180"
            :render-cell="useEmptyCell"
          ></n-data-table>
        </div>
      </n-form>
    </n-spin>
    <!-- <selectUser
      ref="selectUserRef"
      :getPersonManageData="getTreeDataPerson"
      :sure="true"
      v-model:showModal="isShowAside"
      @success="getData1"
      @close="isShowAside = false"
    ></selectUser> -->
    <selectUser
      ref="selectUserRef"
      :parentOrgCode="orgCode"
      @getPersonManageData="getTreeDataPerson1"
      v-model:show="isShowAside"
      @close="isShowAside = false"
      :userConfigFeid="true"
      @selectUserData="handleAdd"
    >
    </selectUser>
    <selectTree :dataList="treeData" v-model:showModal="isShowAside2" @success="getData2" @close="isShowAside2 = false">
    </selectTree>
  </div>
</template>

<script setup lang="ts">
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import selectUser from '@/components/select-user/Select-user.vue';
import { FileUpload } from '@/components/upload';
import { IUploadRes } from '@/components/upload/type';
import mittBus from '@/utils/mittBus';
import { DataTableColumns, FormInst, FormRules, NButton, useMessage } from 'naive-ui';
import { h, onMounted, ref, VNode } from 'vue';
import { ACTION_LABEL } from '../../constant';
import { addSafyData, getConfigList, getOrgTrees, getTreeDataPerson } from '../../fetchData';
import selectTree from '../select-tree.vue';
import { cols } from './columns';

import { useStore } from '@/store';
const selectUserRef: any = ref(null);
const deptRef: any = ref(null);
const store = useStore();
const message = useMessage();
const props = defineProps({
  getDataa: {
    type: Function,
  },
  handleClose: {
    type: Function,
  },
  unitId: {
    type: String,
    default: '',
  },
  levelCode: {
    type: String,
    default: '',
  },
});
const treeData = ref([]);
const formRef = ref<FormInst | null>();
const [loading, search] = useAutoLoading(false);
const isShowAside = ref(false);
const isShowAside2 = ref(false);
const fileData = ref<any[]>([]);
const columns = ref<DataTableColumns>([]);
// 总部、直管公司、基层单位

const categoryOpt: any = ref([]);
const formData: any = ref({
  members: [],
  dept: '',
  deptId: '',
  attachments: [],
});
const close = () => {};
// const rules: FormRules = {
//   name: [{ required: true, message: '请输入', trigger: ['blur', 'input'] }],
//   dept: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],

//   safetyCommitteeLevelId: [
//     {
//       required: true,
//       message: '请选择',
//       trigger: ['blur', 'input'],
//     },
//   ],
//   chengyuan: [{ required: true, message: '请选择', trigger: ['blur', 'input'] }],
// };
const rules: FormRules = {
  name: [{ required: true, message: '请输入安委会名称', trigger: ['blur', 'input'] }],
  safetyCommitteeLevelId: [
    {
      required: true,
      message: '请选择安委会级别',
      trigger: ['blur', 'input'],
    },
  ],

  dept: [
    {
      required: true,
      message: '请选择负责单位/部门',
      trigger: ['blur', 'input'],
    },
  ],
};
// 获取安委会级别列表
function getSecurityCommissionType(unitId: string, levelCode: string) {
  let params = {
    pageNo: -1,
    pageSize: 1000,
    unitId,
    levelCode,
  };
  search(getConfigList(params)).then((res: any) => {
    if (res.code != 200) return;
    console.log(res, 'res');
    categoryOpt.value = res.data.rows;
  });
}
// 递归函数，使用map生成新的数组
function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };

    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }

    return newItem; // 返回新对象
  });
}
const orgCode = ref(store.userInfo.unitId);
async function getTreeDataPerson1(params: any) {
  let res = await getTreeDataPerson({ ...params, type: 1 });

  selectUserRef.value.renderTable(res);
}
//获取树结构数据
function QueryOrgTrees() {
  const params = {
    orgCode: store.userInfo.unitId,
    needChildUnit: '1',
    needself: '1',
  };
  search(getOrgTrees(params)).then((res: any) => {
    if (res.code != 200) return;
    const _RES: any = removeEmptyChildren(res.data);
    treeData.value = _RES;
  });
}
onMounted(() => {
  if (props.unitId && props.levelCode) {
    getSecurityCommissionType(props.unitId, props.levelCode);
  }
});
const phoneError = ref('');
const phoneStatus = ref('default');
// 手机号正则校验
const validatePhoneNumber = () => {
  const phoneRegex = /^(1[3-9]\d{9}|0\d{2,3}-?\d{7,8})$/; // 正则表达式，10到15位数字
  if (!formData.value.phone) {
    phoneError.value = '';
    phoneStatus.value = 'default';
    return;
  }
  if (!phoneRegex.test(formData.value.phone)) {
    phoneError.value = '请填写正确的联系电话';
    phoneStatus.value = 'error';
  } else {
    phoneError.value = '';
    phoneStatus.value = 'success';
  }
};
const xuan = () => {
  isShowAside.value = true;
  selectUserRef.value.getList(formData.value.members);
  // selectUserRef.value.getTreeAndTable();
};
// 打开选人弹框
const xuan2 = () => {
  isShowAside2.value = true;
  QueryOrgTrees();
};
const handleAdd = (arr: any) => {
  formData.value.members = arr;
};
const getData2 = (str: string, id: string) => {
  console.log('str', str);
  formData.value.dept = str;
  formData.value.deptId = id;
  isShowAside2.value = false;
  deptRef.value.restoreValidation();
};
const emits = defineEmits(['action', 'submitted']);

function setColumns() {
  columns.value.push(...cols);

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    minWidth: 100,
    align: 'center',
    fixed: 'right',
    render(row) {
      return getActionBtn(row);
    },
  });
}
function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-del-button-small',
          onClick: () =>
            // emits('action', { action: ACTION.DELET, data: toRaw(row) }),
            handleDelete(row),
        },
        { default: () => ACTION_LABEL.DELET }
      ),
    ],
  ];

  return useActionDivider(acList);
}
setColumns();

function handleSubmit() {
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      if (phoneStatus.value == 'error') {
        return;
      } else if (formData.value.members.length > 0) {
        formData.value.members = formData.value.members.map((item: any) => {
          return {
            unit: item.unitName,
            dept: item.deptName,
            name: item.userName,
            phone: item.userTelphone,
            post: item.postName,
            userId: item.id,
            ...item,
          };
        });
        let res: any = await addSafyData(formData.value);
        if (res.code == 200) {
          mittBus.emit('addUpdate', 1);
          props.getDataa();
          props.handleClose();
          message.success('新增成功');
        }
      } else {
        message.error('未选中安委会成员');
      }
    } else {
      return;
    }
  });
}
// 上传回调
function handleUpdate(res: IUploadRes[]) {
  console.log(res, 'edit');
  if (!res || !res.length) return;

  const result = res.map((item: any) => {
    return { address: item };
  });
  formData.value.attachments = result;
}
// 移除
function handleDelete(row: any) {
  $dialog.error({
    title: '移除',
    content: '确定移除吗',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: () => {
      formData.value.members = formData.value.members.filter((item: any) => {
        if (item.id !== row.id) {
          return item;
        }
      });
      message.success('移除成功');
    },
  });
}
// init
// getData();

defineExpose({
  handleSubmit,
  getSecurityCommissionType,
});

defineOptions({ name: 'CommitteeSecurityMangeAdd' });
</script>

<style module lang="scss">
:deep(.h-full) {
  width: 100%;

  .n-data-table-th {
    font-size: 12px;
    color: red;
  }
}

.wrap {
  padding: 24px;
}

.checkbox-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  row-gap: 20px;
  padding: 0 10px;
  margin: 20px 0 30px;
  min-height: 107px;
}

.red {
  color: #a30014;
}
</style>
