<template>
  <div :class="$style.wrap">
    <n-spin :show="loading" :delay="500">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="120"
        require-mark-placement="left"
      >
        <n-form-item label="安委会名称" path="name">
          <n-input v-model:value="formData.name" clearable maxlength="30" placeholder="请输入安委会名称" show-count />
        </n-form-item>
        <n-form-item label="安委会级别" path="safetyCommitteeLevelId">
          <n-select
            placeholder="请选择安委会级别"
            v-model:value="formData.safetyCommitteeLevelId"
            :options="categoryOpt"
            label-field="levelName"
            value-field="id"
          />
        </n-form-item>
        <n-form-item label="负责单位/部门" path="dept">
          <n-input
            v-model:value="formData.dept"
            readonly="true"
            type="input"
            placeholder="请从组织架构选择单位或部门"
            @click="xuan2"
          />
          <n-button @click="xuan2()" style="margin-left: 10px">选择</n-button>
        </n-form-item>
        <n-form-item label="联系电话" style="position: relative">
          <n-input
            v-model:value="formData.phone"
            @input="validatePhoneNumber"
            :status="phoneStatus"
            placeholder="请输入联系电话"
          />
          <div v-if="phoneError" style="position: absolute; top: 100%; color: #f56c6c">
            {{ phoneError }}
          </div>
          <div v-if="!phoneError && formData.phone" style="color: green; margin-top: 5px"></div>
        </n-form-item>
        <n-form-item label="简介">
          <n-input
            v-model:value="formData.profile"
            type="textarea"
            placeholder="请输入安委会简介"
            maxlength="200"
            show-count
          />
        </n-form-item>
        <n-form-item style="margin-left: 16%">
          <file-upload
            accept=".docx, .pdf,.doc"
            :data="fileData"
            @update="handleUpdate"
            @removee="handleRemove"
            :max="3"
            :size="30"
          ></file-upload>
        </n-form-item>
        <n-form-item>
          <template #label> <span style="color: red">*</span> 安委会成员 </template>
          <n-button type="primary" style="margin-left: 300px" @click="xuan">添加成员</n-button>
        </n-form-item>
        <div>
          <n-data-table
            class="h-full"
            :data="dataList"
            :columns="columns"
            size="small"
            :max-height="180"
            :render-cell="useEmptyCell"
          ></n-data-table>
        </div>
      </n-form>
    </n-spin>
    <!-- <selectUser
      ref="selectUserRef"
      v-model:showModal="isShowAside"
      @success="getD1"
      @close="isShowAside = false"
    ></selectUser> -->
    <selectUser
      ref="selectUserRef"
      :parentOrgCode="orgCode"
      @getPersonManageData="getTreeDataPerson1"
      v-model:show="isShowAside"
      :title="actionLabel"
      @close="isShowAside = false"
      :userConfigFeid="true"
      @selectUserData="handleAdd"
    ></selectUser>
    <selectTree :dataList="treeData" v-model:showModal="isShowAside2" @success="getD2" @close="isShowAside2 = false">
    </selectTree>
  </div>
</template>

<script setup lang="ts">
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import selectUser from '@/components/select-user/Select-user.vue';
import { FileUpload } from '@/components/upload';
import { IUploadRes } from '@/components/upload/type';
import { useStore } from '@/store';
import { DataTableColumns, FormInst, FormRules, NButton, useMessage } from 'naive-ui';
import { computed, h, inject, onMounted, ref, Ref, VNode } from 'vue';
import { ACTION_LABEL, PROVIDE_KEY } from '../../constant';
import {
  detailSafyData,
  editSafyData,
  getConfigList,
  getMemberData,
  getOrgTrees,
  getTreeDataPerson,
} from '../../fetchData';
import type { IActionData } from '../../type';
import selectTree from '../select-tree.vue';
import { cols } from './columns';
const store = useStore();
const message = useMessage();
const [loading, run] = useAutoLoading(false);
const [loading2, search] = useAutoLoading(false);
const formData: any = ref({
  members: [],
  dept: '',
  attachments: [],
});
const titleValue = ref('添加附件');
const selectUserRef: any = ref(null);
const isShowAside = ref(false);
const isShowAside2 = ref(false);
const treeData = ref([]);
const rules: FormRules = {
  name: [{ required: true, message: '请输入安委会名称', trigger: ['blur', 'input'] }],
  safetyCommitteeLevelId: [
    {
      required: true,
      message: '请选择安委会级别',
      trigger: ['blur', 'input'],
    },
  ],

  dept: [
    {
      required: true,
      message: '请选择负责单位/部门',
      trigger: ['blur', 'input'],
    },
  ],
};
const dataList = ref([]);
const emits = defineEmits(['action', 'submitted']);
const columns = ref<DataTableColumns>([]);
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const actionData = computed(() => currentAction.value.data);
const categoryOpt: any = ref([]);
const fileData = ref<any[]>([]);
const props: any = defineProps({
  id: {
    type: String,
  },
  getDataa: {
    type: Function,
  },
  handleClose: {
    type: Function,
  },
  unitId: {
    type: String,
  },
  levelCode: {
    type: String,
  },
});
const orgCode = ref(store.userInfo.unitId);
async function getTreeDataPerson1(params: any) {
  let res = await getTreeDataPerson({ ...params, type: 1 });

  selectUserRef.value.renderTable(res);
}
// 获取详情
const idd: any = ref('');
function getDetailData() {
  run(detailSafyData(props.id)).then((res: any) => {
    if (res.code == 200) {
      formData.value = res.data;

      const file = res.data.attachments.map((item: any) => {
        const fileName = item.address.split('/').pop();
        return {
          ...item,
          name: fileName || '',
        };
      });
      fileData.value = res.data.attachments.map((item: any) => {
        const fileName = item.address.split('/').pop();
        console.log(fileName, 'filename');
        return {
          ...item,
          name: fileName || '',
        };
      });
      idd.value = res.data.id;
    }
  });
  run(getMemberData(props.id)).then((res: any) => {
    // console.log(res);
    if (res.code == 200) {
      res.data = res.data.map((item: any) => {
        return {
          ...item,
          userTelphone: item.phone,
          deptName: item.dept,
          postName: item.post,
          unitName: item.unit,
        };
      });
      dataList.value = res.data;
    } else {
      return;
    }
  });
}
const phoneError = ref('');
const phoneStatus = ref('default');
// 手机号正则校验
const validatePhoneNumber = () => {
  const phoneRegex = /^(1[3-9]\d{9}|0\d{2,3}-?\d{7,8})$/; // 正则表达式，10到15位数字
  if (!formData.value.phone) {
    phoneError.value = '';
    phoneStatus.value = 'default';
    return;
  }
  if (!phoneRegex.test(formData.value.phone)) {
    phoneError.value = '请填写正确的联系电话';
    phoneStatus.value = 'error';
  } else {
    phoneError.value = '';
    phoneStatus.value = 'success';
  }
};
const handleAdd = (arr: any) => {
  dataList.value = arr;
};
const getD2 = (str: string) => {
  formData.value.dept = str;
};
// 打开选人弹框
const xuan = () => {
  isShowAside.value = true;
  selectUserRef.value.getList(dataList.value);
};
// 打开选人弹框
const xuan2 = () => {
  isShowAside2.value = true;
  QueryOrgTrees();
};
// 递归函数，使用map生成新的数组
function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };

    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }

    return newItem; // 返回新对象
  });
}
//获取树结构数据
function QueryOrgTrees() {
  const params = {
    orgCode: store.userInfo.unitId,
    needChildUnit: '1',
    needself: '1',
  };
  search(getOrgTrees(params)).then((res: any) => {
    if (res.code != 200) return;
    const _RES: any = removeEmptyChildren(res.data);
    treeData.value = _RES;
  });
}
// 获取安委会级别列表
function getSecurityCommissionType(unitId: string, levelCode: string) {
  let params = {
    pageNo: -1,
    pageSize: 1000,
    unitId,
    levelCode,
  };
  search(getConfigList(params)).then((res: any) => {
    if (res.code != 200) return;
    console.log(res, 'res');
    categoryOpt.value = res.data.rows;
  });
}

const formRef = ref<FormInst | null>();
function handleSubmit() {
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      if (phoneStatus.value == 'error') {
        return;
      } else if (dataList.value.length > 0) {
        formData.value.members = dataList.value.map((item: any) => {
          return {
            unit: item.unitName,
            dept: item.deptName,
            name: item.userName,
            phone: item.userTelphone,
            post: item.postName,
            userId: item.id,
            ...item,
          };
        });
        let res: any = await editSafyData(formData.value);
        if (res.code == 200) {
          // mittBus.emit('addUpdate', 1);
          props.getDataa();
          props.handleClose();
          message.success('编辑成功');
        }
      } else {
        message.error('未选中安委会成员');
      }
    } else {
      return;
    }
  });
}
// 上传回调
function handleUpdate(res: IUploadRes[]) {
  if (!res || !res.length) return;

  const result = res.filter((item) => item !== null).map((item) => ({ address: item }));

  // 使用 concat 拼接到 attachments
  formData.value.attachments = formData.value.attachments.concat(result);

  // 过滤掉空的附件
  formData.value.attachments = formData.value.attachments.filter(
    (item) => item && item.address // 这里检查 item 是否有效
  );
  const uniqueAddresses = new Set();
  formData.value.attachments = formData.value.attachments.filter((item) => {
    const isDuplicate = uniqueAddresses.has(item.address);
    uniqueAddresses.add(item.address);
    return !isDuplicate; // 返回 false 以删除重复项
  });
}
// 删除回调
function handleRemove(data: any) {
  formData.value.attachments = formData.value.attachments.filter((item: any) => {
    if (item.id !== data.id) {
      return item;
    }
  });
}
// 移除
function handleDelete(row: any) {
  $dialog.error({
    title: '移除',
    content: '确定移除吗',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      // console.log(row);
      dataList.value = dataList.value.filter((item: any) => {
        if (item.id !== row.id) {
          return item;
        }
      });
      message.success('移除成功');
    },
  });
}
function setColumns() {
  columns.value.push(...cols);

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    minWidth: 100,
    align: 'center',
    fixed: 'right',
    render(row) {
      return getActionBtn(row);
    },
  });
}
function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-del-button-small',
          onClick: () =>
            // emits('action', { action: ACTION.DELET, data: toRaw(row) }),
            handleDelete(row),
        },
        { default: () => ACTION_LABEL.DELET }
      ),
    ],
  ];

  return useActionDivider(acList);
}
// init
setColumns();
defineExpose({
  handleSubmit,
});
onMounted(() => {
  // QueryOrgTrees();
  getDetailData();
  if (props.unitId && props.levelCode) {
    getSecurityCommissionType(props.unitId, props.levelCode);
  }
});
defineOptions({ name: 'CommitteeSecurityMangeEdit' });
</script>

<style module lang="scss">
.wrap {
  padding: 24px;
}

.checkbox-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  row-gap: 20px;
  padding: 0 10px;
  margin: 20px 0 30px;
  min-height: 107px;
}

.red {
  color: #a30014;
}
</style>
