<template>
  <div class="">
    <com-bread :data="breadData"></com-bread>
    <!-- h-[calc(100%-86px)] -->
    <div class="flex h-[calc(100%)]">
      <div style="position: relative">
        <div
          style="
            background-color: #eef7ff;
            display: flex;
            justify-content: space-between;
          "
        >
          <span></span>
          <button
            style="margin-right: 24px"
            class="btn"
            @click="actionFn({ action: ACTION.ADD2, data: {} })"
          >
            新增
          </button>
        </div>
        <TableComp1
          class="com-table-container h-[calc(100%-68px)]"
          style="border-radius: 0 0 5px 5px"
          ref="tableCompRef1"
          :treeId="treeId"
          @action="actionFn"
        />
      </div>
    </div>
    <n-drawer
      v-model:show="isShowAside1"
      class="models !w-[550px]"
      :autoFocus="false"
    >
      <n-drawer-content closable>
        <template #header>
          <div class="flex flex-row items-center">
            <img
              class="w-[17px] h-[12px] mr-[20px]"
              src="@/components/header/assets/icon-title-arrow3.png"
            />
            <div class="text-[16px] text-[#222222] font-bold">
              {{ actionLabel }}
            </div>
          </div>
        </template>
        <n-form
          ref="formRef"
          :model="modelValue"
          :rules="rules"
          label-placement="left"
          label-width="100px"
          require-mark-placement="right-hanging"
          :style="{
            maxWidth: '640px',
          }"
        >
          <n-form-item label="安委会级别" path="levelName">
            <n-input
              v-model:value="modelValue.levelName"
              placeholder="请输入"
              maxlength="20"
              show-count
            />
          </n-form-item>
        </n-form>
        <template #footer>
          <div class="flex justify-end items-center">
            <n-button @click="isShowAside1 = false">取消</n-button>
            <n-button
              type="primary"
              @click="handleValidateClick"
              style="margin-left: 10px"
            >
              保存
            </n-button>
          </div>
        </template>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script lang="ts" setup>
import { useAutoLoading } from '@/common/hooks/useAutoLoading';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { useStore } from '@/store';
import { computed, onMounted, provide, Ref, ref } from 'vue';
import TableComp1 from './comp/table1/Table.vue';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from './constant';
import {
  addConfig,
  delConfig,
  editConfig,
  getConfigDetail,
  getOrgTrees,
} from './fetchData';
import type { IActionData } from './type';

const store = useStore();
const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);
const tableCompRef = ref();
const tableCompRef1 = ref();
const breadData: any[] = [{ name: '参数配置' }, { name: '安委会级别配置' }];

const isShowAside1 = ref(false);
// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);
// 获取安委会配置数据
function getEditFormData(id: string) {
  console.log(id, 'id');
  // 调用接口获取数据
  search(getConfigDetail(id)).then((res: any) => {
    if (res.code != 200) return;
    modelValue.value = res.data;
  });
}
function actionFn(val: IActionData) {
  currentAction.value = val;

  if (val.action === ACTION.SEARCH) {
    handleSearch(val.data);
  } else if (val.action === ACTION.EDIT2) {
    isShowAside1.value = val.action === ACTION.EDIT2;
    getEditFormData(val.data.id);
  } else if (val.action === ACTION.DELET) {
    handleDelete(val.data.id);
  } else {
    modelValue.value = {
      levelName: '',
    };
    isShowAside1.value = val.action === ACTION.ADD2;
  }
}
// 搜索
function handleSearch(data?: Record<string, any>) {
  if (data) {
    tableCompRef1.value?.getTableDataWrap(data);
  } else {
    tableCompRef1.value?.getTableData();
  }
}
const getData = () => {
  tableCompRef1.value?.getTableData();
};
const treeData = ref<any[]>([]);
const [loading, search] = useAutoLoading(true);
// 递归函数，使用map生成新的数组
function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };

    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }

    return newItem; // 返回新对象
  });
}
onMounted(() => {
  getData();
});
//获取树结构数据
function QueryOrgTrees() {
  const params = {
    orgCode: store.userInfo.unitId,
    needChildUnit: '1',
    needself: '1',
  };
  search(getOrgTrees(params)).then((res: any) => {
    if (res.code != 200) return;
    const _RES: any = removeEmptyChildren(res.data);
    treeData.value = _RES;
  });
}
const treeId = ref('');

const formRef = ref();
const modelValue = ref({ levelName: '' });
const rules: any = {
  levelName: [
    {
      required: true,
      message: '请输入安委会级别',
      trigger: ['blur'],
    },
  ],
};
// 新增 编辑配置
function handleValidateClick() {
  console.log(modelValue.value);
  formRef.value?.validate((error: boolean) => {
    if (!error) {
      let params = {
        levelName: modelValue.value.levelName,
        id: '',
      };
      // 判断是新增还是编辑
      if (currentAction.value.action === ACTION.ADD2) {
        // 调用新增接口
        search(addConfig(params)).then((res: any) => {
          if (res.code != 200) return;
          tableCompRef1.value?.getTableData();
          isShowAside1.value = false;
          $toast.success('新增成功');
        });
      } else if (currentAction.value.action === ACTION.EDIT2) {
        // 编辑操作，需要添加id
        params = {
          ...params,
          id: currentAction.value.data.id,
        };
        // 调用编辑接口
        search(editConfig(params)).then((res: any) => {
          if (res.code != 200) return;
          tableCompRef1.value?.getTableData();
          isShowAside1.value = false;
        });
      }
    } else {
      console.log('验证失败');
    }
  });
}

// 删除
function handleDelete(row: any) {
  console.log('row', row);
  $dialog.error({
    title: '删除',
    content: '确定删除吗?',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      search(delConfig(row)).then((res: any) => {
        if (res.code != 200) return;
        console.log('222', res);
        if (res.data.total <= 10) {
          tableCompRef1.value.pagination.page =
            tableCompRef1.value?.pagination.page - 1;
          tableCompRef1.value?.getTableDataWrap({});
        } else {
          tableCompRef1.value?.getTableData();
        }
      });
    },
  });
}
defineOptions({ name: 'CommitteeSecurityMangeIndex' });
</script>

<style scoped lang="scss">
.com-table-container {
  padding: 0 24px 16px;
}

.btn {
  margin-left: 88%;
  margin-top: 15px;
  margin-bottom: 15px;
  width: 88px;
  height: 34px;
  background-color: #3e62eb;
  color: #fff;
  border-radius: 4px;
}

.btn:hover {
  background-color: #6889f7;
}
</style>
