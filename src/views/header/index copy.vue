<template>
  <header class="com-header" :class="$style['header']">
    <div :class="$style['area']">
      <!--  left  -->
      <div class="flex" :class="$style['area-l']" style="padding-left: 0">
        <!-- {{ userStore.zhPlatformUrl }} -->
        <img
          class="logo"
          :src="logo"
          style="cursor: pointer"
          alt=""
          @click="toWork"
        />
        <span
          :class="$style['title']"
          style="
            margin-left: 5px;
            font-family: MyCustomFont, sans-serif;
            font-weight: 400;
            font-size: 30px;
            color: #ffffff;
            text-shadow: 0px 2px 8px rgba(41, 47, 58, 0.2016);
            font-style: normal;
            text-transform: none;
            cursor: pointer;
          "
          @click="toWork"
        >
          {{ store.userInfo.zhName }}
        </span>
      </div>

      <!--  right  -->
      <div :class="$style['area-r']">
        <avatar />
      </div>
    </div>
  </header>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useStore } from '@/store';
import Avatar from './comp/Avatar.vue';
import YangChang from '@/assets/iconImg/yanchang.png';
import AnGang from '@/assets/iconImg/angang.png';
import WyCh from '@/assets/iconImg/wych.png';
const store = useStore();
const logo = ref(store.userInfo.logoPicUrl);
// logo.value = store.userInfo.zhLogo === 'yanchang' ? YangChang : AnGang;
// if (store.userInfo.zhLogo === 'yanchang') {
//   logo.value = YangChang;
// } else if (store.userInfo.zhLogo === 'changhang') {
//   logo.value = WyCh;
// } else {
//   logo.value = AnGang;
// }
const toWork = () => {
  window.open(store.userInfo.zhPlatformUrl);
};
defineOptions({ name: 'MisHeaderComp' });
</script>
<style scoped>
.logo {
  width: 40px;
  height: 40px;
}
</style>
<style module lang="scss">
.header {
  position: relative;
  overflow: hidden;
  background: #16213a;
}

.area {
  position: relative;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr auto;
  justify-content: space-between;
  align-items: center;
  z-index: 2;
}

.area-l {
  position: relative;
  font-size: 24px;
  padding-left: 45px;
}

.area-r {
  display: grid;
  grid-template-columns: auto 1fr;
  // column-gap: 20px;
}

.title {
  margin-left: 5px;
  font-family: MyCustomFont, sans-serif;
  font-weight: 400;
  font-size: 30px;
  color: #ffffff;
  text-shadow: 0px 2px 8px rgba(41, 47, 58, 0.2016);
  font-style: normal;
  text-transform: none;
}
</style>
