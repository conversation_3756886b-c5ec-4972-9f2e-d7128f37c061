<template>
  <header class="com-header" :class="$style['header']">
    <div :class="$style['area']">
      <div class="flex" :class="$style['area-l']" style="padding-left: 0">
        <img
          @click="toWork"
          class="logo"
          :src="logo"
          alt=""
          style="cursor: pointer; margin-right: 10px; margin-top: 12px"
        />
        <span
          @click="toWork"
          class="titleTop"
          style="
            font-family: MyCustomFont, sans-serif;
            font-weight: 400;
            font-size: 30px;
            color: #fff;

            text-shadow: 0px 2px 8px rgba(41, 47, 58, 0.2016);

            cursor: pointer;
          "
        >
          {{ store.userInfo.zhName }}
        </span>
      </div>
      <div :class="$style['area-r']">
        <avatar />
      </div>
    </div>
  </header>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useStore } from '@/store';
import Avatar from './comp/Avatar.vue';
import Yang<PERSON>hang from '@/assets/iconImg/yanchang.png';
import AnGang from '@/assets/iconImg/angang.png';
import WyCh from '@/assets/iconImg/wych2.png';
import Zhongdanong from '@/assets/iconImg/zhongdanong.png';
const store = useStore();
const logo = ref(store.userInfo.logoPicUrl);
// logo.value = store.userInfo.zhLogo === 'yanchang' ? YangChang : AnGang;
// if (store.userInfo.zhLogo === 'yanchang') {
//   logo.value = YangChang;
// } else if (store.userInfo.zhLogo === 'changhang') {
//   logo.value = WyCh;
// } else if (store.userInfo.zhLogo === 'zhongdanong') {
//   logo.value = Zhongdanong;
// } else if (store.userInfo.zhLogo === 'angang') {
//   logo.value = AnGang;
// } else {
//   logo.value = store.userInfo.logoPicUrl + `${store.userInfo.zhLogo}.png`;
// }

const toWork = () => {
  window.open(store.userInfo.zhPlatformUrl);
};
defineOptions({ name: 'MisHeaderComp' });
</script>
<style scoped>
.logo {
  width: 40px;
  height: 40px;
}
</style>
<style module lang="scss">
@font-face {
  font-family: 'MyCustomFont';
  src: url('@/assets/font/YouSheBiaoTiHei-Bold.TTF') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
.header {
  position: relative;
  overflow: hidden;
  background: #16213a;
}

.area {
  position: relative;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr auto;
  justify-content: space-between;
  align-items: center;
  z-index: 2;
}

.area-l {
  position: relative;
  font-size: 24px;
  padding-left: 45px;
}

.area-r {
  display: grid;

  grid-template-columns: auto 1fr;
  column-gap: 10px;
}

.titleTop {
}
</style>
