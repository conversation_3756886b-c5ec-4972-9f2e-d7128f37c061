<template>
  <n-modal
    v-model:show="showModal"
    title="导入员工证书"
    style="width: 600px"
    @negative-click="onNegativeClick"
    preset="card"
  >
    <n-form
      ref="formRef"
      :model="model"
      :rules="rules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
      :style="{
        maxWidth: '820px',
      }"
    >
      <n-form-item label="导入文件" ref="importFielRef" :title="model.importFiel" path="importFiel">
        <n-upload
          @change="uploadChange"
          :on-before-upload="handleBeforeUpload"
          :show-file-list="false"
          accept=".excel,.XLSX,.XLS"
          action=""
          style="width: 100px"
        >
          <n-input v-model:value="model.importFiel" placeholder="请上传导入文件" readonly clearable />
        </n-upload>
      </n-form-item>
      <n-form-item label="" path="">
        <n-upload
          @change="uploadChange"
          :on-before-upload="handleBeforeUpload"
          :show-file-list="false"
          accept=".excel,.XLSX,.XLS"
          action=""
          style="width: 100px"
        >
          <n-button type="primary">上传Excel</n-button>
        </n-upload>
        <!-- <n-button type="primary" style="margin-right: 20px"> 上传Excel</n-button> -->
        <n-button quaternary type="info" @click="DownloadTemplateClick"> 下载模版</n-button>
      </n-form-item>
      <n-form-item label="导入设置" path="importSetting">
        <n-radio-group v-model:value="model.importSetting" size="medium">
          <n-radio value="0"> 遇错继续 </n-radio>
          <n-radio value="1"> 遇错停止 </n-radio>
        </n-radio-group>
      </n-form-item>
    </n-form>
    <template #footer>
      <div style="display: flex; justify-content: flex-end">
        <n-button style="margin-right: 20px" type="info" @click="onNegativeClick"> 关闭 </n-button>
        <n-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submit"> 确定 </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { codeOrg } from '@/utils/levelCodeList';
import { UploadFileInfo } from 'naive-ui';
import { getApiBase } from '@/utils/gaitSite';
import { useUpload } from '@/common/hooks/useUpload.ts';
import { downloadTemplate, importCertificate } from './fetchList';
import { fileDownloader } from '@/utils/fileDownloader';
import { $toast } from '@/common/shareContext/useToastCtx.ts';

import { ref, defineProps, computed, watch, onMounted } from 'vue';

const emits = defineEmits(['closeModal', 'updateList']);
const importFielRef: any = ref(null);
const formRef = ref<any>(null);
const submitLoading = ref(false);
const model = ref<any>({
  importFiel: '',
  importSetting: '0',
});

const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  getTableList: {
    type: Function,
    default: () => {},
  },
});

const showModal = computed({
  get: () => props.showModal,
  set: (val) => {
    model.value.importFiel = '';
    emits('closeModal', false);
  },
});

const rules = {
  importFiel: {
    required: true,
    trigger: ['blur', 'input'],
    message: '文件不能为空',
  },
};

function submit(e: MouseEvent) {
  // console.log(model.value, 'model');
  // e.preventDefault();
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      // console.log(files.value,codeOrg.value,model.value.importSetting)
      const formData = new FormData();
      formData.append('importExcelFile', files.value);
      formData.append('orgCode ', codeOrg.value);
      formData.append('importSetUp ', model.value.importSetting);
      submitLoading.value = true;
      uploadFn(formData)
        .then((res: any) => {
          if (res.code == 200) {
            $toast.success(`导入成功`);
            props.getTableList();
            model.value.importFiel = '';
            submitLoading.value = false;
            emits('closeModal', false);
          }
        })
        .catch((error: any) => {
          submitLoading.value = false;
          try {
            // 尝试解析错误字符串为JSON对象

            const parsedError = JSON.parse(error.message);
            console.log(parsedError);
            $toast.error(parsedError.message);
            // 处理解析后的错误信息
            if (parsedError.code) {
              console.error(`错误代码: ${parsedError.code}`);
              console.error(`错误信息: ${parsedError.message}`);
            }
          } catch (e) {
            console.error('解析错误信息时出错:', e);
            console.error('原始错误信息:', error);
          }
        });
    }
  });
}

function onNegativeClick() {
  model.value.importFiel = '';
  emits('closeModal', false);
}

// 下载模板
async function DownloadTemplateClick() {
  const res = await downloadTemplate();
  fileDownloader(`${window.$SYS_CFG.apiBase}${res.data}`);

  console.log(res.data, 'res');
}

const [uploadFn, uploading, uploadProgress] = useUpload(
  `${window.$SYS_CFG.apiBase}/api/v3/bw-clnt-org-person-service/certificate/importCertificate`
);

const files = ref();
async function uploadChange(list: any) {
  files.value = list.file.file; //获取文件
  model.value.importFiel = files.value.name;
  importFielRef.value.restoreValidation();
}

// 上传前校验(文件大小、类型)
function handleBeforeUpload(options: { file: UploadFileInfo }) {
  const { file } = options;
  if (!file.file) return false;
  if (5 && file.file.size / 1024 / 1024 > 5) {
    $toast.error(`文件大小不能超过5MB`);
    return false;
  }
  return true;
}
watch(
  () => props.showModal,
  (newVal) => {}
);
</script>
<style scoped lang="scss">
.n-input {
  width: 400px !important;
}
</style>
