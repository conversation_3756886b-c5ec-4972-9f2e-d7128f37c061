import type { IPageDataRes } from './type';
import { $http } from '@tanzerfe/http';
import { api } from '@/api';

//获取模板
export function downloadTemplate() {
  const url = api.getUrl(api.type.server, api.name.interface.downloadTemplate);
  return $http.get(url, { data: { _cfg: { showTip: false } } });
}

//导入证书信息
export function importCertificate(query: any) {
  const url = api.getUrl(api.type.server, api.name.interface.importCertificate, query);
  return $http.post(url, { data: { _cfg: { showTip: false } } });
}
