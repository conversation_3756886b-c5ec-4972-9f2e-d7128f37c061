<template>
  <div class="">
    <!-- <com-bread :data="breadData"></com-bread> -->
    <!-- <div class="header" style="margin-top: 10px; margin-bottom: 15px; margin-left: 5px">证书管理</div> -->
    <com-bread :data="breadData"></com-bread>
    <!-- h-[calc(100%-46px)] -->
    <div class="flex h-full">
      <transition name="slide-fade">
        <div class="h-[calc(100%)]" v-show="formVisible">
          <com-tree
            v-if="curTab == '1'"
            :title="titlee"
            :dataList="treeData"
            @treeChange="treeChange"
          ></com-tree>
        </div>
      </transition>

      <div
        :class="
          curTab == '1' && formVisible && !isInIfm
            ? 'w-[calc(100vw-630px)]'
            : curTab == '1' && formVisible && isInIfm
              ? 'w-[calc(100vw-385px)]'
              : curTab == '1' && !formVisible && !isInIfm
                ? 'w-[calc(84vw)]'
                : curTab == '1' && !formVisible && isInIfm
                  ? 'w-[calc(96vw)]'
                  : curTab == '2' && formVisible && !isInIfm
                    ? 'w-[calc(100vw)]'
                    : 'w-[calc(100vw)]'
        "
        class="!ml-[15px]"
        style="position: relative"
      >
        <img
          v-if="curTab == '1'"
          @click="formVisible = !formVisible"
          src="@/assets/open.png"
          style="
            position: absolute;
            left: -1%;
            top: 50%;
            width: 30px;
            cursor: pointer;
          "
        />
        <RadioTab :tabList="tabList" :tab="curTab" @change="handleChange" />
        <table-comp
          v-if="curTab === '1'"
          class="com-table-container h-[calc(95%)]"
          ref="tableCompRef"
          @getParams="getParams"
          :code="levelCode"
          @action="actionFn"
          :treeId="codeOrg"
        />
        <certificateConfig
          class="com-table-container h-[calc(100%)]"
          v-if="curTab === '2'"
          ref="tableCompRefs"
          style="padding: 24px 0"
        />
      </div>
    </div>
    <!-- 新增编辑 -->
    <AsideComp
      :listDetail="listDetail"
      v-model:show="isShowAside"
      :title="actionLabel"
      @action="actionFn"
    />
    <!-- 证书复审 -->
    <certificateReviewModal
      :reviewDetail="reviewDetail"
      :showReview="showReview"
      @close="showReview = false"
      :getTableList="getTableList"
    ></certificateReviewModal>
    <!-- 导入 -->
    <importModal
      :getTableList="getTableList"
      :showModal="showModal"
      @closeModal="showModal = false"
    />
  </div>
</template>
<script lang="ts" setup>
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import RadioTab from '@/components/tab/ComRadioTabA.vue';
import comTree from '@/components/tree/comTree.vue';
import { useStore } from '@/store';
import { fileDownloader } from '@/utils/fileDownloader';
import { codeOrg, levelCode } from '@/utils/levelCodeList';
import { getOrgTrees } from '@/views/personManage/fetchData.ts';
import { computed, onMounted, provide, Ref, ref } from 'vue';
import certificateConfig from './certificateConfig/index.vue';

import AsideComp from './comp/aside/index.vue';
import certificateReviewModal from './comp/certificateReviewModal.vue';
import TableComp from './comp/table/Table.vue';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from './constant';
import { deleteData } from './fetchData';
import importModal from './importModal.vue';
import type { IActionData, ICheckTempRow } from './type';
const store = useStore();
const formVisible = ref(true);
const isInIfm = window.__IFM_ENV__;
// const titlee = ref('人员管理');
const breadData: any[] = ref([{ name: '奖惩记录' }, { name: '证书清单' }]);
const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);
const isShowAside = ref(false);
const showReview = ref(false);
const tableCompRef = ref();
const tableCompRefs = ref();
const curTab = ref('1');
const showModal = ref(false);
const treeData: any = ref([]);
const listDetail = ref();
// 复审记录
const reviewDetail = ref({});
const tabList = [
  { name: '1', label: '证书清单' },
  { name: '2', label: '证书配置' },
];
// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

onMounted(() => {
  QueryOrgTrees();
});

function handleChange(value: string) {
  console.log(value);
  curTab.value = value;
  levelCode.value = codee.value;
  codeOrg.value = firstId.value;
  if (value === '1') {
    breadData.value = [{ name: '证书管理' }, { name: '证书清单' }];
  } else {
    breadData.value = [{ name: '证书管理' }, { name: '证书配置' }];
  }
}
function actionFn(val: IActionData) {
  currentAction.value = val;
  // QueryOrgTrees();

  if (val.action === ACTION.SEARCH) {
    handleSearch(val.data);
  }
  if (val.action === ACTION.ADD || val.action === ACTION.EDIT) {
    listDetail.value = val.data;
    isShowAside.value = true;
  }
  if (val.action === ACTION.DELETE) {
  }
  if (val.action === ACTION.EXPORT) {
    return exportExcel('1');
  }
  if (val.action === ACTION.IMPORT) {
    showModal.value = true;
  }
  if (val.action === ACTION.REVIEW) {
    showReview.value = true;
    reviewDetail.value = val.data;
  }
}
function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };

    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }
    return newItem; // 返回新对象
  });
}

const codee = ref('');
const firstId = ref('');
const newObj: any = ref({});
const getParams = (obj: any) => {
  Object.keys(obj).forEach((key) => {
    if (obj[key] === null) {
      obj[key] = '';
    }
  });
  newObj.value = obj;
};
function QueryOrgTrees() {
  const params = {
    orgCode: store.userInfo.unitId,
    needChildUnit: '1',
    needself: '1',
  };

  getOrgTrees(params).then((res: any) => {
    if (res.code != 200) return;
    treeData.value = removeEmptyChildren(res.data);
    console.log(treeData.value, 'treeData.value');
    const data = {
      levelCode: treeData.value[0].levelCode,
    };

    levelCode.value = treeData.value[0].levelCode;
    codee.value = treeData.value[0].levelCode;
    firstId.value = treeData.value[0].id;
    // tableCompRef.value?.getTableDataWrap(data);
    tableCompRef.value?.getTableData(treeData.value[0].levelCode);
    if (res.data.length && codeOrg.value == '') {
      codeOrg.value = res.data[0].id;
    }
  });
}

const treeChange = (v: any, id: any) => {
  levelCode.value = v;
  codeOrg.value = id;
  const data = {
    levelCode: v,
  };
  if (curTab.value === '1') {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRefs.value?.handleSearch(data);
  }
};

// 导出
function exportExcel(e: string) {
  fileDownloader(
    `${window.$SYS_CFG.apiBaseURL}/bw-clnt-org-person-service/certificate/exportCertificateList?levelCode=${levelCode.value}&zhId=${store.userInfo.zhId}&certificateTypeId=${newObj.value.certificateTypeId}&certificateStatus=${newObj.value.certificateStatus}&isReview=${newObj.value.isReview}&name=${newObj.value.name}`
  );
}
function handleSearch(data?: Record<string, any>) {
  if (data) {
    if (curTab.value === '1') {
      tableCompRef.value?.getTableDataWrap(data);
    } else {
      tableCompRefs.value?.handleSearch(data);
    }
  } else {
    tableCompRef.value?.getTableData(codee.value);
  }
}
const getTableList = () => {
  tableCompRef.value?.getTableData(levelCode.value);
};
function handleDelete(data: ICheckTempRow) {
  $dialog.error({
    title: '删除',
    content: `确定删除？`,
    positiveText: '确定',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      deleteData({
        id: data.id,
        status: 1,
      }).then(() => {
        handleSearch();
      });
    },
  });
}

defineOptions({ name: 'certificateManagementIndex' });
</script>
<style module lang="scss"></style>
