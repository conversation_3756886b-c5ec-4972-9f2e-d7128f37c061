<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-20 14:17:46
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-20 22:53:26
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/views/certificateManagement/comp/certificateReviewModal.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <n-modal :show="props.showReview">
    <n-card
      id="card"
      title="证书复审"
      class="top1"
      :bordered="false"
      size="huge"
      preset="dialog"
      role="dialog"
      aria-modal="true"
    >
      <template #header-extra>
        <n-icon size="24" @click="cancel" style="cursor: pointer"> × </n-icon>
      </template>
      <div class="content">
        <n-card title="证书信息" :bordered="false">
          <div class="text1">
            <div class="">
              证书编号：
              {{ detailData.code || '--' }}
            </div>
            <div class="">
              证书名称：
              {{ detailData.name || '--' }}
            </div>
            <div class="">
              证书类型：
              {{ detailData.typeName || '--' }}
            </div>
          </div>
          <div class="text2">
            <div>
              员工：
              {{ detailData.staffName }}
            </div>
            <div class="">
              发证日期：
              {{ detailData.issueTime }}
            </div>
          </div>
        </n-card>
        <div class="mt-10">
          <n-card title="复审记录" :bordered="false">
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px">
              <div>
                <n-input v-model:value="namee" @input="getTabData" placeholder="请输入记录人进行模糊查询"></n-input>
              </div>
              <div>
                <n-button type="primary" @click="open" style="margin-right: 10px">新增</n-button>
              </div>
            </div>
            <!-- 新增复审弹窗 -->
            <n-modal v-model:show="showModalAdd">
              <n-card
                class="card3"
                :bordered="false"
                title="新增复审记录"
                size="huge"
                role="dialog"
                aria-modal="true"
                style="width: 600px; position: fixed; left: 35%; margin: 0 auto"
              >
                <template #header-extra>
                  <n-icon size="24" @click="closee" style="cursor: pointer"> × </n-icon>
                </template>
                <n-form
                  style="border: 1px solid #eee; padding: 15px; border-radius: 5px"
                  ref="formRef"
                  :model="formData"
                  :rules="rules"
                  label-placement="left"
                  require-mark-placement="left"
                >
                  <n-form-item label="最近复审日期:" path="latestReviewTime">
                    <n-date-picker
                      v-model:value="formData.latestReviewTime"
                      value-format="yyyy-MM-dd"
                      type="date"
                      clearable
                      placeholder=" 请选择最近复审日期"
                    />
                  </n-form-item>
                  <n-form-item label="下次复审日期:" path="nextReviewTime">
                    <n-date-picker
                      v-model:value="formData.nextReviewTime"
                      type="date"
                      clearable
                      placeholder=" 请选择下次复审日期"
                    />
                  </n-form-item>
                </n-form>
                <template #footer>
                  <n-space>
                    <n-button strong @click="closee">关闭</n-button>
                    <n-button type="primary" @click="confirm">保存</n-button>
                  </n-space>
                </template>
              </n-card>
            </n-modal>
            <n-data-table
              style="height: 250px; overflow-y: scroll"
              remote
              striped
              :columns="columns"
              :data="tableData"
              :pagination="pagination"
              :loading="loading"
              :render-cell="useEmptyCell"
              :theme-overrides="{
                thColor: '#BBCCF3',
                thTextColor: '#222',
              }"
            />
          </n-card>
        </div>
      </div>
      <template #footer>
        <n-space style="margin-left: 95%">
          <n-button strong @click="cancel">关闭</n-button>
        </n-space>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { ref, h, onMounted, watch, reactive, VNode } from 'vue';
import { CaCloseOutline as CloseOutline } from '@kalimahapps/vue-icons';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { certificateReviewList, deleteCertificateReviewRecord, addCertificateReviewRecord } from '../fetchData.ts';
import { NButton, FormInst, FormRules, useMessage } from 'naive-ui';
import type { ICategoryRes } from '../type';
import dayjs from 'dayjs';
const message = useMessage();
const { pagination, updateTotal } = useNaivePagination(getTabData);
import { timestampToDate } from '@/lib/storages/_utils.ts';
pagination.pageSlot = 5;
const showModalAdd = ref(false);
const emits = defineEmits(['close', 'toDelete']);
const [loading, search] = useAutoLoading(true);
const props = defineProps({
  showReview: {
    type: Boolean,
    default: false,
  },
  reviewDetail: {
    type: Object,
    default: () => ({}),
  },
  getTableList: {
    type: Function,
    required: true,
  },
});

const formRef = ref<FormInst | null>();
const formData = ref<ICategoryRes>({
  latestReviewTime: null,
  nextReviewTime: null,
});

const rules: FormRules = {
  latestReviewTime: { required: true, message: '请选择最近复审日期' },
  nextReviewTime: { required: true, message: '请选择下次复审日期' },
};

// #region 表格
interface Song {
  id: number;
  latestReviewTime: string;
  nextReviewTime: string;
  recorderName: string;
  updateTime: string;
}
const columns = <any[]>[
  {
    title: '序号',
    key: 'index',
    width: 60,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '最近复审日期',
    key: 'latestReviewTime',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '下次复审日期',
    key: 'nextReviewTime',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '记录人',
    key: 'recorderName',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    width: 200,
    title: '记录时间',
    key: 'updateTime',
    ellipsis: {
      tooltip: true,
    },
  },
];
const tableData = ref<Song[]>([]);
const detailData = ref<any>({});
const closee = () => {
  showModalAdd.value = false;
};
const open = () => {
  showModalAdd.value = true;

  formData.value = {};
};

function setColumns() {
  // 添加操作栏 action
  columns.push({
    title: '操作',
    key: 'actions',
    width: 290,
    fixed: 'right',
    align: 'center',
    render(row: any) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-del-button',
          onClick: () => handelDetele(row),
        },
        { default: () => '删除' }
      ),
    ],
  ];

  return useActionDivider(acList);
}
const namee = ref('');
// 获取列表

function getTabData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    certificateId: detailData.value.id,
    recorderName: namee.value,
  };
  console.log('🚀 ~ getTabData ~ params:', params);
  certificateReviewList(params).then((res: any) => {
    console.log('🚀 ~ getTabData ~ res:', res);
    if (res.code != 'success') return;
    loading.value = false;
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

// 删除
function handelDetele(row: any) {
  $dialog.error({
    title: '删除',
    content: '确定删除吗?',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      let res: any = await deleteCertificateReviewRecord({
        id: row.id,
        certificateId: row.certificateId,
      });
      if (res.code == 'success') {
        if (tableData.value.length - 1 == 0) {
          pagination.page = 1;
        }
        getTabData();
      }
    },
  });
}

// 新增复审记录
function confirm() {
  console.log('🚀 ~ run ~ formData.value:-累到晕厥', formData.value);
  formData.value.certificateId = detailData.value.id;
  const latestReviewTime = timestampToDate(formData.value.latestReviewTime);
  const nextReviewTime = timestampToDate(formData.value.nextReviewTime);
  const params = { ...formData.value, latestReviewTime, nextReviewTime };

  // if(latestReviewTime.valueOf()>nextReviewTime.valueOf()){
  //   return message.error('下次复审日期不能早于最近复审日期');
  // }
  // formData.value.recorderId = detailData.value.createUserId;
  // formData.value.recorderName = detailData.value.createUserName;
  // console.log('formData-----????', formData.value);
  // return;
  formRef.value?.validate((errors) => {
    if (!errors) {
      addCertificateReviewRecord(params).then(() => {
        formData.value = {};
        getTabData();
        showModalAdd.value = false;
      });
    }
  });
}

// 取消
const cancel = () => {
  props.getTableList();
  emits('close');
};
setColumns();

// onMounted(() => {
//   l, ();
// });

watch(
  () => props.showReview,
  (nv) => {
    if (!props.showReview) return;

    if (props.reviewDetail) {
      detailData.value = props.reviewDetail;
      getTabData();
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
:deep(.n-data-table.n-data-table--bordered .n-data-table-wrapper) {
  overflow: auto;
}
.p1 {
  color: #c3cad6;
}
.n-input {
  width: 300px !important;
}
#card {
  width: 1100px;
  height: 800px;
  // background-color: #f8f8f8;

  :deep(.n-card-header .n-card-header__main::before) {
    content: '';
    display: inline-block;
    width: 4px;
    height: 15px;
    margin-right: 5px;
    background-color: #527cff;
  }
  :deep(.n-card-header__main) {
    // background-color: #ecf1ff;

    font-weight: 600;
  }
}

.n-space {
  flex-direction: row-reverse;
  flex-flow: initial;
}
.content {
  // padding-left: 20px;

  height: 100%;
  :deep(.n-card-header .n-card-header__main::before) {
    content: '';
    display: none !important;

    width: 4px;
    margin-right: 5px;
    background-color: #527cff;
  }
  :deep(.n-card-header__main) {
    background-color: #ecf1ff;
    padding-left: 20px;
    height: 50px;
    line-height: 50px;
    color: #587de8;
  }
  div {
    // padding-left: 20px;
    // box-sizing: border-box;
    margin-top: 5px;
  }
  .text1 {
    margin-left: 30px;
  }
  .text2 {
    margin-left: 250px;
  }
}
:deep(.content > .n-card > .n-card__content) {
  width: 70%;
  display: flex;
  // font-weight: 600;
  // justify-content: space-around;
}
</style>
