<template>
  <div :class="$style.wrap">
    <n-spin :show="loading" :delay="500">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="110"
        require-mark-placement="left"
      >
        <n-form-item label="证书名称" path="name">
          <n-input
            v-model:value="formData.name"
            clearable
            maxlength="20"
            show-count
            placeholder="请输入证书名称"
          />
        </n-form-item>
        <n-form-item label="证书编号" path="code">
          <n-input
            v-model:value="formData.code"
            placeholder="请输入证书编号"
            clearable
            maxlength="20"
            show-count
          />
        </n-form-item>
        <n-form-item label="证书类型" path="certificateTypeId">
          <n-select
            v-model:value="formData.certificateTypeId"
            clearable
            placeholder="请选择证书类型"
            :disabled="isEdit"
            @change="changeClick"
            :options="typeOpt"
          />
        </n-form-item>
        <n-form-item label="员工" ref="staffNameRef" path="staffName">
          <n-input
            readonly
            v-model:value="formData.staffName"
            placeholder="请从组织架构选择员工"
            style="width: 85%; margin-right: 10px"
            @click="clickSelect"
          />
          <n-button @click="clickSelect">选择</n-button>
        </n-form-item>
        <n-form-item label="部门" path="deptName">
          <n-input
            v-model:value="formData.deptName"
            placeholder="从组织架构里自动获取"
            disabled
          />
        </n-form-item>
        <n-form-item label="岗位" path="postName">
          <n-input
            v-model:value="formData.postName"
            placeholder="从组织架构里自动获取"
            disabled
          />
        </n-form-item>
        <n-form-item label="发证机构">
          <n-input
            v-model:value="formData.issueOrg"
            placeholder="请输入发证机构"
            maxlength="20"
            show-count
          />
        </n-form-item>
        <n-form-item label="发证日期" path="issueTime">
          <n-date-picker
            v-model:formatted-value="formData.issueTime"
            value-format="yyyy-MM-dd"
            type="date"
            clearable
            placeholder="请选择发证日期"
          />
        </n-form-item>
        <n-form-item label="需要复审" path="isReview">
          <n-radio-group
            v-model:value="formData.isReview"
            disabled
            size="medium"
          >
            <n-radio value="1"> 是 </n-radio>
            <n-radio value="0"> 否 </n-radio>
          </n-radio-group>
        </n-form-item>
        <n-form-item label="有效期" path="effectiveTime">
          <n-date-picker
            v-model:formatted-value="formData.effectiveTime"
            type="daterange"
            clearable
            placeholder="请选择有效期"
          />
        </n-form-item>
        <n-form-item
          label="最近复审日期"
          path="latestReviewTime"
          v-if="formData.isReview == 1"
        >
          <n-date-picker
            v-model:formatted-value="formData.latestReviewTime"
            value-format="yyyy-MM-dd"
            type="date"
            clearable
            placeholder="请选择最近复审日期"
          />
        </n-form-item>
        <n-form-item
          label="下次复审日期"
          path="nextReviewTime"
          v-if="formData.isReview == 1"
        >
          <n-date-picker
            v-model:formatted-value="formData.nextReviewTime"
            value-format="yyyy-MM-dd"
            type="date"
            clearable
            placeholder="请选择下次复审日期"
          />
        </n-form-item>
        <n-form-item style="margin-left: 15%">
          <file-upload3
            :data="fileData"
            :disabled1="fileData != ''"
            @update="handleUpdate"
            :max="1"
            :size="30"
            accept=".png, .jpg"
          ></file-upload3>
          <div style="position: absolute; top: 60px">
            <n-image width="100" :src="getApiBase() + fileData" style="" />
            <span
              style="position: absolute; cursor: pointer; color: #999"
              v-if="fileData != ''"
              @click="
                fileData = '';
                formData.filePath = '';
              "
              >X</span
            >
          </div>
        </n-form-item>
      </n-form>
    </n-spin>

    <selectUser
      @success="getData"
      v-model:showModal="showModal"
      @close="showModal = false"
    ></selectUser>
  </div>
</template>

<script setup lang="ts">
import { codeOrg, levelCode, userTelphone } from '@/utils/levelCodeList';
import selectUser from '../selectUser.vue';
import { FileUpload3 } from '@/components/upload';
import type { IActionData, ICategory } from '../../type';
import { computed, inject, onMounted, ref, Ref, toRaw, watch } from 'vue';
import {
  FormInst,
  FormRules,
  DataTableColumns,
  NButton,
  useMessage,
} from 'naive-ui';
import {
  postUpdate,
  addData,
  getCertificateType,
  getOrgTrees,
} from '../../fetchData';
import { useStore } from '@/store';
import { ACTION, PROVIDE_KEY } from '../../constant';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';

// import { typeOpt } from '../table/columns';
import dayjs from 'dayjs';
import {
  dateStringToTimestamp,
  timestampToDate,
} from '@/lib/storages/_utils.ts';
const showModal = ref(false);
const store = useStore();
const typeOpt = ref<any[]>([]);
const props = defineProps({
  listDetail: {
    type: Object,
    default: () => {},
  },
});
const message = useMessage();
const staffNameRef: any = ref(null);
const treeData = ref([]);
const emits = defineEmits(['submitted']);
const formRef = ref<FormInst | null>();
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const actionData = computed(() => currentAction.value.data);
const [loading, run] = useAutoLoading(true);
const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);

const fileData: any = ref('');
// const effectiveTimeBegin = ref<any[]>([]);
const formData: any = ref({
  certificateName: '',
  effectiveTimeBegin: '',
  effectiveTimeEnd: '',
  code: '',
  typeName: '',
  staffName: '',
  staffId: '',
  deptName: '',
  deptId: '',
  postName: '',
  issueOrg: '',
  issueTime: null,
  isReview: 1,
  effectiveTime: null,
  latestReviewTime: null,
  nextReviewTime: null,
  filePath: '',
  orgCode: codeOrg.value || store.userInfo.unitId,
  levelCode: levelCode.value,
  staffTelphone: '',
  unitId: '',
  unitName: '',
});

const rules: FormRules = {
  // certificateName: { required: true, message: '请输入证书名称',trigger: ['input', 'blur'] },
  name: { required: true, message: '请输入证书名称', trigger: ['blur'] },
  code: { required: true, message: '请输入证书编号', trigger: ['blur'] },
  type: { required: true, message: '请选择', trigger: ['blur', 'change'] },
  staffName: {
    required: true,
    message: '请从组织架构选择人员',
    trigger: ['blur', 'change'],
  },
  certificateTypeId: { required: true, message: '请选择证书类型' },
  // issueOrg: { required: true, message: '请输入发证机构' },
  issueTime: {
    required: true,
    message: '请选择发证日期',
    trigger: ['blur', 'change'],
  },
  effectiveTime: {
    required: true,
    message: '请选择有效期',
    trigger: ['blur', 'change'],
    validator: (a, b) => {
      return !!b;
    },
  },
  // latestReviewTime: { required: true, message: '请选择最近复审日期' },
  nextReviewTime: {
    required: true,
    message: '请选择下次复审日期',
    trigger: ['blur', 'change'],
  },
};

function certificateTypeData() {
  const params = {
    pageNo: 1,
    pageSize: -1,
  };
  getCertificateType(params).then((res: any) => {
    console.log('🚀 ~ run ~ res:', res);
    if (res.code != 200) return;
    const aa = res.data.rows || [];
    typeOpt.value = aa.map((item: any) => {
      var obj = { label: item.name, value: item.id, isReview: item.isReview };
      return obj;
    });
    console.log('🚀 ~ getCertificateType ~  typeOpt.value:', typeOpt.value);
  });
}
// function handleRemove(data: any) {
//   // console.log(data, '>>>>>>>>>');
//   // formData.value.attachments = formData.value.attachments.filter(
//   fileData.value = '';
//   formData.value.filePath = '';
//   //   (item: any) => {
//   //     if (item.id !== data.id) {
//   //       return item;
//   //     }
//   //   }
//   // );
// }
function changeClick(val: any) {
  console.log(val, 'val');
  if (val === null) {
    formData.value.isReview = '1';
  } else {
    const arr: any = typeOpt.value.filter((item) => item.value == val);
    console.log(arr, '>>>>>');
    formData.value.isReview = String(arr[0].isReview);
    console.log(formData.value.isReview, 'formData.value.isReview ');
    formData.value.latestReviewTime = null;
    formData.value.nextReviewTime = null;
  }
}

function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };

    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }
    return newItem; // 返回新对象
  });
}
const unitId = ref('10000');
function QueryOrgTrees() {
  const params = {
    orgCode: codeOrg.value,
    needChildUnit: '1',
    needself: '1',
  };
  getOrgTrees(params).then((res: any) => {
    if (res.code != 200) return;
    const _RES: any = removeEmptyChildren(res.data);
    treeData.value = _RES;
  });
}

onMounted(() => {
  loading.value = false;
  QueryOrgTrees();
});
function getData(str: any) {
  console.log(str, '>>>>>>>>');
  formData.value.unitId = str[0].unitId;
  formData.value.unitName = str[0].unitName;
  // 员工姓名及id
  formData.value.levelCode = str[0].levelCode;
  // console.log(formData.value.levelCode);
  formData.value.staffName = str[0].userName;
  formData.value.staffId = str[0].id;
  // 岗位姓名
  formData.value.postName = str[0].postName;
  formData.value.staffTelphone = str[0].userTelphone;
  // 部门姓名及id
  formData.value.deptId = str[0].deptId;
  formData.value.deptName = str[0].deptName;
  staffNameRef.value.restoreValidation();
}

function clickSelect() {
  showModal.value = true;
}
function handleSubmit() {
  if (formData.value.effectiveTime) {
    (formData.value.effectiveTimeBegin = formData.value.effectiveTime[0]),
      (formData.value.effectiveTimeEnd = formData.value.effectiveTime[1]);
  }
  // console.log('🚀 ~ run ~ formData.value:', userTelphone.value);
  formRef.value?.validate((errors) => {
    if (!errors) {
      if (isEdit.value) {
        formData.value.imageUpdFlag = 1;
        formData.value.orgCode = codeOrg.value;

        run(postUpdate(formData.value)).then(() => {
          emits('submitted');
          message.success('编辑成功');
        });
      } else {
        console.log(formData.value, '999');
        run(addData(formData.value)).then(() => {
          emits('submitted');
          message.success('新增成功');
        });
      }
    }
  });
}

// init
// getData();
certificateTypeData();

defineExpose({
  handleSubmit,
});

watch(
  () => isEdit.value,
  () => {
    if (isEdit.value) {
      // formData.value = { ...props.listDetail } as any;
      // if(props.listDetail.latestReviewTim){

      // }else{

      // }certificateManagementAside
      formData.value = { ...props.listDetail } as any;
      console.log(formData.value, 'formData.value');
      if (props.listDetail.latestReviewTime) {
        formData.value.latestReviewTime = props.listDetail.latestReviewTime;
      } else {
        formData.value.latestReviewTime = null;
      }

      formData.value.nextReviewTime = props.listDetail.nextReviewTime;
      // fileData.value = props.listDetail.filePath;
      formData.value.isReview = String(props.listDetail.isReview);
      fileData.value = props.listDetail.filePath;

      formData.value.issueTime =
        timestampToDate(props.listDetail.issueTime) || '';
      formData.value.effectiveTime = [
        timestampToDate(props.listDetail.effectiveTimeBegin),
        timestampToDate(props.listDetail.effectiveTimeEnd),
      ];
      console.log(formData.value, '...............');

      // console.log(timestampToDate(props.listDetail.effectiveTimeBegin), '33333');
    }
  },
  {
    immediate: true,
  }
);

const handleUpdate = (res: any) => {
  console.log(res, '��� ~ handleUpdate ~ res');
  // if (!res || !res.length) return;
  // const result = res
  //   .filter((item) => item !== null)
  //   .map((item) => ({ address: item }));

  // // 使用 concat 拼接到 attachments
  // formData.value.filePath = formData.value.filePath.concat(result);

  // // 过滤掉空的附件
  // formData.value.filePath = formData.value.filePath.filter(
  //   (item) => item && item.address // 这里检查 item 是否有效
  // );
  // const uniqueAddresses = new Set();
  // formData.value.filePath = formData.value.filePath.filter((item) => {
  //   const isDuplicate = uniqueAddresses.has(item.address);
  //   uniqueAddresses.add(item.address);
  //   return !isDuplicate; // 返回 false 以删除重复项
  // });
  formData.value.filePath = res[0];
};
const addr = window.sessionStorage.getItem('ehs-org-alloc-mgr-address');
function getApiBase() {
  console.log(window.$SYS_CFG.apiPreviewURL + addr + fileData.value);
  return window.$SYS_CFG.apiPreviewURL;
}
defineOptions({ name: 'certificateManagementEdit' });
</script>

<style module lang="scss">
.wrap {
  padding: 24px;
}

.checkbox-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  row-gap: 20px;
  padding: 0 10px;
  margin: 20px 0 30px;
  min-height: 107px;
}

.red {
  color: #a30014;
}

:v-deep(.n-button--primary-type) {
  width: 50px !important;
}
</style>
