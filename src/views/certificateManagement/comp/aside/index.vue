<template>
  <ComDrawerA
    :autoFocus="false"
    :footerPaddingBottom="25"
    :maskClosable="false"
    :show-action="true"
    @handle-negative="handleClose"
    @handle-positive="handleSubmit"
    class="!w-[550px]"
  >
    {{ parentId }}
    <Edit ref="editRef" :listDetail="listDetail" @submitted="handleSubmitted" />
  </ComDrawerA>
</template>

<script lang="ts" setup>
import ComDrawerA from '@/components/drawer/ComDrawerA.vue';
import Edit from './Edit.vue';
import type { IActionData } from '../../type';
import { ACTION, PROVIDE_KEY } from '../../constant';
import { computed, useAttrs, inject, Ref, ref } from 'vue';

defineProps({
  listDetail: {
    type: Object,
    default: () => {},
  },
  parentId: {
    type: String,
  },
});

const emits = defineEmits(['action']);
const attrs = useAttrs();
const show = computed(() => !!attrs.show);
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);
const editRef = ref();

function handleSubmit() {
  editRef.value?.handleSubmit();
}

function handleSubmitted() {
  emits('action', { action: ACTION.SEARCH });
  handleClose();
}

function handleClose() {
  emits('action', { action: ACTION.SEARCH });
  emits('update:show' as any, false); // any-屏蔽组件内透传的emit类型
}

defineOptions({ name: 'certificateManagementAside' });
</script>

<style module lang="scss"></style>
