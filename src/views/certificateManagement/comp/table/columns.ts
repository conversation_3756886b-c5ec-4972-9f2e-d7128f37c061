import { DataTableColumn, NTag } from 'naive-ui';
import { h } from 'vue';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    // fixed: 'left',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '证书类别',
    key: 'typeName',
    align: 'center',
    width: 160,
    // fixed: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '员工姓名',
    key: 'staffName',
    align: 'center',
    // fixed: 'left',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '员工手机号',
    key: 'staffTelphone',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '证书名称',
    key: 'name',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },

  {
    title: '发证日期',
    key: 'issueTime',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '证书有效期',
    key: 'effectiveTimeBegin',
    align: 'center',
    width: 200,
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return `${row.effectiveTimeBegin} - ${row.effectiveTimeEnd}`;
    },
  },
  {
    title: '证书图片',
    key: 'filePath',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
    render: function (data: any) {
      if (data.filePath == '') {
        return '--';
      }
      return h('img', {
        src: `${getApiBase()}${data.filePath}`,
        style: {
          height: '40px',
        },
        onClick: () => {
          // 点击事件逻辑
          alert(`图片路径: ${data.filePath}`);
        },
      });
    },
  },
  {
    title: '部门名称',
    key: 'deptName',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '岗位名称',
    key: 'postName',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '是否需要复审',
    key: 'isReview',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
    render: (e: any) => {
      return e.isReview ? '是' : '否';
    },
  },
  {
    title: '下次复审日期',
    key: 'nextReviewTime',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '最近修改人',
    key: 'updateUserName',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '最近修改时间',
    key: 'updateTime',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '证书状态',
    key: 'certificateStatus',
    align: 'center',
    fixed: 'right',
    width: 160,
    ellipsis: {
      tooltip: false,
    },
    render(e) {
      const status = e.certificateStatus;
      const color = status === 1 ? '#00B578FF' : status === 2 ? 'rgba(250, 81, 81, 1)' : 'rgba(229, 163, 60, 1)';
      const text = status === 1 ? '正常' : status === 2 ? '已过期' : '即将过期';
      return h(
        NTag,
        {
          style: {
            color: 'white',
            background: color,
          },
        },
        () => text
      );
    },
  },
  {
    title: '预警天数',
    key: 'warningDays',
    align: 'center',
    width: 160,
    fixed: 'right',
    ellipsis: {
      tooltip: true,
    },
    render: (e: any) => {
      const status = e.certificateStatus;
      const isWarningDays =
        status == 2 ? '已过期' + e.warningDays + '天' : status > 2 ? '还剩' + e.warningDays + '天' : '--';
      if (status == 1) {
        return '--';
      }
      return h(
        'span',
        {
          style: {
            color: 'rgb(250, 81, 81)',
          },
        },
        isWarningDays
      );
    },
  },
];

function getApiBase() {
  return window.location.hostname == 'localhost' ? 'http://*************:9862' : window.location.origin;
}

export const typeOpt = [
  { label: '全部', value: '' },
  { label: '动火证', value: '1' },
  { label: '焊接证', value: '2' },
];

export const statusOpt = [
  { label: '全部', value: '' },
  { label: '正常', value: '1' },
  { label: '已过期', value: '2' },
  { label: '即将过期', value: '3' },
];

export const reviewOpt = [
  { label: '全部', value: '' },
  { label: '是', value: '1' },
  { label: '否', value: '0' },
];
