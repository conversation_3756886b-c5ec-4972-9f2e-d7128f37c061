<template>
  <div>
    <n-form class="mb-[10px]" :show-feedback="false" label-placement="left">
      <div class="flex justify-between">
        <n-form-item
          label="证书类型:"
          label-placement="left"
          label-width="auto"
        >
          <n-select
            class="!w-[220px]"
            v-model:value="filterForm.certificateTypeId"
            filterable
            clearable
            label-field="name"
            value-field="id"
            placeholder="请选择证书类型"
            :options="typeOpt"
            @update:value="getTableData1"
          />
        </n-form-item>
        <n-form-item
          label="证书状态:"
          label-placement="left"
          label-width="auto"
        >
          <n-select
            class="!w-[150px]"
            style="width: 150px"
            v-model:value="filterForm.certificateStatus"
            filterable
            clearable
            placeholder="请选择证书状态"
            :options="statusOpt"
            @update:value="getTableData1"
          />
        </n-form-item>
        <n-form-item
          label="需要复审:"
          label-placement="left"
          label-width="auto"
        >
          <n-select
            class="!w-[150px]"
            v-model:value="filterForm.isReview"
            filterable
            clearable
            placeholder="请选择需要复审"
            :options="reviewOpt"
            @update:value="getTableData1"
          />
        </n-form-item>
        <n-form-item
          label=""
          label-placement="left"
          label-width="auto"
          style="margin-right: 20px"
        >
          <n-input
            class="!w-[230px]"
            v-model:value="filterForm.name"
            clearable
            placeholder="请输入证书名称/姓名搜索"
            @update:value="getTableData1"
          >
            <template #suffix>
              <n-icon :component="AnOutlinedSearch" />
            </template>
          </n-input>
        </n-form-item>
        <div class="flex justify-end">
          <n-button
            type="primary"
            @click="doHandle(ACTION.ADD)"
            style="margin-right: 10px"
          >
            {{ ACTION_LABEL.ADD }}
          </n-button>
          <n-button
            type="primary"
            @click="doHandle(ACTION.IMPORT)"
            style="margin-right: 10px"
          >
            {{ ACTION_LABEL.IMPORT }}
          </n-button>
          <n-button type="primary" @click="doHandle(ACTION.EXPORT)">
            {{ ACTION_LABEL.EXPORT }}
          </n-button>
        </div>
      </div>
    </n-form>
    <n-data-table
      class="h-[calc(100%-40px)] com-table"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :pagination="pagination"
      :loading="loading"
      :render-cell="useEmptyCell"
      :flex-height="true"
      :bordered="false"
      :scroll-x="3000"
    />
    <!-- <div class="imgBox" v-show="isModalVisible" title="放大图片">
        <div class="header" @click="closee">×</div>
        <img :src="selectedImage" style="height: 700px" />
        <n-image width="100" :src="selectedImage" style="" />
      </div> -->
  </div>
</template>

<script lang="ts" setup>
import { ACTION, ACTION_LABEL } from '../../constant';
import type { IPageData } from '../../type';

import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { useStore } from '@/store';
import { IObj } from '@/types';
import { levelCode } from '@/utils/levelCodeList';
import { AnOutlinedSearch } from '@kalimahapps/vue-icons';
import {
  DataTableColumn,
  DataTableColumns,
  NButton,
  NImage,
  NTag,
  useMessage,
} from 'naive-ui';
import { h, onMounted, reactive, ref, toRaw, VNode, watch } from 'vue';
import { deleteData, getCertificateType, pageData } from '../../fetchData';
import { reviewOpt, statusOpt } from './columns';
import { useRoute, useRouter } from 'vue-router';
const router = useRouter();
const route = useRoute();
const store = useStore();
const message = useMessage();
const emits = defineEmits(['action', 'getParams']);
const props = defineProps({
  code: {
    type: String,
  },
  treeId: {
    type: String,
  },
});
const [loading, search] = useAutoLoading(true);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IPageData[]>([]);
const reviewData = ref({});
const { pagination, updateTotal } = useNaivePagination(getTableData);

const curcertificateStatus = ref<any>(route.query?.certificateStatus || '');
let filterForm = reactive({
  certificateTypeId: '',
  certificateStatus: curcertificateStatus.value,
  isReview: '',
  name: '',
  status: 0,
});

const typeOpt = ref([]);
function getTable() {
  const params = {
    pageNo: pagination.page,
    pageSize: 50,
  };
  search(getCertificateType(params)).then((res: any) => {
    const arr = { name: '全部', id: '' };
    res.data.rows.unshift(arr);
    typeOpt.value = res.data.rows || [];
    // console.log(typeOpt.value, '==========');
    // updateTotal(res.data.total || 0);
  });
}

onMounted(() => {
  // getTableData();
  getTable();
});
const closee = () => {
  isModalVisible.value = false;
};
function getTableData1() {
  pagination.page = 1;
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    levelCode: levelCode.value,
    ...filterForm,
  };
  emits('getParams', params);
  search(pageData(params)).then((res) => {
    console.log(res, 'res');
    if (res.code != 200) return;
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total);
    // console.log(res.data,'==============')
  });
}
// const total = ref()
function getTableData(data?: any) {
  if (data == pagination.page) {
    data = levelCode.value;
  }
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    levelCode: data,
    ...filterForm,
  };
  emits('getParams', params);
  search(pageData(params)).then((res) => {
    if (res.code != 200) return;
    tableData.value = res.data.rows || [];
    console.log(tableData.value, 'tableData.value');
    updateTotal(res.data.total);
    // console.log(res.data,'==============')
  });
}

function getTableDataWrap(data: IObj<any>) {
  console.log(data, '>>>>>>>>>>>>>>>>');
  filterForm = Object.assign(filterForm, data) || {};
  pagination.page = 1;
  getTableData(data.levelCode);
}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: toRaw(reviewData.value),
  });
}
const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    // fixed: 'left',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '证书类型',
    key: 'typeName',
    align: 'center',
    width: 160,
    // fixed: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '员工姓名',
    key: 'staffName',
    align: 'center',
    // fixed: 'left',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '员工手机号',
    key: 'staffTelphone',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '证书名称',
    key: 'name',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '证书编号',
    key: 'code',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '发证日期',
    key: 'issueTime',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '证书有效期',
    key: 'effectiveTimeBegin',
    align: 'center',
    width: 200,
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return `${row.effectiveTimeBegin} - ${row.effectiveTimeEnd}`;
    },
  },
  {
    title: '证书图片',
    key: 'filePath',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
    render: function (data: any) {
      if (!data.filePath) return '--';

      return h(
        'div',
        {
          style: { display: 'inline-flex', alignItems: 'center' },
        },
        [
          h(
            'span',
            { style: { color: '#1890ff', cursor: 'pointer' } },
            '已上传'
          ),
          h(NImage, {
            src: `${window.$SYS_CFG.apiPreviewURL}${data.filePath}`,
            style: { opacity: 0, width: '40px', position: 'absolute' },
            previewSrc: `${window.$SYS_CFG.apiPreviewURL}${data.filePath}`,
          }),
        ]
      );
    },
  },
  {
    title: '部门名称',
    key: 'deptName',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '岗位名称',
    key: 'postName',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '是否需要复审',
    key: 'isReview',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
    render: (e: any) => {
      return e.isReview ? '是' : '否';
    },
  },
  {
    title: '下次复审日期',
    key: 'nextReviewTime',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      return row.certificateStatus == '2'
        ? h('div', [
            h(
              'p',
              {
                style: {
                  color: 'red',
                },
              },
              row.nextReviewTime // 这里直接传入字符串
            ),
            h(
              'p',
              {
                style: {
                  color: 'red',
                  textAlign: 'center',
                },
              },
              '(已逾期)' // 这里可以是另一个字符串或内容
            ),
          ])
        : row.nextReviewTime;
    },
  },
  {
    title: '最近修改人',
    key: 'updateUserName',
    align: 'center',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '最近修改时间',
    key: 'updateTime',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '证书状态',
    key: 'certificateStatus',
    align: 'center',
    fixed: 'right',
    width: 160,
    ellipsis: {
      tooltip: false,
    },
    render(e) {
      const status = e.certificateStatus;
      const color =
        status === 1
          ? '#00B578FF'
          : status === 2
            ? 'rgba(250, 81, 81, 1)'
            : 'rgba(229, 163, 60, 1)';
      const text = status === 1 ? '正常' : status === 2 ? '已过期' : '即将过期';
      return h(
        NTag,
        {
          style: {
            color: 'white',
            background: color,
          },
        },
        () => text
      );
    },
  },
  {
    title: '剩余到期天数',
    key: 'warningDays',
    align: 'center',
    width: 160,
    fixed: 'right',
    ellipsis: {
      tooltip: true,
    },
    render: (e: any) => {
      const status = e.certificateStatus;
      const isWarningDays =
        status == 2
          ? '已过期' + e.warningDays + '天'
          : status > 2
            ? '还剩' + e.warningDays + '天'
            : '--';
      if (status == 1) {
        return '--';
      }
      return h(
        'span',
        {
          style: {
            color: 'rgb(250, 81, 81)',
          },
        },
        isWarningDays
      );
    },
  },
];
const isModalVisible = ref(false);
const selectedImage = ref('');
// 放大图片
const handleImageClick = (filePath: any) => {
  selectedImage.value = `${getApiBase()}${filePath}`;

  isModalVisible.value = true;
};
function getApiBase() {
  return window.location.hostname == 'localhost'
    ? 'http://*************:9862'
    : window.location.origin;
}
function setColumns() {
  columns.value.push(...cols);

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 290,
    fixed: 'right',
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}
setColumns();
function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-edit-button',
          disabled:
            row.createUnitId != store.userInfo.orgCode ||
            row.createUserId === 'aqscdqhqyd',
          onClick: () =>
            emits('action', { action: ACTION.EDIT, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.EDIT }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-edit-button',
          disabled:
            row.isReview == 0 || row.createUnitId != store.userInfo.orgCode,
          onClick: () =>
            emits('action', { action: ACTION.REVIEW, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.REVIEW }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-del-button',
          disabled: row.createUnitId != store.userInfo.orgCode,
          onClick: () => handleDelete(row),
        },
        { default: () => '删除' }
      ),
    ],
  ];

  return useActionDivider(acList);
}
function handleDelete(data: any) {
  $dialog.error({
    title: '删除',
    content: `确定删除？`,
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      deleteData({
        id: data.id,
        status: 1,
      }).then((res: any) => {
        if (res.code == 200) {
          if (tableData.value.length - 1 == 0) {
            pagination.page = pagination.page - 1;
          }
          getTableData(props.code);
          message.success('删除成功');
        }
      });
    },
  });
}

defineExpose({
  getTableDataWrap,
  getTableData,
});
onMounted(() => {
  // getTableData();
});
watch(
  () => props.code,
  (nv) => {
    if (nv) {
      getTableData(nv);
    }
  },
  { immediate: true }
);
defineOptions({ name: 'certificateManagementTable' });
</script>

<style scoped lang="scss">
.com-table-container {
  border-radius: 5px !important;
}

.box {
  height: 100%;
}

.imgBox {
  background-color: white;

  .header {
    width: 20px;

    cursor: pointer;
    // margin-right: 10px;
    margin-left: 98%;
  }

  position: fixed;
  top: 15%;
  z-index: 10000;

  left: 30%;
}
</style>
