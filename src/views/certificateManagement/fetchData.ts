/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-19 16:12:10
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-21 11:01:09
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/views/certificateManagement/fetchData.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import type { IDetail, IPageDataRes, OrgTree } from './type';
import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';

// 获取分页
export function pageData(query: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.queryCertificateList, query);
  return $http.post<IPageDataRes>(url, { data: { _cfg: { showTip: false } } });
}

// 新增
export function addData(query: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.addCertificate, query);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
// 删除
export function deleteData(query: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.deleteCertificate, query);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
// 更新
export function postUpdate(query: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.updateCertificate, query);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}

//证书类型
export function getCertificateType(params: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.getCertificateTypeList, params);
  return $http.get(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}

//删除证书类型
export function deleteCertificateType(params: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.deleteCertificateType, params);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true } } });
}

// 新增证书类型
export function addCertificateType(params: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.addCertificateType, params);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...params } });
}

// 编辑证书类型
export function editCertificateType(params: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.editCertificateType, params);
  return $http.post<IDetail>(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...params } });
}

// 证书复审列表
export function certificateReviewList(params: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.certificateReviewList, params);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}

// 新增证书复审记录
export function addCertificateReviewRecord(params: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.addCertificateReviewRecord, params);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}

// 删除证书复审记录
export function deleteCertificateReviewRecord(params: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.deleteCertificateReviewRecord, params);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true } } });
}

// 获取树形结构
export function getOrgTrees(query: IObj<any>) {
  return $http.get<OrgTree>(api.getUrl(api.type.server, api.name.certificateManagement.getTreeData, query));
}
// 获取树结构列表
export function getTreeDataPerson(data: any) {
  const url = api.getUrl(api.type.server, api.name.certificateManagement.getTreeDataPerson, data);
  return $http.get<IDetail>(url, { data: { _cfg: { showTip: true } } });
}
