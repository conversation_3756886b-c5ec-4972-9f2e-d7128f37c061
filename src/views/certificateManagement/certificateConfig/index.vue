<template>
  <div class="h-[calc(100vh-180px)]">
    <div class="flex">
      <table-comp class="com-table-container" ref="tableCompRef" @action="actionFn" />
    </div>
    <!-- aside -->
    <AsideComp v-model:show="isShowAside" :typeList="typeList" :title="actionLabel" @action="actionFn" />
  </div>
</template>

<script lang="ts" setup>
import AsideComp from './comp/aside/index.vue';
import TableComp from './comp/table/Table.vue';
import type { IActionData, ICheckTempRow } from '../type';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { computed, provide, Ref, ref } from 'vue';
import { deleteCertificateType } from '../fetchData';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from '../constant';
const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const actionLabel = ref('');
// const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);
const isShowAside = ref(false);
const tableCompRef = ref();
const curTab = ref('1');
const showModal = ref(false);
const typeList = ref([]);

// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

function handleChange(value: string) {
  curTab.value = value;
}

function actionFn(val: IActionData) {
  console.log('🚀 ~ actionFn ~ val:', val);
  if (val.action === ACTION.SEARCH) {
    handleSearch(val.data);
  }
  if (val.action === ACTION.ADD || val.action === ACTION.EDIT) {
    if (val.action === ACTION.EDIT) {
      // typeList.value = val.data;
      typeList.value = val;
      actionLabel.value = '编辑证书类型';
    } else {
      typeList.value = [];
      actionLabel.value = '新增证书类型';
    }
    isShowAside.value = true;
  }
  if (val.action === ACTION.DELETE) {
    return handleDelete(val.data as ICheckTempRow);
  }
}

function handleSearch(data?: Record<string, any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

function handleDelete(data: ICheckTempRow) {
  $dialog.error({
    title: '删除',
    content: `确定删除该配置？`,
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      deleteCertificateType({ id: data.id }).then(() => {
        handleSearch();
      });
    },
  });
}
defineExpose({
  handleSearch,
});

defineOptions({ name: 'certificateManagementIndex' });
</script>

<style module lang="scss"></style>
