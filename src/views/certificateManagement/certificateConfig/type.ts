/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-16 09:23:08
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-20 10:09:36
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/views/certificateManagement/certificateConfig/type.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { ACTION } from '../constant';
import type { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 分页列表数据
export interface IPageData {
  id: string;
  name: string;
}
export type IPageDataRes = IPageRes<IPageData>;
export interface ICheckTempRow {
  id: string;
}

export interface IDetail {
  id: string;
}
export interface ICategory {
  name: string;
  warningDays: number | string;
  reviewCycle: number | string;
  reviewCycleType: string;
  isReview: string;
  definition: string;
}
