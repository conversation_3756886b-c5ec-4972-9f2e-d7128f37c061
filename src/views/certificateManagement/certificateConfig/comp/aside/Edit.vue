<template>
  <div :class="$style.wrap">
    <n-spin :show="loading" :delay="500">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="110"
        require-mark-placement="left"
      >
        <n-form-item label="证书类型" path="name">
          <n-input
            v-model:value="formData.name"
            maxlength="20"
            type="input"
            clearable
            show-count
            placeholder="请输入证书类型名称"
          />
        </n-form-item>
        <n-form-item label="是否需要复审" path="isReview">
          <n-radio-group v-model:value="formData.isReview" :disabled="type == 'add' ? false : true" size="medium">
            <n-radio value="1"> 是 </n-radio>
            <n-radio value="0"> 否 </n-radio>
          </n-radio-group>
        </n-form-item>
        <n-form-item label="预警天数" path="warningDays" clearable v-if="formData.isReview == '1'">
          <n-input
            type="text"
            v-model:value="formData.warningDays"
            :allow-input="onlyAllowNumber"
            placeholder="请输入预警天数"
            clearable
            maxlength="3"
          />
        </n-form-item>
        <n-form-item label="复审周期" path="reviewCycle" v-if="formData.isReview == '1'">
          <n-input
            type="text"
            class="w-[500px] mr-[20px]"
            v-model:value="formData.reviewCycle"
            :allow-input="onlyAllowNumber"
            placeholder="请输入复审周期"
            clearable
            maxlength="2"
          />
          <n-select
            path="reviewCycleType"
            class="!w-[140px]"
            v-model:value="formData.reviewCycleType"
            label-field="label"
            value-field="value"
            :options="reviewTypeList"
          />
        </n-form-item>
        <n-form-item label="证书定义" path="definition">
          <n-input
            v-model:value="formData.definition"
            clearable
            show-count
            placeholder="请输入证书定义"
            maxlength="200"
            type="textarea"
          />
        </n-form-item>
      </n-form>
    </n-spin>
  </div>
</template>

<script setup lang="ts">
import { FileUpload } from '@/components/upload';
import _ from 'lodash';
import { codeOrg, levelCode, userTelphone } from '@/utils/levelCodeList';
import type { IActionData, ICategory } from '../../type';
import { computed, inject, onMounted, ref, Ref, watch } from 'vue';
import { FormRules, DataTableColumns, NButton } from 'naive-ui';
import { addCertificateType, editCertificateType } from '../../../fetchData';
import { PROVIDE_KEY } from '../../../constant';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { reviewTypeList } from '../table/columns';
import { useMessage } from 'naive-ui';
const message = useMessage();
const emits = defineEmits(['submitted']);
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const actionData = computed(() => currentAction.value.data);
const [loading, run] = useAutoLoading(true);
const fileData = ref<any[]>([]);
const type = ref<string>('add');
const editData = ref<any>({});
const formData = ref({
  name: '',
  warningDays: '',
  reviewCycle: '',
  reviewCycleType: 'year',
  isReview: '1',
  definition: '',
  levelCode: levelCode.value,
});

const props = defineProps({
  typeList: {
    type: Object,
    default: () => {},
  },
});

watch(
  () => props.typeList,
  (nv) => {
    // console.log('🚀 ~ props.typeList:', props.typeList);
    if (Object.keys(props.typeList).length > 0) {
      type.value = props.typeList.action;
      // eslint-disable-next-line vue/no-mutating-props
      props.typeList.data.isReview = props.typeList.data.isReview + '';
      // formData.value = props.typeList.data;
      formData.value = _.cloneDeep(props.typeList.data);
    }
  },
  { immediate: true }
);

const rules: FormRules = {
  name: {
    required: true,
    message: '请输入证书类型名称',
    trigger: ['blur', 'input'],
  },
  isReview: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择是否需要复审',
  },
  warningDays: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入预警天数',
    validator: (a, b) => {
      return !!b;
    },
  },
  reviewCycle: {
    required: true,
    // type: 'number',
    message: '请输入复审周期',
    trigger: ['blur', 'input'],
    validator: (a, b) => {
      return !!b;
    },
  },
  // reviewType: { required: true, type: 'array', message: '请选择', trigger: 'change' },
  // definition: { message: '请输入', trigger: ['blur', 'input'] },
};

const onlyAllowNumber = (value: string) => {
  return !value || /^(?!0)\d+$/.test(value);
};

onMounted(() => {
  loading.value = false;
});
function getData() {}

const formRef: any = ref(null);
function handleSubmit() {
  if (formData.value.isReview == '0') {
    formData.value.warningDays = '';
    formData.value.reviewCycle = '';
    formData.value.reviewCycleType = '';
  }
  console.log('🚀 ~ formRef.value?.validate ~ formData.value:', formData.value);
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      if (type.value === 'add') {
        await addCertificateType(formData.value);
        message.success('新增成功');
      } else {
        await editCertificateType(formData.value);
        message.success('编辑成功');
      }
      emits('submitted');
    } else {
      console.log(errors);
      // message.error('验证失败');
    }
  });
}

// init
getData();

defineExpose({
  handleSubmit,
});

defineOptions({ name: 'certificateManagementEdit' });
</script>

<style module lang="scss">
.wrap {
  padding: 24px;
}

.checkbox-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  row-gap: 20px;
  padding: 0 10px;
  margin: 20px 0 30px;
  min-height: 107px;
}

.red {
  color: #a30014;
}
</style>
