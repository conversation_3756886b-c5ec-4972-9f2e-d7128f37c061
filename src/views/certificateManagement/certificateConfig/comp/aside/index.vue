<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-16 09:23:08
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-19 22:46:13
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/views/certificateManagement/certificateConfig/comp/aside/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <ComDrawerA
    :autoFocus="false"
    :footerPaddingBottom="25"
    :maskClosable="false"
    :show-action="true"
    @handle-negative="handleClose"
    @handle-positive="handleSubmit"
    class="!w-[550px]"
  >
    <Edit ref="editRef" :typeList="props.typeList" @submitted="handleSubmitted" />
  </ComDrawerA>
</template>

<script lang="ts" setup>
import ComDrawerA from '@/components/drawer/ComDrawerA.vue';
import Edit from './Edit.vue';
import type { IActionData } from '../../type';
import { ACTION, PROVIDE_KEY } from '../../../constant';
import { computed, useAttrs, watch, inject, Ref, ref } from 'vue';

const emits = defineEmits(['action']);
const attrs = useAttrs();
const show = computed(() => !!attrs.show);
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);
const editRef = ref();
const props = defineProps({
  typeList: {
    type: Array,
    default: () => [],
  },
});

// watch(
//   () => props.typeList,
//   (val) => {
//     if (val) {
//       emits('action', { action: ACTION.ADD });
//     }
// })

function handleSubmit() {
  editRef.value?.handleSubmit();
}

function handleSubmitted() {
  // emits('action', { action: ACTION.SEARCH });
  handleClose();
}

function handleClose() {
  emits('action', { action: ACTION.SEARCH });
  emits('update:show' as any, false); // any-屏蔽组件内透传的emit类型
}

defineOptions({ name: 'certificateManagementAside' });
</script>

<style module lang="scss"></style>
