<template>
  <div class="">
    <n-form class="mb-[10px]" :show-feedback="false" label-placement="left">
      <div class="flex justify-between">
        <div></div>
        <div class="w-[12%] flex justify-end" style="margin-top: -30px">
          <n-button type="primary" @click="doHandle(ACTION.ADD)">
            {{ ACTION_LABEL.ADD }}
          </n-button>
        </div>
      </div>
    </n-form>
    <div class="box" style="height: 72vh">
      <n-data-table
        class="h-[calc(100%)] com-table"
        remote
        :columns="columns"
        :data="tableData"
        :bordered="false"
        :flex-height="true"
        scroll-x=""
        :pagination="pagination"
        :theme-overrides="themeOverrides"
        :loading="loading"
        :render-cell="useEmptyCell"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import type { IPageData } from '../../../type';
import { ACTION, ACTION_LABEL } from '../../../constant';
import { cols } from '../../comp/table/columns';
import { DataTableColumns, NButton, useMessage } from 'naive-ui';
import { h, reactive, ref, toRaw, VNode } from 'vue';
import { getCertificateType } from '../../../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { IObj } from '@/types';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { onMounted } from 'vue';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(true);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IPageData[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData);
let filterForm = reactive({
  // type: undefined,
  status: 0,
});
let filterForms = {};
const themeOverrides = {
  thColor: '#BBCCF3',
  thTextColor: '#222',
  tdColorStriped: '#dfeefc',
  // tdColorHover: 'rgba(18, 83, 123, 0.35)',
  tdColorStripedModal: 'red',
  // tdColorHoverModal: 'rgba(18, 83, 123, 0.35)',
  // tdColorHoverPopover: 'rgba(18, 83, 123, 0.35)',
};

function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    status: 0,
    ...filterForms,
  };
  search(getCertificateType(params)).then((res: any) => {
    console.log(res, 'res');
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterForms = data;
  // filterForm = Object.assign({ filterForm }, data) || {};
  pagination.page = 1;
  getTableData();
}
onMounted(() => {
  getTableData();
});
function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    // data: trimObjNull(tableData.value),
  });
}

function setColumns() {
  columns.value.push(...cols);

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-del2-button',
          onClick: () =>
            emits('action', { action: ACTION.EDIT, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.EDIT }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-del-button',
          onClick: () =>
            emits('action', { action: ACTION.DELETE, data: toRaw(row) }),
        },
        { default: () => '删除' }
      ),
    ],
  ];

  return useActionDivider(acList);
}

// on created
setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
});

defineOptions({ name: 'certificateManagementTable' });
</script>

<style module lang="scss">
.n-button {
  width: 88px !important;
}
</style>
