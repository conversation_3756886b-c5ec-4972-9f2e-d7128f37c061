/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-16 09:23:08
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-20 10:57:35
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/views/certificateManagement/certificateConfig/comp/table/columns.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    // align: 'center',
    width: 55,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '证书类型',
    key: 'name',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '证书定义',
    key: 'definition',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '是否需要复审',
    key: 'isReview',
    width: 180,
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return row.isReview == 1 ? '是' : '否';
    },
  },
  {
    title: '预警天数',
    key: 'warningDays',
    width: 180,
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return row.warningDays ? row.warningDays + '天' : '--';
    },
  },
  {
    title: '复审周期',
    key: 'reviewCycle',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      const text = row.reviewCycleType == 'year' ? '年' : '月';
      return row.reviewCycle ? row.reviewCycle + text : '--';
    },
  },
  {
    title: '最近修改人',
    key: 'updateUserName',
    width: 180,
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '最近修改时间',
    key: 'updateTime',
    align: 'center',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
];

export const typeOpt = [
  { label: '动火证', value: '1' },
  { label: '焊接证', value: '2' },
];

export const statusOpt = [
  { label: '正常', value: '1' },
  { label: '已过期', value: '2' },
  { label: '即将过期', value: '2' },
];

export const reviewOpt = [
  { label: '是', value: '1' },
  { label: '否', value: '2' },
];

export const reviewTypeList = [
  { label: '年', value: 'year' },
  { label: '月', value: 'month' },
];
