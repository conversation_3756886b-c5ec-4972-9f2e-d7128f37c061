import { ACTION } from './constant';
import type { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 分页列表数据
export interface IPageData {
  id: string;
  name: string;
}
export type IPageDataRes = IPageRes<IPageData>;
export interface ICheckTempRow {
  id: string;
}

export interface IDetail {
  id: string;
}
export interface ICategory {
  // 证书名称
  name: string;
  // 证书编号
  code: string;
  // /证书类型id
  certificateTypeId: string;
  // 员工
  staffName: string;
  //部门
  deptName: string;
  //岗位
  postName: string;
  //发证机构
  issueOrg: string;
  //发证日期
  issueTime: number | null;
  //需要复审
  isReview: string;
  //有效期
  effectiveTimeBegin: number | null;
  //最近复审日期
  latestReviewTime: number | null;
  //下次复审日期
  nextReviewTime: number | null;
  //附件
  filePath: string;
}
export interface ICategoryRes {
  //最近复审日期
  latestReviewTime: string | null;
  //下次复审日期
  nextReviewTime: string | null;
}
export interface OrgTree {
  /**
   * 节点属性
   */
  attributes?: { [key: string]: any };
  /**
   * 点是否被选中
   */
  checked?: boolean;
  /**
   * 节点的子节点
   */
  children?: OrgTree[];
  hasChildren?: boolean;
  hasParent?: boolean;
  /**
   * 主键id
   */
  id?: string;
  /**
   * 层级
   */
  level?: number;
  /**
   * 父ID
   */
  parentId?: string;
  state?: string;
  /**
   * 节点名称
   */
  text?: string;
  /**
   * 树名
   */
  treeName?: string;
  /**
   * 节点类型
   */
  type?: string;
  /**
   * 节点id
   */
  typeId?: string;
  [property: string]: any;
}
