<!--
 * @Author: xginger <EMAIL>
 * @Date: 2024-09-14 17:45:20
 * @LastEditors: Zhangyaling <EMAIL>
 * @LastEditTime: 2025-05-09 15:06:11
 * @FilePath: \ehs-org-alloc-mgr\src\layouts\MainLayout.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div v-if="!isInIfm" :class="[$style['main-layout'], $style['row']]">
    <Header :class="$style['header']" />
    <Menu :class="$style['sidebar']" />
    <router-view :class="$style['content']" v-slot="{ Component }">
      <keep-alive :include="keepAliveComps">
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </div>
  <div v-else :class="[$style['main-layout'], $style['row'], $style['row-ifm']]">
    <router-view :class="$style['content']" v-slot="{ Component }">
      <keep-alive :include="keepAliveComps">
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </div>
</template>

<script lang="ts" setup>
import Header from '@/views/header/index.vue';
import Menu from '@/views/menu/index.vue';
const isInIfm = window.__IFM_ENV__;
// 需缓存组件
const keepAliveComps = [] as string[];

defineOptions({ name: 'MainLayoutComp' });
</script>

<style module lang="scss">
body {
  min-width: 1366px !important;
}

.main-layout {
  display: grid;
  // min-width: 1366px;

  &.row {
    grid-template-areas:
      'header header'
      'sidebar content';
    /* 定义grid区域 */
    grid-template-columns: auto 1fr;
    grid-template-rows: var(--header-height) calc(100vh - var(--header-height));
  }

  &.row2 {
    grid-template-areas:
      'header header'
      'sidebar content';
    /* 定义grid区域 */
    grid-template-columns: auto 1fr;
    grid-template-rows: 0px calc(100vh);
  }

  &.row-ifm {
    grid-template-areas: 'content';
    /* 定义grid区域 */
    grid-template-columns: 1fr;
    grid-template-rows: 100vh;
  }
}

.header {
  grid-area: header;
  z-index: 9;
}

.sidebar {
  grid-area: sidebar;
  min-width: 48px;
}

.content {
  grid-area: content;
  overflow: auto;
  padding: 0 24px 24px;
  display: flex;
  flex-direction: column;
}
</style>
