import { $Config } from '@tanzerfe/http';
import useToastCtx from '@/common/shareContext/useToastCtx.ts';
import { useStore } from '@/store';

export function setupApi() {
  const store = useStore();

  $Config.getToken = () => store.userInfo.userToken;
  $Config.$toastDark = useToastCtx({ theme: 'dark' });
  $Config.$toastLight = useToastCtx({ theme: 'light' });
}
$Config.requestErrorHandler = (error: any) => {
  console.log(error, 'error');
  if (error && error.code && error.code === 'ERR_NETWORK') {
    location.reload();
  }
};
