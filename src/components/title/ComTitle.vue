<template>
  <div :class="[$style.DutyEvaluationObjTitle, bottom ? $style.bottom : '']">
    <slot>
      <span>{{ title }}</span>
    </slot>
    <slot name="right"></slot>
  </div>
</template>

<script setup lang="ts">
defineProps({
  title: String,
  bottom: {
    type: Boolean,
    required: false,
  },
});

defineOptions({ name: 'ComTitle' });
</script>

<style module lang="scss">
.DutyEvaluationObjTitle {
  position: relative;
  font-size: 16px;
  font-weight: bold;
  color: #222;
  min-height: 32px;
  padding-left: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 3px;
    height: 16px;
    background: #527cff;
    border-radius: 4px;
  }

  &.bottom {
    border-bottom: 1px solid var(--skin-bd1);
  }
}
</style>
