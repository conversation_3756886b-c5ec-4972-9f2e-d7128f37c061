import type { IDetail, IPageDataRes, PageModel, OrgTree } from './type';
import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';

// 获取人员列表
export function getPersonManageList(params: any) {
  const url = api.getUrl(api.type.server, api.name.interface.getExpertPersonManageList);
  return $http.post<IPageDataRes>(url, {
    data: { _cfg: { showTip: true }, ...params },
  });
}

// 获取树形结构
export function getOrgTrees(query: IObj<any>) {
  return $http.get<OrgTree>(api.getUrl(api.type.server, api.name.interface.getTreeData, query));
}

// 获取组织机构树形菜单
export function getOrgTree(query: IObj<any>) {
  return $http.get<OrgTree>(api.getUrl(api.type.server, api.name.interface.getTreeData, query));
}

// 获取相关方树形
export function getAllUnit(query: IObj<any>) {
  const url = api.getUrl('ehs-clnt-hazard-service', '/ehsUpms/getAllUnit');
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...query },
  });
}

// 获取承租方、相关方树
export function queryOrgTreeByTanzer(query: IObj<any>) {
  const url = api.getUrl('train-server', '/org/queryOrgTreeByTanzer', {
    ...query,
  });
  return $http.post<any[]>(url, {
    data: { _cfg: { showTip: true } },
  });
}

export function queryUserByDeptCode(query: IObj<any>) {
  const url = api.getUrl('train-server', '/org/queryUserByDeptCode', {
    ...query,
  });
  return $http.post<any[]>(url, {
    data: { _cfg: { showTip: true } },
  });
}

// 获取机构人员
export function getOrgUserList(query: IObj<any>) {
  const url = api.getUrl('ehs-clnt-hazard-service', '/ehsUpms/getOrgUserPage');
  return $http.post(url, { data: { _cfg: { showTip: true }, ...query } });
}
// 获取承租方人员信息
export function getTenantUserList(query: IObj<any>) {
  const url = api.getUrl(api.type.server, '/personManage/getTenantryUsersByTenantryId', {
    ...query,
  });
  return $http.get<any[]>(url, { data: { _cfg: { showTip: true } } });
}
// 获取相关方人员信息1
export function getPerManagerList1(query: IObj<any>) {
  const url = api.getUrl(api.type.server, '/personManage/getPersonManageEduUser', {
    ...query,
  });
  return $http.post<any[]>(url, {
    data: { _cfg: { showTip: true } },
  });
}
