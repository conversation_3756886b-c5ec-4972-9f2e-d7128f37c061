<template>
  <n-modal>
    <n-card
      class="n_card"
      :title="title"
      :bordered="false"
      size="huge"
      preset="dialog"
      role="dialog"
      aria-modal="true"
    >
      <template #header-extra>
        <span class="sp" @click="cancel">×</span>
      </template>
      <div class="content">
        <div class="header">
          <div class="text">已选人员：</div>
          <div class="box">
            <n-tag
              :color="{
                color: '#fff',
                textColor: '#2080f0',
                borderColor: '#2080f0',
              }"
              v-for="(item, index) in checkedDatas"
              :key="index"
              closable
              size="small"
              @close="handleClose(item._id, item.id)"
            >
              {{ item.userName }}
            </n-tag>
          </div>
        </div>
        <div class="search-box">
          <div class="text" style="margin-top: 5px">关键字搜索：</div>
          <n-input
            placeholder="请输入员工姓名或岗位或手机号搜索"
            :style="{ width: '50%' }"
            size="small"
            v-model:value="searchValue"
            @input="keyWordSearch"
          />
        </div>

        <div class="main">
          <div class="left">
            <div class="list">
              <comtwo-tree
                :parentOrgCode="parentOrgCode"
                :data="treeData"
                @update="treeChange"
              ></comtwo-tree>
            </div>
          </div>

          <div class="right">
            <div>
              <n-data-table
                remote
                striped
                :columns="columns"
                :data="tableData"
                :bordered="false"
                :pagination="pagination"
                :loading="loading"
                :min-height="400"
                :max-height="400"
                :render-cell="useEmptyCell"
                v-model:checked-row-keys="checkedKeys"
                :row-key="(row: any) => row._id"
                @update:checked-row-keys="handleCheck"
              />
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <n-space>
          <n-button class=" " strong @click="cancel">关闭</n-button>
          <n-button strong type="primary" @click="onConfirm">保存</n-button>
        </n-space>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { useStore } from '@/store';
import { DataTableRowKey, NTag, TreeOption, useMessage } from 'naive-ui';
import { ref, toRaw, useAttrs, watch } from 'vue';
import { useAutoLoading } from '../../common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '../../common/hooks/useNaivePagination.ts';
import ComtwoTree from '../tree/comTree2.vue';
import { getOrgTrees } from './fetchData.ts';

const store = useStore();
const message = useMessage();

const { pagination, updateTotal } = useNaivePagination(getTabData);

const url = (pagination.pageSlot = 5);
const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '选择人员',
  },
  useArray: {
    type: Array<string | number | undefined>,
    default: () => [],
  },
  //用户置灰,是否使用isConfig字段控制,反之使用调用getList方法传入数据。
  userConfigFeid: {
    type: Boolean,
    default: false,
  },

  parentOrgCode: {
    type: String,
  },
  rule: {
    type: Boolean,
    default: false,
  },
});
const attr = useAttrs();
const emits = defineEmits(['close', 'selectUserData', 'getPersonManageData']);
const [loading, search] = useAutoLoading(true);
const treeId: any = ref('');
// 选择项id合集
const checkedKeys = ref<Array<string | number>>([]);
// 选择项具体数据合集
const checkedDatas = ref<Array<any>>([]);
// #region 右侧表格
interface Song {
  id: number;
  userName: string;
  loginName: string;
  userTelphone: string;
  userTypeName: string;
}
const searchValue = ref<string>('');
const list = ref([]);
const getList = (data: any) => {
  list.value = data.map((item: any) => {
    return {
      ...item,
      personId: item.userId ? item.userId : item.id,
    };
  });
  list.value = list.value.map((item: any) => {
    return {
      ...item,
      _id: item.personId + item.unitId + item.deptId,
    };
  });
  checkedDatas.value = list.value;

  checkedKeys.value = list.value.map((item: any) => {
    return item._id;
  });
};

const columns = <any[]>[
  {
    type: 'selection',
    disabled(row: any) {
      {
        if (!props.userConfigFeid) {
          return row.isConfig == 1;
        } else {
          return false;
        }
      }
    },
  },

  {
    title: '序号',
    key: 'index',
    width: 60,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '人员姓名',
    key: 'userName',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '部门',
    key: 'deptName',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '岗位',
    key: 'postName',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '员工类别',
    key: 'userTypeName',
    ellipsis: {
      tooltip: true,
    },
  },
];
const tableData = ref<Song[]>([]);
const levelCode: any = ref('');
const treeData = ref([]);
//监听树结构点击
const treeChange = (v: TreeOption) => {
  treeId.value = v.id;
  levelCode.value = v.levelCode;
  pagination.page = 1;
  getTabData();
};
function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };
    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }

    return newItem; // 返回新对象
  });
}

//获取树结构数据
function QueryOrgTrees() {
  const params = {
    orgCode: store.userInfo.unitId,
    needChildUnit: '1',
    needself: '1',
  };
  search(getOrgTrees(params)).then((res: any) => {
    if (res.code != 200) return;
    const _RES: any = removeEmptyChildren(res.data);
    treeData.value = _RES;
    // console.log(treeData.value[0].levelCode);
    levelCode.value = treeData.value[0].levelCode;
  });
}

function renderTable(res: any) {
  if (res.code != 200) return;
  const _Data: any = res.data.rows;
  if (_Data.length === 0) {
    tableData.value = [];
    pagination.page = 1;
    updateTotal(0);
    return;
  }
  _Data.forEach((item: any) => {
    item._id = item.id + item.unitId + item.deptId;
  });

  tableData.value = _Data || [];

  updateTotal(res.data.total || 0);
}
async function fun() {
  const params = {
    orgCode: treeId.value ? treeId.value : store.userInfo.unitId,
    keyWords: searchValue.value,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
  };
  emits('getPersonManageData', params);
}
function getTabData() {
  search(fun());
}

function keyWordSearch() {
  pagination.page = 1;
  search(fun());
}

const rowList = ref({});
//勾选事件
function handleCheck(rowKeys: DataTableRowKey[], data: any) {
  const arr: any = [];
  for (const key of rowKeys) {
    for (const checkedData of checkedDatas.value) {
      if (key === checkedData._id) {
        const obj = arr.find((item: any) => {
          return item._id === checkedData._id;
        });
        if (obj === undefined) {
          arr.push(checkedData);
        }
      }
    }
    for (const item of data) {
      if (item !== undefined) {
        const obj = arr.find((itemObj: any) => {
          return itemObj._id === item._id;
        });
        if (obj === undefined) {
          arr.push(item);
        }
      }
    }
  }
  checkedDatas.value = arr;
}
// 取消选择事件
const handleClose = (value: any, id: any) => {
  checkedDatas.value = checkedDatas.value.filter((item) => {
    return item.id !== id;
  });
  checkedKeys.value = checkedKeys.value.filter((item) => {
    return item !== value;
  });
};
defineExpose({
  getList,
  renderTable,
});
const cancel = () => {
  // 置空id合集
  checkedKeys.value = [];
  checkedDatas.value = [];
  searchValue.value = '';
  pagination.page = 1;
  pagination.pageSize = 10;
  treeId.value = '';
  emits('close');
};
//输出id合集
const onConfirm = () => {
  checkedDatas.value = checkedDatas.value.filter((item) => {
    return item.id !== undefined;
  });
  if (checkedDatas.value.length > 0) {
    emits('selectUserData', checkedDatas.value, levelCode.value);
    pagination.page = 1;
    pagination.pageSize = 10;
    treeId.value = '';
    cancel();
  } else if (props.rule) {
    emits('selectUserData', checkedDatas.value, levelCode.value);
    pagination.page = 1;
    pagination.pageSize = 10;
    treeId.value = '';
    cancel();
  } else {
    message.error('请选择人员');
  }
};
watch(
  () => props.showModal,
  (nv) => {
    if (!props.showModal) return;
    const _checkedKeys = toRaw(props.useArray);
    if (_checkedKeys.length) {
      checkedDatas.value = _checkedKeys;
      _checkedKeys.filter((item: any) => checkedKeys.value.push(item.id));
    }
  },
  { immediate: true }
);
watch(
  () => attr.show,
  (nv) => {
    if (nv) {
      QueryOrgTrees();
      getTabData();
    }
  }
  // { immediate: true }
);
</script>

<style lang="scss" scoped>
.sp {
  // font-family: '宋体';
  font-size: 24px;
}
.sp:hover {
  cursor: pointer;
}
.n_card {
  width: 1000px;
  position: relative;
  height: 750px;
  background-color: #f8f8f8;
  :deep(.n-card-header) {
    // padding: 15px;
    padding-left: 20px;
    padding-right: 20px;
    padding-top: 10px;
    padding-bottom: 10px;
  }
  :deep(.n-card-header .n-card-header__main::before) {
    content: '';
    display: inline-block;
    width: 4px;
    height: 15px;
    margin-right: 5px;
    background-color: #527cff;
  }
}
.content {
  height: 100%;

  .header {
    display: flex;
    margin-bottom: 10px;

    .text {
      color: #606266;
    }

    .box {
      flex: 1;
      margin-left: 10px;
      max-height: 60px;
      height: 60px;
      border-radius: 3px;
      border: 1px solid #dcdfe6;
      background-color: #fff;
      overflow-y: auto;
    }
  }

  .main {
    height: 500px;
    display: flex;
    justify-content: space-between;

    .left {
      width: 280px;
      height: 500px;
      margin-right: 10px;
      background-color: #fff;
      border-radius: 3px;
      border: 1px solid #e1e1e1;

      .list {
        height: 600px;
        overflow-y: auto;
      }
    }

    .right {
      flex: 1;
      padding: 10px 10px;
      background-color: #fff;
      border-radius: 3px;
      border: 1px solid #e1e1e1;
    }
  }
}
.search-box {
  width: 100%;
  margin-bottom: 10px;
  display: flex;
  justify-content: end;
}
.n-space {
  position: absolute;
  bottom: 1%;
  right: 5%;
}
.n-input {
  width: 300px !important;
}
:deep(.n-tag) {
  padding: 10px 12px !important;
  margin: 5px 5px 0 5px !important;
  border-radius: 4px;
}
</style>
