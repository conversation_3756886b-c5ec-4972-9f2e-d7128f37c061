<template>
  <n-modal>
    <n-card class="n_card" :title="title" :bordered="false" size="huge" preset="dialog" role="dialog" aria-modal="true">
      <template #header-extra>
        <span class="sp" @click="cancel">×</span>
      </template>
      <div class="content">
        <div class="header">
          <div class="text">已选人员：</div>
          <div class="box">
            <n-tag
              :color="{
                color: '#fff',
                textColor: '#2080f0',
                borderColor: '#2080f0',
              }"
              v-for="(item, id) in selectUserInfo"
              :key="id"
              closable
              size="small"
              @close="handleUnSelect(id)"
            >
              {{ item.userName }}
            </n-tag>
          </div>
        </div>
        <div class="search-box">
          <div class="text" style="margin-top: 5px">关键字搜索：</div>
          <n-input
            size="small"
            placeholder="请输入员工姓名或岗位或手机号搜索"
            :style="{ width: '50%' }"
            v-model:value="searchValue"
          />
        </div>

        <div class="main">
          <div class="left">
            <div class="left">
              <ComTree
                ref="tabRef"
                :sourceRouter="sourceRouter"
                :userType="userTypeValue"
                :treeData="treeData"
                :custom-tabs="customTabs"
                @tabChange="tabChange"
                @action="treeChange"
              />
            </div>
            <!-- </div> -->
          </div>

          <div class="right">
            <div>
              <n-data-table
                remote
                striped
                :columns="columns"
                :data="tableData"
                :bordered="false"
                :pagination="pagination"
                :loading="loading"
                :max-height="400"
                :min-height="400"
                :row-key="(row: any) => row.id"
                :default-checked-row-keys="defaultCheckedIds"
                v-model:checked-row-keys="checkedRowKeys"
                @update:checked-row-keys="updateCheckedKeys"
              />
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <n-space>
          <n-button class=" " strong @click="cancel">取消</n-button>
          <n-button strong type="primary" @click="onConfirm">保存</n-button>
        </n-space>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, watch, computed, useAttrs, toRaw, watchEffect } from 'vue';
import { NTag, NIcon, TreeOption, DataTableRowKey, useMessage } from 'naive-ui';
import ComTree from '../tree2/index.vue';
import {
  getOrgTree,
  queryUserByDeptCode,
  getTenantUserList,
  getAllUnit,
  getPerManagerList1,
  queryOrgTreeByTanzer,
} from './fetchData.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { cloneDeep, throttle } from 'lodash-es';
import { getTreeDataPerson } from '@/views/personManage/fetchData.ts';
import { de } from 'date-fns/locale';

type ITab = Array<{ label: string; value: number }>;

const { pagination, updateTotal } = useNaivePagination(getTabData);
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  userType: {
    type: Number,
    default: 1,
  },
  unitId: {
    type: String,
    efault: '',
  },
  showSelectUserModal: {
    type: Boolean,
    default: false,
  },
  userlist: {
    type: Array,
    default: () => [],
  },
  sourceRouter: {
    type: String,
    efault: '',
  },
  // 安全管理人员/特种作业人员
  curTab: {
    type: Number,
    default: 1,
  },
  // 自定义tabs
  customTabs: {
    type: Array as () => ITab | undefined,
    required: false,
  },
  defaultCheckedIds: {
    type: Array as () => Array<string>,
    default: () => [],
  },
  defaultCheckedUserInfo: {
    type: Object,
    default: () => {},
  },
  modelId: String,
});

const checkedRowKeys = ref<string[]>([]);
const checkedRowDataMap = ref(new Map()); // Map<userId, row>
const selectUserInfo = computed(() => {
  const defaultUserMap: Record<string, any> = cloneDeep(props.defaultCheckedUserInfo);
  const data: Record<string, any> = {};
  for (const id of checkedRowKeys.value) {
    data[id] = checkedRowDataMap.value.get(id);

    if (!data[id]) {
      const v = defaultUserMap[id];

      if (v) {
        v.id = v.userId;
      }

      data[id] = v || {};
    }
  }

  return data;
});

function handleUnSelect(id: string) {
  checkedRowKeys.value = checkedRowKeys.value.filter((item) => item != id);
}

watchEffect(() => {
  checkedRowKeys.value = [...props.defaultCheckedIds];
});

// 显示承租方（初始化调用是否有承租方数据 无数据则不展示承租方tab）
const checkIds = computed(() => checkUsers.value.map((item) => item.checkUserId));
const message = useMessage();
const tabRef = ref();
const userTypeValue = ref(1);
const emits = defineEmits(['close', 'treeChange', 'success']);
const treeId: any = ref(null);
const checkUsers = ref<Array<any>>([]);
const searchValue = ref<string>('');
let colList = [
  // 组织机构
  [
    {
      type: 'selection',
      disabled(row: any) {
        return row.isOtherEvaluateModel === '1'; // 是否其他履职模型绑定： '1'是 '0'否
      },
    },
    {
      title: '序号',
      key: 'index',
      width: 60,
      render: (_: any, index: number) => {
        return index + 1;
      },
    },
    {
      title: '人员姓名',
      key: 'userName',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '部门',
      key: 'deptName',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '岗位',
      key: 'postName',
      ellipsis: {
        tooltip: true,
      },
      render(row: any) {
        return row.postName || '--';
      },
    },
    {
      title: '员工类别',
      key: 'userTypeName',
      ellipsis: {
        tooltip: true,
      },
    },
  ],
  // 相关方
  [
    {
      type: 'selection',
      disabled(row: any) {
        return row.isConfig == 1;
      },
    },
    {
      title: '序号',
      key: 'index',
      width: 60,
      render: (_: any, index: number) => {
        return index + 1;
      },
    },
    {
      title: '人员姓名',
      key: 'userName',
      ellipsis: {
        tooltip: true,
      },
    },

    {
      title: '部门',
      key: 'depart',
      ellipsis: {
        tooltip: true,
      },
      render(row: any) {
        return row.depart || '--';
      },
    },
    {
      title: '岗位',
      key: 'unitNamea',
      ellipsis: {
        tooltip: true,
      },
      render(row: any) {
        return '--';
      },
    },
    {
      title: '员工类别',
      key: 'unitNamea',
      ellipsis: {
        tooltip: true,
      },
      render(row: any) {
        return '相关方';
      },
    },
  ],
  // 承租方
  [
    {
      type: 'selection',
      disabled(row: any) {
        return row.isConfig == 1;
      },
    },
    {
      title: '序号',
      key: 'index',
      width: 60,
      render: (_: any, index: number) => {
        return index + 1;
      },
    },
    {
      title: '人员姓名',
      key: 'userName',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '部门',
      key: 'unitName',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '岗位',
      key: 'unitNamea',
      ellipsis: {
        tooltip: true,
      },
      render(row: any) {
        return '--';
      },
    },

    {
      title: '员工类别',
      key: 'unitNamea',
      ellipsis: {
        tooltip: true,
      },
      render(row: any) {
        return '承租方';
      },
    },
  ],
];
const columns = ref();
const tableData = ref([]);
const treeData: any = ref([]);
const loading = ref(false);
const treeLoading = ref(false);
//树结构点击
const treeChange = (v: TreeOption, notGetData: boolean) => {
  treeId.value = v.id;
  pagination.page = 1;
  pagination.pageSize = 10;
  searchValue.value = '';
  !notGetData && getTabData();
};
//获取树结构数据
async function tabChange(newTab: number) {
  try {
    userTypeValue.value = newTab;
    treeLoading.value = true;
    // 检查人员展示组织内人员是所有人员，相关人和承租方人员是检查对象内的
    if (newTab == 1) {
      let { data } = await getOrgTree({ orgCode: props.unitId });
      treeData.value = (data && childrenFn(data)) || [];
    } else {
      //承租方相关方树结构
      let { data } = await queryOrgTreeByTanzer({
        needChildUnit: 1,
        needself: 0,
        type: newTab,
      });
      treeData.value = data.map((item) => {
        return {
          text: item.tenantryName || item.text,
          id: item.tenantryId || item.id,
        };
      });
    }
    treeLoading.value = false;
    pagination.page = 1;
    pagination.pageSize = 10;
    getTabData();
  } catch {
    treeLoading.value = false;
    treeData.value = [];
    tableData.value = [];
  }
}

function childrenFn(_arr: any) {
  if (!_arr.length) return _arr;
  for (let index = 0; index < _arr.length; index++) {
    const element = _arr[index];
    if (element.children?.length) {
      childrenFn(element.children);
    } else {
      delete element.children;
    }
  }
  return _arr;
}

watch(
  () => searchValue.value,
  () => {
    doHandle();
  }
);
const doHandle = throttle(() => {
  getTabData();
}, 1000);

//获取组织机构人员
const getOrgUserListApi = async (orgCode: any, pageData: any, keyWords: any) => {
  let {
    data: { rows, total },
  }: any = await getTreeDataPerson({
    orgCode,
    keyWords,
    ...pageData,
    type: props.curTab,
    modelId: props.modelId,
  });
  tableData.value = rows
    ? rows.map((item: any) => {
        return {
          ...item,
          checkUserId: item.id,
          onlyId: item.id + ',' + item.deptId,
        };
      })
    : [];
  updateTotal(total || 0);
  loading.value = false;
};

//获取承租方人员
const getTenantUserListApi = async (tenantryId: any, pageData: any, likeParam: any) => {
  let {
    data: { rows, total },
  }: any = await getTenantUserList({
    tenantryId,
    likeParam,
    ...pageData,
    type: props.curTab,
  });
  tableData.value = rows
    ? rows.map((item: any) => {
        return {
          ...item,
          id: item.loginUserId,
          onlyId: item.id + ',' + item.unitId,
        };
      })
    : [];
  updateTotal(total ?? 0);
  loading.value = false;
};

//获取相关方人员
const getPerManagerListApi = async (unitId: any, pageData: any, keyWord: any) => {
  let {
    data: { rows, total },
  }: any = await getPerManagerList1({
    sysCode: 'web',
    isBlack: 0,
    unitId,
    type: props.curTab,
    name: keyWord,
    ...pageData,
  });
  tableData.value = rows
    ? rows?.map((item: any) => {
        return {
          ...item,
          checkUserId: item.id,
          checkUserName: item.name,
          userName: item.name,
          unitId: item.createOrgCode,
          onlyId: item.id + ',' + item.orgCode,
        };
      })
    : [];
  updateTotal(total ?? 0);
  loading.value = false;
};

async function getTabData() {
  // 动态获取列
  columns.value = colList[userTypeValue.value - 1];
  let orgCode = treeId.value || (treeData.value && treeData.value[0]?.id) || null;
  if (!orgCode) return;
  let pageData = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
  };
  loading.value = true;
  try {
    userTypeValue.value == 1 && (await getOrgUserListApi(orgCode, pageData, searchValue.value));
    userTypeValue.value == 2 && (await getPerManagerListApi(orgCode, pageData, searchValue.value));
    userTypeValue.value == 3 && (await getTenantUserListApi(orgCode, pageData, searchValue.value));
  } catch (error) {
    loading.value = false;
  }
}

function updateCheckedKeys(keys: Array<string>, data: any, meta: any) {
  const action = meta?.action;
  checkedRowKeys.value = keys;

  if (action === 'checkAll') {
    for (const item of data) {
      if (item) {
        checkedRowDataMap.value.set(item.id, item);
      }
    }
  } else {
    const userId = meta?.row?.id;

    // 只要触发选择就记录row
    if (userId) {
      checkedRowDataMap.value.set(userId, meta.row);
    }
  }
}

// 取消选择事件
const handleClose = (value: any) => {
  // checkUsers.value = checkUsers.value.filter((item) => {
  //   return item.onlyId != value;
  // });
};

const cancel = () => {
  emits('close');
  checkedRowKeys.value = [...props.defaultCheckedIds];
  // 置空id合集
  userTypeValue.value = 1;
  treeData.value = [];
  treeId.value = null;
  checkUsers.value = [];
  tableData.value = [];
};
//输出id合集
const onConfirm = () => {
  // if (!checkedRowKeys.value.length) return message.error('请选择人员');

  const data: Record<string, any> = {};

  for (const id of checkedRowKeys.value) {
    data[id] = checkedRowDataMap.value.get(id) || null;
  }

  emits('success', cloneDeep(data));

  cancel();
};
const attr = useAttrs();
watch(
  () => attr.show,
  (nv) => {
    tabChange(1);
    getTabData();
  }
);
</script>

<style lang="scss" scoped>
.sp {
  // font-family: '宋体';
  font-size: 24px;
}

.sp:hover {
  cursor: pointer;
}

.n_card {
  width: 1100px;
  position: relative;
  height: 750px;
  background-color: #f8f8f8;

  :deep(.n-card-header) {
    // padding: 15px;
    padding-left: 20px;
    padding-right: 20px;
    padding-top: 10px;
    padding-bottom: 10px;
  }

  :deep(.n-card-header .n-card-header__main::before) {
    content: '';
    display: inline-block;
    width: 4px;
    height: 15px;
    margin-right: 5px;
    background-color: #527cff;
  }
}

.content {
  height: 100%;

  .header {
    display: flex;
    margin-bottom: 10px;

    .text {
      color: #606266;
    }

    .box {
      flex: 1;
      margin-left: 10px;
      max-height: 60px;
      height: 60px;
      border-radius: 3px;
      border: 1px solid #dcdfe6;
      background-color: #fff;
      overflow-y: auto;
    }
  }

  .main {
    height: 500px;
    display: flex;
    justify-content: space-between;

    .left {
      width: 280px;
      height: 500px;
      margin-right: 10px;
      background-color: #fff;
      border-radius: 3px;
      border: 1px solid #e1e1e1;

      .list {
        height: 600px;
        overflow-y: auto;
      }
    }

    .right {
      flex: 1;
      padding: 10px 10px;
      background-color: #fff;
      border-radius: 3px;
      border: 1px solid #e1e1e1;
    }
  }
}

.search-box {
  width: 100%;
  margin-bottom: 10px;
  display: flex;
  justify-content: end;
}

.n-space {
  position: absolute;
  bottom: 1%;
  right: 5%;
}

.n-input {
  width: 300px !important;
}

:deep(.n-tag) {
  padding: 10px 12px !important;
  margin: 5px 5px 0 5px !important;
  border-radius: 4px;
}
</style>
