<!--环形图-->
<template>
  <div v-if="!isEmpty" class="normal-pie w-full h-full" ref="radarChartRef"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, onBeforeUnmount, ref, watch } from 'vue';
import * as echarts from 'echarts';
import Empty from '@/components/empty/index.vue';
import { useEchartsResizeObserver } from '@/utils/useEchartsResizeObserver';
import { sleep } from '@/utils/sleep.ts';

const props = defineProps({
  echartsData: {
    type: Array,
    default: () => {
      return [];
    },
  },
  indicator: {
    type: Array,
    default: () => {
      return [];
    },
  },
});

const isEmpty = ref(false);
const radarChartRef = ref();
const myChart = ref<any>(null);
const observer = ref<ResizeObserver>();

function initEcharts(data: any[]) {
  if (myChart.value) destroyEcharts();
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(radarChartRef.value));

  // 计算最大超出比例
  let maxOverflowRatio = 1;
  data.forEach((dataItem) => {
    dataItem.value.forEach((value, index) => {
      const maxValue = props.indicator[index].max;
      const ratio = value / maxValue;
      if (ratio > maxOverflowRatio) {
        maxOverflowRatio = ratio;
      }
    });
  });

  // 根据超出比例计算半径
  const baseRadius = 70; // 基础半径百分比
  const newRadius = baseRadius / maxOverflowRatio;

  const option = {
    tooltip: {
      show: true,
      trigger: 'item',
      confine: true,
    },
    radar: {
      indicator: props.indicator,
      triggerEvent: true,
      axisName: {
        color: '#527CFF',
        backgroundColor: '#FFFFFF',
        padding: [3, 6],
        borderRadius: 50,
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: '#FFFFFF', // 设置雷达图背景为白色
        },
      },
      radius: `${newRadius}%`, // 根据超出比例调整半径
    },
    series: [
      {
        name: '',
        type: 'radar',
        symbolSize: 0,
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: '#5161EB', // 起始颜色
            },
            {
              offset: 1,
              color: '#CF9AEF', // 结束颜色
            },
          ],
        },
        areaStyle: {
          color: '#448FFF', // 改为单一颜色
          opacity: 0.3, // 设置透明度
        },
        data,
      },
    ],
  };

  myChart.value.setOption(option);
  myChart.value.on('click', function (params: any) {
    console.log(params);
  });
  observer.value = useEchartsResizeObserver(myChart, radarChartRef).observer;
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose();
    myChart.value = null;
  }
}

onMounted(() => {
  watch(
    () => props.echartsData,
    async (val: any[]) => {
      isEmpty.value = !val.length;
      await sleep(500);
      if (!isEmpty.value && radarChartRef.value) initEcharts(val);
    },
    { immediate: true, deep: true }
  );
});

onBeforeUnmount(() => {
  destroyEcharts();
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect();
});

defineComponent({ name: 'RadarChartComp' });
</script>

<style scoped></style>
