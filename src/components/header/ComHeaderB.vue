<template>
  <div :class="$style['header']">
    <slot name="h-icon">
      <img :src="activeColor ? activeIcon : normalIcon" alt="" />
    </slot>
    <span :class="{ [$style.title]: true, [$style.activeColor]: activeColor }">{{ title }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import activeIcon from './assets/icon-title-arrow2.png';
import arrow3Icon from './assets/icon-title-arrow3.png';
import titleArrowIcon from './assets/icon-title-arrow.png';

interface Props {
  title: string;
  activeColor?: boolean;
  blueIcon?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  activeColor: false,
  blueIcon: false,
});

const normalIcon = computed(() => {
  if (props.blueIcon) {
    return arrow3Icon;
  } else {
    return titleArrowIcon;
  }
});

defineOptions({ name: 'ComHeaderB' });
</script>

<style module lang="scss">
.header {
  @apply flex items-center;
  img {
    @apply w-[17px] h-[12px] mr-[10px];
  }
}

.title {
  font-weight: 600;
  font-size: 16px;
  @apply text-[#303133];

  &.activeColor {
    @apply text-[#FFFFFF];
  }
}
</style>
