<template>
  <div :class="[$style.treeInput]">
    <n-input v-model:value="pattern" placeholder="搜索" size="small">
      <template #suffix>
        <BySearch :class="$style['icon']" />
      </template>
    </n-input>
  </div>
  <div class="org">
    <span
      v-for="item in customTabs || tabs"
      :key="item.value"
      :class="props.userType == item.value ? 'active_span' : ''"
      @click="changeTab(item.value)"
      >{{ item.label }}</span
    >
  </div>
  <div :class="[$style.comTree]" class="h-[calc(100%-150px)] overflow-auto">
    <n-tree
      class="tree-cont"
      :show-irrelevant-nodes="false"
      :pattern="pattern"
      :data="treeData"
      block-line
      :override-default-node-click-behavior="override"
      :render-prefix="renderPrefix"
      :default-expanded-keys="defaultExpandedkeys"
      :default-selected-keys="defaultSelectedkeys"
      key-field="id"
      label-field="text"
      size="large"
      style="
        --n-node-content-height: 44px;
        --n-line-height: 44px;
        --n-node-border-radius: 4px;
        --n-node-color-hover: rgba(82, 124, 255, 0.1);
      "
    />
  </div>
</template>

<script lang="ts" setup>
import { BySearch } from '@kalimahapps/vue-icons';
import type { TreeOption } from 'naive-ui';
import { h, ref, watch, watchEffect } from 'vue';
import TreeParentIcon from './comp/TreeParentIcon.vue';
import TreeChildIcon from './comp/TreeChildIcon.vue';
const emits = defineEmits(['action', 'tabChange']);
const pattern = ref('');
const defaultExpandedkeys = ref<string[]>([]);
const defaultSelectedkeys = ref<string[]>([]);

type ITab = Array<{ label: string; value: number }>;

const props = defineProps({
  treeData: {
    type: Array,
    required: true,
  },
  userType: {
    type: Number,
    default: 1, // 设置默认值为组织架构
  },
  customTabs: {
    type: Array as () => ITab | undefined,
    required: false,
  },
});

const tabs = ref<any>([]);
watch(
  () => props.treeData,
  (nv) => {
    if (nv) {
      if (!props.treeData[0]) return;
      let curKey = props.treeData[0].id;
      defaultExpandedkeys.value = [curKey];
      defaultSelectedkeys.value.push(curKey);
    }
  },
  { immediate: true, deep: true }
);

watchEffect(() => {
  tabs.value = [
    { label: '组织架构', value: 1 },
    { label: '相关方', value: 2 },
    { label: '承租方', value: 3 },
  ];
});

function changeTab(val: any) {
  emits('tabChange', val);
}

function override({ option }: { option: TreeOption }) {
  const op: any = option;
  if (props.userType == 1) {
    localStorage.setItem('_riskUnitID', op.id);
    localStorage.setItem('_riskUnitName', op.text);
  } else {
    localStorage.setItem('_riskUnitIDTenant', op.id);
    localStorage.setItem('_riskUnitNameTenant', op.text);
  }
  emits('action', option);
}

function renderPrefix(info: { option: TreeOption; checked: boolean; selected: boolean }) {
  if (info.option.key === '0') {
    return h(TreeParentIcon);
  }
  return h(TreeChildIcon);
}

defineOptions({ name: 'ComRadioTabE' });
</script>

<style module lang="scss">
.treeInput {
  margin: 16px 12px 12px;

  .icon {
    color: #c0c4cc;
  }
}

.comTree {
  .n-tree .n-tree-node {
    padding-left: 10px !important;
  }
}
</style>
<style scoped lang="scss">
.org {
  height: 50px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;

  span {
    height: 100%;
    flex: 1;
    line-height: 50px;
    text-align: center;
    cursor: pointer;
  }

  .active_span {
    font-size: 18px;
    font-weight: 700;
    border-bottom: 2px solid #000;
  }
}

.tree-cont {
  &.n-tree {
    .n-tree-node {
      padding-left: 20px !important;
    }
  }
}

:deep(.n-tree-node-content) {
  .n-tree-node-content__text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
