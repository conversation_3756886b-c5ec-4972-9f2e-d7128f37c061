<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-19 09:52:35
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-06-23 10:56:43
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/components/tree/comTree.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div
    class="bg-[#EEF7FF] w-[323px] h-full"
    style="border-radius: 5px; border-right: 1px solid #eee"
  >
    <!-- 采用menu的形式 可以自由选择-->
    <!-- <n-menu :options="menuOptions" :default-expanded-keys="defaultExpandedKeys" accordion default-value="test1" /> -->
    <!-- 采用表单树的形式 -->
    <!-- <h3 class="p-4 pb-[0]">{{ title }}</h3> -->
    <div class="flex justify-start items-center mb-4 pt-5 pl-5">
      <img src="@/assets/icon.png" style="width: 18px" alt="" />
      <div style="font-size: 16px; font-weight: 600; margin-left: 10px">
        所属单位
      </div>
    </div>
    <div style="padding-left: 15px; padding-right: 15px; margin-bottom: 5px">
      <n-input v-model:value="pattern" placeholder="搜索" clearable>
        <template #suffix>
          <BySearch :class="$style['icon']" />
        </template>
      </n-input>
    </div>
    <n-scrollbar style="height: calc(100vh - 242px)">
      <n-tree
        ref="treeRef"
        class="px-4"
        :data="treeData"
        selectable
        :pattern="pattern"
        :cancelable="false"
        block-line
        key-field="levelCode"
        label-field="text"
        :show-irrelevant-nodes="false"
        :render-label="renderLabel"
        :render-switcher-icon="renderSwitcherIconWithExpaned"
        :theme-overrides="{ nodeHeight: '40px' }"
        :default-expanded-keys="defaultExpandedkeys"
        :default-selected-keys="defaultSelectedkeys"
        :override-default-node-click-behavior="override"
        @update:selected-keys="selectedKeys"
      />
    </n-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, h, ref, watch, toRaw, nextTick } from 'vue';
import {
  CaDataStructured as structure,
  CaEnterprise as unit,
} from '@kalimahapps/vue-icons';
import { NIcon, TreeOption } from 'naive-ui';
import { BySearch } from '@kalimahapps/vue-icons';

import { icon_chevron as iconChevronForward } from './assets/index';
import Dept from './assets/dept.png';
import YeWu from './assets/yewu.png';
import JianGuan from './assets/jianguan.png';
import YangChang from '@/assets/iconImg/yanchang.png';
import AnGang from '@/assets/iconImg/angang.png';
import WyCh from '@/assets/iconImg/wych2.png';
import Zhongdanong from '@/assets/iconImg/zhongdanong.png';
import { useStore } from '@/store';
const store = useStore();
const treeRef = ref(null);
// const aa = ref(['10000'])
const pattern = ref('');
const defaultExpandedkeys = ref<string[]>([]);
const defaultSelectedkeys = ref<string[]>([]);
const emits = defineEmits(['handelChange', 'treeChange', 'treeChange2']);
const props: any = defineProps({
  title: { type: String },
  dataList: { type: Array },
});
function renderSwitcherIconWithExpaned({ option }: any) {
  if (option.root === 1) {
    return null;
  } else {
    if (option.children && option.children.length > 0) {
      return h(
        NIcon,
        {
          style: {
            width: '14px',
            height: '14px',
          },
        },
        { default: () => h(iconChevronForward) }
      );
    }
  }
}
function renderLabel(e: any) {
  // 如果节点没有子节点，不显示展开图标
  if (e.option.children && e.option.children.length === 0) {
    e.option.isLeaf = true;
    return h(
      'div',
      {
        style: {
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          padding: '10px 0 10px 0',
        },
      },
      [
        h('img', {
          style: {
            width: '16px',
            height: '16px',
            marginRight: '5px',
          },
          src:
            e.option.attributes?.orgType === '2'
              ? JianGuan
              : e.option.attributes?.orgType === '1'
                ? YeWu
                : Dept,
        }),
        h(
          'div',
          {
            style: {
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            },
            title: e.option.text,
          },
          e.option.text
        ),
      ]
    );
  } else {
    // 如果节点有子节点，正常显示
    if (e.option.root === 1) {
      // e.option.isLeaf = true;
      return h(
        'div',
        {
          style: {
            display: 'flex',
            alignItems: 'center',
            padding: '10px 0 10px 0',
          },
        },
        [
          h('img', {
            style: {
              width: '16px',
              height: '16px',
              marginRight: '10px',
              // marginLeft: '24px',
            },
            src: store.userInfo.logoPicUrl,
          }),
          h(
            'div',
            {
              style: {
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              },
              title: e.option.text,
            },
            e.option.text
          ),
        ]
      );
    } else {
      return h(
        'div',
        {
          style: {
            display: 'flex',
            alignItems: 'center',
            padding: '10px 0 10px 0',
            marginLeft: '-10px',
          },
        },
        [
          h('img', {
            style: {
              width: '16px',
              height: '16px',
              marginRight: '5px',
            },
            src:
              e.option.attributes?.orgType === '2'
                ? JianGuan
                : e.option.attributes?.orgType === '1'
                  ? YeWu
                  : Dept,
          }),
          h(
            'div',
            {
              style: {
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              },
              title: e.option.text,
            },
            e.option.text
          ),
        ]
      );
    }
  }
}
const treeData: any = ref([]);

function selectedKeys(array: Array<any>, option: any) {
  emits('treeChange', array[0], option[0].id);
}

function calleArr(array: Array<any>) {
  var data = array;
  if (data[0].children?.length) {
    calleArr(data[0].children);
  } else {
  }
}

function renderPrefix(info: {
  option: TreeOption;
  checked: boolean;
  selected: boolean;
}) {
  function traverseOptions(options: any): any {
    for (const key in options) {
      let value = options[key]; // 获取对应的值
      if (key === 'attributes') {
        if (value && typeof value === 'object' && 'orgType' in value) {
          if (value.orgType === '0') {
            return h(NIcon, null, {
              default: () =>
                h('img', {
                  src: Dept,
                }),
            }); // 返回 unit 组件
          } else if (value.orgType === '1') {
            return h(NIcon, null, {
              default: () =>
                h('img', {
                  src: YeWu,
                }),
            }); // 返回 unit 组件
          } else if (value.orgType === '2') {
            return h(NIcon, null, {
              default: () =>
                h('img', {
                  src: JianGuan,
                }),
            }); // 返回 unit 组件
          }
        } else {
          // console.log(`"attributes" 键存在，但没有 orgType 属性`);
        }
      }

      // 处理 children 数组或嵌套对象
      if (Array.isArray(value)) {
        for (const item of value) {
          const result = traverseOptions(item);
          if (result) {
            return result; // 找到后返回结果
          }
        }
      } else if (typeof value === 'object' && value !== null) {
        const result = traverseOptions(value);
        if (result) {
          return result; // 找到后返回结果
        }
      }
    }
    return null; // 如果没有找到，返回 null
  }

  return traverseOptions(info.option) || null; // 调用递归函数
}
function override({ option }: { option: TreeOption }) {
  const op: any = option;
  // console.log("🚀 ~ override ~ option:", option);
  // localStorage.setItem('_riskUnitID', op.id);
  // localStorage.setItem('_riskUnitName', op.text);
  emits('handelChange', option);
}
watch(
  () => props.dataList,
  (nv) => {
    if (props.dataList?.length) {
      let curKey = props.dataList[0].levelCode as string;
      defaultExpandedkeys.value = [curKey];
      defaultSelectedkeys.value.push(curKey);
      treeData.value = props.dataList;
      treeData.value[0].root = 1;
      nextTick(() => {
        const firstNodeSwitcher = treeRef.value.selfElRef.querySelector(
          '.n-tree-node-switcher'
        );
        if (firstNodeSwitcher) {
          // 设置display为none
          firstNodeSwitcher.style.display = 'none';
        }
      });
      emits('handelChange', props.dataList[0]);
      // emits('treeChange', props.dataList[0].levelCode);
      calleArr(props.dataList);
    }
  },
  { immediate: true }
);
onMounted(() => {});

defineOptions({ name: 'comTree' });
</script>

<style module lang="scss">
:global(.n-tree) {
  width: 100%;
  height: 100%;
  overflow: auto;
}
.icon {
  color: #c0c4cc;
}
</style>
