<!--
该组件 为复制 components/tree 组件样式微调整
-->
<template>
  <!-- <div :class="[$style.treeInput]">
    <n-input v-model:value="pattern" placeholder="搜索">
      <template #suffix>
        <BySearch :class="$style['icon']" />
      </template>
    </n-input>
  </div> -->

  <!-- <div class="org">选择组织</div> -->
  <!-- <div style="height: 50px"></div> -->
  <div :class="[$style.comTree]" class="h-[calc(100%-150px)] overflow-auto">
    <n-tree
      ref="treeRef"
      class="tree-cont"
      :show-irrelevant-nodes="false"
      :pattern="pattern"
      :data="treeData"
      :cancelable="false"
      block-line
      :render-label="renderLabel"
      :render-switcher-icon="renderSwitcherIconWithExpaned"
      :default-expanded-keys="defaultExpandedkeys"
      :default-selected-keys="store.userInfo.unitId"
      key-field="id"
      label-field="text"
      size="large"
      :theme-overrides="{ nodeHeight: '40px' }"
      @update:selected-keys="selectedKeys"
    />
  </div>
</template>

<script lang="ts" setup>
import AnGang from '@/assets/iconImg/angang.png';
import YangChang from '@/assets/iconImg/yanchang.png';
import Zhongdanong from '@/assets/iconImg/zhongdanong.png';
import { useStore } from '@/store';
import { NIcon, TreeOption } from 'naive-ui';
import { h, nextTick, ref, watch } from 'vue';
import Dept from './assets/dept.png';
import { icon_chevron as iconChevronForward } from './assets/index';
import JianGuan from './assets/jianguan.png';
import YeWu from './assets/yewu.png';

const emits = defineEmits(['action', 'update']);
const pattern = ref('');
const store = useStore();
const treeRef = ref(null);
const defaultExpandedkeys = ref<string[]>([]);
const defaultSelectedkeys = ref<string[]>([]);
type listT = {
  data: TreeOption[];
};
const props: any = defineProps({
  parentOrgCode: {
    type: String,
  },
  data: {
    type: Array,
    required: true,
  },
});
function selectedKeys(array: Array<any>, option: any) {
  emits('update', option[0]);
}
const treeData = ref([]);
watch(
  () => props.data,
  (nv) => {
    if (props.data?.length) {
      let curKey = props.data[0].id as string;
      defaultExpandedkeys.value = [curKey];
      treeData.value = props.data;
      treeData.value[0].root = 1;
      nextTick(() => {
        const firstNodeSwitcher = treeRef.value.selfElRef.querySelector(
          '.n-tree-node-switcher'
        );
        if (firstNodeSwitcher) {
          // 设置display为none
          firstNodeSwitcher.style.display = 'none';
        }
      });
      calleArr(props.data);
    }
  },
  { immediate: true }
);

function calleArr(array: Array<any>) {
  var data = array;
  if (data[0].children?.length) {
    calleArr(data[0].children);
  } else {
    // defaultSelectedkeys.value.push(data[0].id);
    // localStorage.setItem('_riskUnitID', data[0].id);
    // localStorage.setItem('_riskUnitName', data[0].text);
  }
}

function override({ option }: { option: TreeOption }) {
  emits('update', option.id);
}

function renderSwitcherIconWithExpaned({ option }: any) {
  if (option.root === 1) {
    return null;
  } else {
    if (option.children && option.children.length > 0) {
      return h(
        NIcon,
        {
          style: {
            width: '14px',
            height: '14px',
          },
        },
        { default: () => h(iconChevronForward) }
      );
    }
  }
}
function renderLabel(e: any) {
  // 如果节点没有子节点，不显示展开图标
  if (e.option.children && e.option.children.length === 0) {
    e.option.isLeaf = true;
    return h(
      'div',
      {
        style: {
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          padding: '10px 0 10px 0',
        },
      },
      [
        h('img', {
          style: {
            width: '16px',
            height: '16px',
            marginRight: '5px',
          },
          src:
            e.option.attributes?.orgType === '2'
              ? JianGuan
              : e.option.attributes?.orgType === '1'
                ? YeWu
                : Dept,
        }),
        h(
          'div',
          {
            style: {
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            },
            title: e.option.text,
          },
          e.option.text
        ),
      ]
    );
  } else {
    // 如果节点有子节点，正常显示
    if (e.option.root === 1) {
      // e.option.isLeaf = true;
      return h(
        'div',
        {
          style: {
            display: 'flex',
            alignItems: 'center',
            padding: '10px 0 10px 0',
          },
        },
        [
          h('img', {
            style: {
              width: '16px',
              height: '16px',
              marginRight: '10px',
              // marginLeft: '24px',
            },
            src: store.userInfo.logoPicUrl,
          }),
          h(
            'div',
            {
              style: {
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              },
              title: e.option.text,
            },
            e.option.text
          ),
        ]
      );
    } else {
      return h(
        'div',
        {
          style: {
            display: 'flex',
            alignItems: 'center',
            padding: '10px 0 10px 0',
            marginLeft: '-10px',
          },
        },
        [
          h('img', {
            style: {
              width: '16px',
              height: '16px',
              marginRight: '5px',
            },
            src:
              e.option.attributes?.orgType === '2'
                ? JianGuan
                : e.option.attributes?.orgType === '1'
                  ? YeWu
                  : Dept,
          }),
          h(
            'div',
            {
              style: {
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              },
              title: e.option.text,
            },
            e.option.text
          ),
        ]
      );
    }
  }
}
// function renderPrefix(info: {
//   option: TreeOption;
//   checked: boolean;
//   selected: boolean;
// }) {
//   function traverseOptions(options: any): any {
//     for (const key in options) {
//       let value = options[key]; // 获取对应的值
//       if (key === 'attributes') {
//         if (value && typeof value === 'object' && 'orgType' in value) {
//           if (value.orgType === '0') {
//             return h(NIcon, null, {
//               default: () =>
//                 h('img', {
//                   src: Dept,
//                 }),
//             }); // 返回 unit 组件
//           } else if (value.orgType === '1') {
//             return h(NIcon, null, {
//               default: () =>
//                 h('img', {
//                   src: YeWu,
//                 }),
//             }); // 返回 unit 组件
//           } else if (value.orgType === '2') {
//             return h(NIcon, null, {
//               default: () =>
//                 h('img', {
//                   src: JianGuan,
//                 }),
//             }); // 返回 unit 组件
//           }
//         } else {
//           // console.log(`"attributes" 键存在，但没有 orgType 属性`);
//         }
//       }

//       // 处理 children 数组或嵌套对象
//       if (Array.isArray(value)) {
//         for (const item of value) {
//           const result = traverseOptions(item);
//           if (result) {
//             return result; // 找到后返回结果
//           }
//         }
//       } else if (typeof value === 'object' && value !== null) {
//         const result = traverseOptions(value);
//         if (result) {
//           return result; // 找到后返回结果
//         }
//       }
//     }
//     return null; // 如果没有找到，返回 null
//   }

//   return traverseOptions(info.option) || null; // 调用递归函数
// }

defineOptions({ name: 'ComRadioTabE' });
</script>

<style module lang="scss">
.treeInput {
  margin: 16px 12px 12px;

  .icon {
    color: #c0c4cc;
  }
}

.comTree {
  .n-tree .n-tree-node {
    padding-left: 10px !important;
  }
}
</style>
<style scoped lang="scss">
.org {
  height: 50px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}
.tree-cont {
  &.n-tree {
    .n-tree-node {
      padding-left: 20px !important;
    }
  }
}

/* 定制滚动条整体 */
::-webkit-scrollbar {
  width: 4px; /* 宽度 */
}

/* 定制滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: #f1f1f1; /* 轨道颜色 */
}

/* 定制滚动条滑块 */
::-webkit-scrollbar-thumb {
  background-color: #888; /* 滑块颜色 */
  border-radius: 6px; /* 圆角 */
}

/* 滑块在鼠标悬停时改变颜色 */
::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}
</style>
