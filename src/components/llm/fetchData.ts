/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-15 14:50:52
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-19 15:22:47
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/views/personManage/fetchData.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import { $http } from '@tanzerfe/http';
import { api } from '@/api';

export function serviceLogin(params: any) {
  const url = api.getUrl('ehs-clnt-platform-service', api.name.interface.serviceLogin, params);
  return $http.post<any>(url, {
    data: { _cfg: { showTip: false } },
  });
}
