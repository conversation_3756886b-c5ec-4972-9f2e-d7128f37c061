<template>
  <el-drawer v-model="showDrawer" modal-class="llm-drawer" title="智能推荐">
    <div class="content">
      <div class="markdown-container">
        <div v-if="isPending" class="loader"></div>
        <!-- 使用v-html渲染净化后的HTML -->
        <div
          ref="contentEl"
          class="markdown-content"
          v-html="processedContent"
        />
        <!-- 加载状态指示 -->
        <div v-if="isLoading" class="typing-indicator">
          <div class="dot-flashing" />
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { marked } from 'marked';
import { serviceLogin } from './fetchData';
import { useLLMStore, useStore } from '@/store';
import hljs from 'highlight.js';
import DOMPurify from 'dompurify';

import config from '@/config/index';

defineOptions({ name: 'LLMDrawer' });

const userInfo = useStore();
const LLMStore = useLLMStore().value;

const emits = defineEmits(['update:show']);

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  quession: {
    type: String,
    default: '',
  },
});
const showDrawer = computed({
  get: () => props.show,
  set: (val) => {
    emits('update:show', val);
  },
});

const tips =
  '以下智能推荐内容，是系统基于大模型技术，结合 王英琦 同志历史培训记录和作业行为数据自动生成的自主提升学习内容，已推送至移动端教育培训- 岗位微练模块，并通知该员工进行学习。';

// 初始化Markdown解析器
marked.setOptions({
  highlight: (code, lang) => {
    const language = hljs.getLanguage(lang) ? lang : 'plaintext';
    return hljs.highlight(code, { language }).value;
  },
  gfm: true, // 启用GitHub风格Markdown
  breaks: true, // 单换行转为<br>
  pedantic: false, // 关闭严格模式
  smartLists: true, // 智能列表检测
});

// 响应式数据
const rawContent = ref('');
const processedContent = ref('');
const isPending = ref(true);
const isLoading = ref(false);
const contentEl = ref(null);

// 处理Markdown内容
const processMarkdown = (content) => {
  // 1. 解析Markdown为HTML
  const rawHtml = marked.parse(content);
  // 2. 安全净化
  const cleanHtml = DOMPurify.sanitize(rawHtml, {
    ADD_ATTR: ['target'], // 允许链接的target属性
  });
  // 3. 添加高亮类名
  return cleanHtml.replace(
    /<code class="language-(.*?)">/g,
    '<code class="hljs language-$1">'
  );
};

const getLLMAnswer = async (quession: string, flag: boolean) => {
  console.log(flag, '>>>>');
  if (flag) {
    await setLLMToken();

    await processLine(
      'https://test-bw.gsafetycloud.com' +
        config.chat_service +
        '/bmChat/tyqwTalking',
      {
        method: 'post',
        body: JSON.stringify({ content: quession }),
        headers: {
          'Content-Type': 'application/json',
          'Client-Type': 'web',
          Phone: userInfo.userInfo.userTelphone ?? '',
          UserToken: tokenn.value,
        },
      },
      (res: string) => {
        isPending.value = false;
        isLoading.value = true;
        try {
          const { data } = JSON.parse(res);
          console.log('🚀 ~ postQuestion ~ data:', data);
          if (data) {
            rawContent.value += data;
          }
        } catch (error: any) {
          console.log('🚀 ~ postQuestion ~ error:', error);
        }
      }
    );
  } else {
    isPending.value = false;
    isLoading.value = true;
    rawContent.value += quession;
  }

  isLoading.value = false;
};

async function* makeTextFileLineIterator(url, params) {
  const utf8Decoder = new TextDecoder('utf-8');
  const response = await fetch(url, params);
  const reader = response?.body?.getReader();
  let { value: chunk, done: readerDone } = await reader.read();
  chunk = chunk ? utf8Decoder.decode(chunk, { stream: true }) : '';
  const re = /\r\n|\n|\r/gm;
  let startIndex = 0;
  while (true) {
    const result = re.exec(chunk);
    if (!result) {
      if (readerDone) {
        break;
      }
      const remainder = chunk.substr(startIndex);
      ({ value: chunk, done: readerDone } = await reader.read());
      chunk =
        remainder + (chunk ? utf8Decoder.decode(chunk, { stream: true }) : '');
      startIndex = re.lastIndex = 0;
      continue;
    }
    yield chunk.substring(startIndex, result.index);
    startIndex = re.lastIndex;
  }
  if (startIndex < chunk.length) {
    yield chunk.substr(startIndex);
  }
}
const processLine = async (url, params, cb) => {
  for await (const line of makeTextFileLineIterator(url, params)) {
    cb(line);
  }
};

// 内容变化时触发处理
watch(rawContent, (newVal) => {
  processedContent.value = processMarkdown(newVal);
  // 滚动到底部
  nextTick(() => {
    if (contentEl.value) {
      document.querySelector('.el-drawer__body').scrollTo({
        top: contentEl.value.scrollHeight,
        behavior: 'smooth',
      });
    }
  });
});
const tokenn = ref('');
const setLLMToken = async () => {
  console.log(userInfo.userInfo.userTelphone, 'userInfo');
  let res = await serviceLogin({ telPhone: userInfo.userInfo.userTelphone });
  LLMStore.token = res.data;
  tokenn.value = res.data;
};

// 自动高亮初始化
onMounted(() => {
  // 监听DOM更新后重新高亮
  const observer = new MutationObserver(() => {
    hljs.highlightAll();
  });
  if (contentEl.value) {
    observer.observe(contentEl.value, {
      childList: true,
      subtree: true,
    });
  }
});

defineExpose({ getLLMAnswer });
// watch(
//   () => props.quession,
//   (v) => {
//     if (!v) return;
//     nextTick(() => {
//       getLLMAnswer(v);
//     });
//   },
//   {
//     immediate: true,
//   }
// );
</script>

<style scoped lang="scss">
/* 引入高亮主题 */
@import 'highlight.js/styles/github-dark.css';

.llm-drawer {
  width: 1000px;

  :deep(.el-drawer__header) {
    margin-bottom: 0;
  }
}

.tips {
  @apply text-[14px] text-[#fff] p-[10px] bg-[#d4d4d4];
  border-radius: 8px;

  .tips-text {
    @apply leading-normal mt-[8px];
    text-indent: 2em;
  }
}

.loader {
  --w: 40ch;
  font-size: 16px;
  line-height: 1.4em;
  letter-spacing: var(--w);
  width: var(--w);
  overflow: hidden;
  white-space: nowrap;
  word-break: break-all;
  color: #0000;
  text-shadow:
    calc(0 * var(--w)) 0 #0d1c2e,
    calc(-1 * var(--w)) 0 #0d1c2e,
    calc(-2 * var(--w)) 0 #0d1c2e,
    calc(-3 * var(--w)) 0 #0d1c2e,
    calc(-4 * var(--w)) 0 #0d1c2e,
    calc(-5 * var(--w)) 0 #0d1c2e,
    calc(-6 * var(--w)) 0 #0d1c2e,
    calc(-7 * var(--w)) 0 #0d1c2e,
    calc(-8 * var(--w)) 0 #0d1c2e,
    calc(-9 * var(--w)) 0 #0d1c2e,
    calc(-10 * var(--w)) 0 #0d1c2e,
    calc(-11 * var(--w)) 0 #0d1c2e,
    calc(-12 * var(--w)) 0 #0d1c2e,
    calc(-13 * var(--w)) 0 #0d1c2e,
    calc(-14 * var(--w)) 0 #0d1c2e;
  animation: l20 2s infinite linear;
}

.loader:before {
  content: '正在为您生成答案......';
}

@keyframes l20 {
  6% {
    text-shadow:
      calc(0 * var(--w)) -10px #0d1c2e,
      calc(-1 * var(--w)) 0 #0d1c2e,
      calc(-2 * var(--w)) 0 #0d1c2e,
      calc(-3 * var(--w)) 0 #0d1c2e,
      calc(-4 * var(--w)) 0 #0d1c2e,
      calc(-5 * var(--w)) 0 #0d1c2e,
      calc(-6 * var(--w)) 0 #0d1c2e,
      calc(-7 * var(--w)) 0 #0d1c2e,
      calc(-8 * var(--w)) 0 #0d1c2e,
      calc(-9 * var(--w)) 0 #0d1c2e,
      calc(-10 * var(--w)) 0 #0d1c2e,
      calc(-11 * var(--w)) 0 #0d1c2e,
      calc(-12 * var(--w)) 0 #0d1c2e,
      calc(-13 * var(--w)) 0 #0d1c2e,
      calc(-14 * var(--w)) 0 #0d1c2e;
  }

  12% {
    text-shadow:
      calc(0 * var(--w)) 0 #0d1c2e,
      calc(-1 * var(--w)) -10px #0d1c2e,
      calc(-2 * var(--w)) 0 #0d1c2e,
      calc(-3 * var(--w)) 0 #0d1c2e,
      calc(-4 * var(--w)) 0 #0d1c2e,
      calc(-5 * var(--w)) 0 #0d1c2e,
      calc(-6 * var(--w)) 0 #0d1c2e,
      calc(-7 * var(--w)) 0 #0d1c2e,
      calc(-8 * var(--w)) 0 #0d1c2e,
      calc(-9 * var(--w)) 0 #0d1c2e,
      calc(-10 * var(--w)) 0 #0d1c2e,
      calc(-11 * var(--w)) 0 #0d1c2e,
      calc(-12 * var(--w)) 0 #0d1c2e,
      calc(-13 * var(--w)) 0 #0d1c2e,
      calc(-14 * var(--w)) 0 #0d1c2e;
  }

  18% {
    text-shadow:
      calc(0 * var(--w)) 0 #0d1c2e,
      calc(-1 * var(--w)) 0 #0d1c2e,
      calc(-2 * var(--w)) -10px #0d1c2e,
      calc(-3 * var(--w)) 0 #0d1c2e,
      calc(-4 * var(--w)) 0 #0d1c2e,
      calc(-5 * var(--w)) 0 #0d1c2e,
      calc(-6 * var(--w)) 0 #0d1c2e,
      calc(-7 * var(--w)) 0 #0d1c2e,
      calc(-8 * var(--w)) 0 #0d1c2e,
      calc(-9 * var(--w)) 0 #0d1c2e,
      calc(-10 * var(--w)) 0 #0d1c2e,
      calc(-11 * var(--w)) 0 #0d1c2e,
      calc(-12 * var(--w)) 0 #0d1c2e,
      calc(-13 * var(--w)) 0 #0d1c2e,
      calc(-14 * var(--w)) 0 #0d1c2e;
  }

  24% {
    text-shadow:
      calc(0 * var(--w)) 0 #0d1c2e,
      calc(-1 * var(--w)) 0 #0d1c2e,
      calc(-2 * var(--w)) 0 #0d1c2e,
      calc(-3 * var(--w)) -10px #0d1c2e,
      calc(-4 * var(--w)) 0 #0d1c2e,
      calc(-5 * var(--w)) 0 #0d1c2e,
      calc(-6 * var(--w)) 0 #0d1c2e,
      calc(-7 * var(--w)) 0 #0d1c2e,
      calc(-8 * var(--w)) 0 #0d1c2e,
      calc(-9 * var(--w)) 0 #0d1c2e,
      calc(-10 * var(--w)) 0 #0d1c2e,
      calc(-11 * var(--w)) 0 #0d1c2e,
      calc(-12 * var(--w)) 0 #0d1c2e,
      calc(-13 * var(--w)) 0 #0d1c2e,
      calc(-14 * var(--w)) 0 #0d1c2e;
  }

  30% {
    text-shadow:
      calc(0 * var(--w)) 0 #0d1c2e,
      calc(-1 * var(--w)) 0 #0d1c2e,
      calc(-2 * var(--w)) 0 #0d1c2e,
      calc(-3 * var(--w)) 0 #0d1c2e,
      calc(-4 * var(--w)) -10px #0d1c2e,
      calc(-5 * var(--w)) 0 #0d1c2e,
      calc(-6 * var(--w)) 0 #0d1c2e,
      calc(-7 * var(--w)) 0 #0d1c2e,
      calc(-8 * var(--w)) 0 #0d1c2e,
      calc(-9 * var(--w)) 0 #0d1c2e,
      calc(-10 * var(--w)) 0 #0d1c2e,
      calc(-11 * var(--w)) 0 #0d1c2e,
      calc(-12 * var(--w)) 0 #0d1c2e,
      calc(-13 * var(--w)) 0 #0d1c2e,
      calc(-14 * var(--w)) 0 #0d1c2e;
  }

  36% {
    text-shadow:
      calc(0 * var(--w)) 0 #0d1c2e,
      calc(-1 * var(--w)) 0 #0d1c2e,
      calc(-2 * var(--w)) 0 #0d1c2e,
      calc(-3 * var(--w)) 0 #0d1c2e,
      calc(-4 * var(--w)) 0 #0d1c2e,
      calc(-5 * var(--w)) -10px #0d1c2e,
      calc(-6 * var(--w)) 0 #0d1c2e,
      calc(-7 * var(--w)) 0 #0d1c2e,
      calc(-8 * var(--w)) 0 #0d1c2e,
      calc(-9 * var(--w)) 0 #0d1c2e,
      calc(-10 * var(--w)) 0 #0d1c2e,
      calc(-11 * var(--w)) 0 #0d1c2e,
      calc(-12 * var(--w)) 0 #0d1c2e,
      calc(-13 * var(--w)) 0 #0d1c2e,
      calc(-14 * var(--w)) 0 #0d1c2e;
  }

  42% {
    text-shadow:
      calc(0 * var(--w)) 0 #0d1c2e,
      calc(-1 * var(--w)) 0 #0d1c2e,
      calc(-2 * var(--w)) 0 #0d1c2e,
      calc(-3 * var(--w)) 0 #0d1c2e,
      calc(-4 * var(--w)) 0 #0d1c2e,
      calc(-5 * var(--w)) 0 #0d1c2e,
      calc(-6 * var(--w)) -10px #0d1c2e,
      calc(-7 * var(--w)) 0 #0d1c2e,
      calc(-8 * var(--w)) 0 #0d1c2e,
      calc(-9 * var(--w)) 0 #0d1c2e,
      calc(-10 * var(--w)) 0 #0d1c2e,
      calc(-11 * var(--w)) 0 #0d1c2e,
      calc(-12 * var(--w)) 0 #0d1c2e,
      calc(-13 * var(--w)) 0 #0d1c2e,
      calc(-14 * var(--w)) 0 #0d1c2e;
  }

  48% {
    text-shadow:
      calc(0 * var(--w)) 0 #0d1c2e,
      calc(-1 * var(--w)) 0 #0d1c2e,
      calc(-2 * var(--w)) 0 #0d1c2e,
      calc(-3 * var(--w)) 0 #0d1c2e,
      calc(-4 * var(--w)) 0 #0d1c2e,
      calc(-5 * var(--w)) 0 #0d1c2e,
      calc(-6 * var(--w)) 0 #0d1c2e,
      calc(-7 * var(--w)) -10px #0d1c2e,
      calc(-8 * var(--w)) 0 #0d1c2e,
      calc(-9 * var(--w)) 0 #0d1c2e,
      calc(-10 * var(--w)) 0 #0d1c2e,
      calc(-11 * var(--w)) 0 #0d1c2e,
      calc(-12 * var(--w)) 0 #0d1c2e,
      calc(-13 * var(--w)) 0 #0d1c2e,
      calc(-14 * var(--w)) 0 #0d1c2e;
  }

  54% {
    text-shadow:
      calc(0 * var(--w)) 0 #0d1c2e,
      calc(-1 * var(--w)) 0 #0d1c2e,
      calc(-2 * var(--w)) 0 #0d1c2e,
      calc(-3 * var(--w)) 0 #0d1c2e,
      calc(-4 * var(--w)) 0 #0d1c2e,
      calc(-5 * var(--w)) 0 #0d1c2e,
      calc(-6 * var(--w)) 0 #0d1c2e,
      calc(-7 * var(--w)) 0 #0d1c2e,
      calc(-8 * var(--w)) -10px #0d1c2e,
      calc(-9 * var(--w)) 0 #0d1c2e,
      calc(-10 * var(--w)) 0 #0d1c2e,
      calc(-11 * var(--w)) 0 #0d1c2e,
      calc(-12 * var(--w)) 0 #0d1c2e,
      calc(-13 * var(--w)) 0 #0d1c2e,
      calc(-14 * var(--w)) 0 #0d1c2e;
  }

  60% {
    text-shadow:
      calc(0 * var(--w)) 0 #0d1c2e,
      calc(-1 * var(--w)) 0 #0d1c2e,
      calc(-2 * var(--w)) 0 #0d1c2e,
      calc(-3 * var(--w)) 0 #0d1c2e,
      calc(-4 * var(--w)) 0 #0d1c2e,
      calc(-5 * var(--w)) 0 #0d1c2e,
      calc(-6 * var(--w)) 0 #0d1c2e,
      calc(-7 * var(--w)) 0 #0d1c2e,
      calc(-8 * var(--w)) 0 #0d1c2e,
      calc(-9 * var(--w)) -10px #0d1c2e,
      calc(-10 * var(--w)) 0 #0d1c2e,
      calc(-11 * var(--w)) 0 #0d1c2e,
      calc(-12 * var(--w)) 0 #0d1c2e,
      calc(-13 * var(--w)) 0 #0d1c2e,
      calc(-14 * var(--w)) 0 #0d1c2e;
  }

  66% {
    text-shadow:
      calc(0 * var(--w)) 0 #0d1c2e,
      calc(-1 * var(--w)) 0 #0d1c2e,
      calc(-2 * var(--w)) 0 #0d1c2e,
      calc(-3 * var(--w)) 0 #0d1c2e,
      calc(-4 * var(--w)) 0 #0d1c2e,
      calc(-5 * var(--w)) 0 #0d1c2e,
      calc(-6 * var(--w)) 0 #0d1c2e,
      calc(-7 * var(--w)) 0 #0d1c2e,
      calc(-8 * var(--w)) 0 #0d1c2e,
      calc(-9 * var(--w)) 0 #0d1c2e,
      calc(-10 * var(--w)) -10px #0d1c2e,
      calc(-11 * var(--w)) 0 #0d1c2e,
      calc(-12 * var(--w)) 0 #0d1c2e,
      calc(-13 * var(--w)) 0 #0d1c2e,
      calc(-14 * var(--w)) 0 #0d1c2e;
  }

  72% {
    text-shadow:
      calc(0 * var(--w)) 0 #0d1c2e,
      calc(-1 * var(--w)) 0 #0d1c2e,
      calc(-2 * var(--w)) 0 #0d1c2e,
      calc(-3 * var(--w)) 0 #0d1c2e,
      calc(-4 * var(--w)) 0 #0d1c2e,
      calc(-5 * var(--w)) 0 #0d1c2e,
      calc(-6 * var(--w)) 0 #0d1c2e,
      calc(-7 * var(--w)) 0 #0d1c2e,
      calc(-8 * var(--w)) 0 #0d1c2e,
      calc(-9 * var(--w)) 0 #0d1c2e,
      calc(-10 * var(--w)) 0 #0d1c2e,
      calc(-11 * var(--w)) -10px #0d1c2e,
      calc(-12 * var(--w)) 0 #0d1c2e,
      calc(-13 * var(--w)) 0 #0d1c2e,
      calc(-14 * var(--w)) 0 #0d1c2e;
  }

  78% {
    text-shadow:
      calc(0 * var(--w)) 0 #0d1c2e,
      calc(-1 * var(--w)) 0 #0d1c2e,
      calc(-2 * var(--w)) 0 #0d1c2e,
      calc(-3 * var(--w)) 0 #0d1c2e,
      calc(-4 * var(--w)) 0 #0d1c2e,
      calc(-5 * var(--w)) 0 #0d1c2e,
      calc(-6 * var(--w)) 0 #0d1c2e,
      calc(-7 * var(--w)) 0 #0d1c2e,
      calc(-8 * var(--w)) 0 #0d1c2e,
      calc(-9 * var(--w)) 0 #0d1c2e,
      calc(-10 * var(--w)) 0 #0d1c2e,
      calc(-11 * var(--w)) 0 #0d1c2e,
      calc(-12 * var(--w)) -10px #0d1c2e,
      calc(-13 * var(--w)) 0 #0d1c2e,
      calc(-14 * var(--w)) 0 #0d1c2e;
  }

  84% {
    text-shadow:
      calc(0 * var(--w)) 0 #0d1c2e,
      calc(-1 * var(--w)) 0 #0d1c2e,
      calc(-2 * var(--w)) 0 #0d1c2e,
      calc(-3 * var(--w)) 0 #0d1c2e,
      calc(-4 * var(--w)) 0 #0d1c2e,
      calc(-5 * var(--w)) 0 #0d1c2e,
      calc(-6 * var(--w)) 0 #0d1c2e,
      calc(-7 * var(--w)) 0 #0d1c2e,
      calc(-8 * var(--w)) 0 #0d1c2e,
      calc(-9 * var(--w)) 0 #0d1c2e,
      calc(-10 * var(--w)) 0 #0d1c2e,
      calc(-11 * var(--w)) 0 #0d1c2e,
      calc(-12 * var(--w)) 0 #0d1c2e,
      calc(-13 * var(--w)) -10px #0d1c2e,
      calc(-14 * var(--w)) 0 #0d1c2e;
  }

  90% {
    text-shadow:
      calc(0 * var(--w)) 0 #0d1c2e,
      calc(-1 * var(--w)) 0 #0d1c2e,
      calc(-2 * var(--w)) 0 #0d1c2e,
      calc(-3 * var(--w)) 0 #0d1c2e,
      calc(-4 * var(--w)) 0 #0d1c2e,
      calc(-5 * var(--w)) 0 #0d1c2e,
      calc(-6 * var(--w)) 0 #0d1c2e,
      calc(-7 * var(--w)) 0 #0d1c2e,
      calc(-8 * var(--w)) 0 #0d1c2e,
      calc(-9 * var(--w)) 0 #0d1c2e,
      calc(-10 * var(--w)) 0 #0d1c2e,
      calc(-11 * var(--w)) 0 #0d1c2e,
      calc(-12 * var(--w)) 0 #0d1c2e,
      calc(-13 * var(--w)) 0 #0d1c2e,
      calc(-14 * var(--w)) -10px #0d1c2e;
  }
}

.markdown-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.markdown-content {
  line-height: 1.6;
}

.markdown-content h1 {
  border-bottom: 2px solid #666;
  padding-bottom: 0.3em;
}

.markdown-content pre {
  background: #1f1f1f;
  padding: 1em;
  border-radius: 8px;
  overflow-x: auto;
}

.markdown-content code:not(.hljs) {
  background: #f0f0f0;
  padding: 0.2em 0.4em;
  border-radius: 3px;
}

.typing-indicator {
  padding: 20px 0;
  text-align: center;
}

.dot-flashing {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #999;
  animation: dot-flashing 1s infinite linear alternate;
  animation-delay: 0.5s;
}

.dot-flashing::before,
.dot-flashing::after {
  content: '';
  display: inline-block;
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #999;
  animation: dot-flashing 1s infinite linear alternate;
}

.dot-flashing::before {
  left: -15px;
  animation-delay: 0s;
}

.dot-flashing::after {
  left: 15px;
  animation-delay: 1s;
}

@keyframes dot-flashing {
  0% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}
</style>
