<template>
  <n-upload
    v-if="isRender"
    :action="actionURL"
    :accept="accept"
    :max="max"
    :size="size"
    :default-file-list="previewList"
    list-type="image-card"
    :on-update:file-list="handleUpdate"
    :on-before-upload="handleBeforeUpload"
    :on-finish="handleUploadFinish"
    :on-remove="handleRemove"
    @preview="handlePreview"
  />
  <n-modal v-model:show="showModal" preset="card" style="width: 600px; height: 600px">
    <!-- <img :src="previewImageUrl" style="width: 100%; height: 100%" /> -->
    <div class="img-item-box">
      <n-image class="img-item" :src="previewImageUrl" object-fit="contain" />
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import { $toast } from '@/common/shareContext/useToastCtx';
import { api } from '@/api';
import { UploadFileInfo } from 'naive-ui';

interface Props {
  data: any[];
  mode?: string; // detail为展示视频 其他为上传视频
  max?: number; // 限制上传数量
  size?: number; // 限制文件大小
  accept?: string; // 文件类型
  tips?: string; // 上传提示
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  data: () => [],
  max: 5,
  accept: '.png, .jpg, .jpeg',
});

const emits = defineEmits(['update']);

const actionURL = api.getUrl(api.type.server, api.name.file.uploadFile2);

const fileResMap: Map<string, string> = new Map(); // 上传结果map

const showModal = ref(false);
const previewImageUrl = ref('');

function handlePreview(file: UploadFileInfo) {
  const { id } = file;
  const _imgUrl = window.$SYS_CFG.apiPreviewURL + fileResMap.get(id);
  previewImageUrl.value = _imgUrl;
  showModal.value = true;
}

const isRender = ref(true);
const previewList = ref<any[]>([]);
// 监听 props.data 的变化
watch(
  () => props.data,
  (value) => {
    if (value.length < 1) return;
    isRender.value = false;
    const _list: any[] = [];
    for (const item of value) {
      _list.push({
        id: item.id,
        name: item.name,
        status: 'finished',
        url: window.$SYS_CFG.apiPreviewURL + item.address,
        res: window.$SYS_CFG.apiPreviewURL + item.address,
      });

      fileResMap.set(item.id, item.address);
    }
    previewList.value = _list;
    nextTick(() => {
      isRender.value = true;
    });
  }
);

// 上传前校验(文件大小、类型)
function handleBeforeUpload(options: { file: UploadFileInfo }) {
  const { file } = options;
  const fileExt = file.name.slice(file.name.lastIndexOf('.') + 1);
  if (!file.file) return false;
  if (!props.accept.includes(fileExt)) {
    $toast.error(`请上传 ${props.accept} 类型的文件!`);
    return false;
  }
  if (props.size && file.file.size / 1024 / 1024 > props.size) {
    $toast.error(`文件不能超过 ${props.size} MB，请重新上传!`);
    return false;
  }
  return true;
}

// 上传完成
function handleUploadFinish(options: { file: UploadFileInfo; event: ProgressEvent }) {
  const { file, event } = options;
  const { data } = JSON.parse((<any>event?.target)?.responseText || '{}');
  fileResMap.set(file.id, data);
}

// 当文件数组改变时触发的回调函数
function handleUpdate(val: UploadFileInfo[]) {
  const finishedList = val.filter((item) => item.status === 'finished');
  const res: any[] = [];
  const data: any[] = [];
  finishedList.forEach((item) => {
    const _res = fileResMap.get(item.id) || null;
    res.push(_res);
    data.push(Object.assign(item, { res: _res }));
  });

  return emits('update', res, data);
}
// 删除附件
const handleRemove = (data: { file: any; arr: any }) => {
  const { file } = data;
  fileResMap.delete(file.id);
};
</script>

<style scoped lang="scss">
.img-item {
  @apply w-[100%] h-[520px] rounded-[4px] flex items-center justify-center;
}
:deep(.n-image img) {
  height: 100%;
}
</style>
