<template>
  <div class="w-full pt-[15px]">
    <n-upload
      response-type="json"
      name="file"
      :action="actionURL"
      :default-file-list="arr"
      :on-finish="handleUploadFinish"
      :on-update:file-list="handleUpdate"
      :on-before-upload="handleBeforeUpload"
      :accept="props.accept"
      :disabled="props.disabled1"
      @remove="handleRemove"
      :max="max"
    >
      <span style="margin-right: 15px">纪要附件</span>
      <n-button type="primary">添加附件</n-button>
      <div class="mt-[10px] text-[#999999]" v-if="props.tips">
        {{ props.tips }}
      </div>
    </n-upload>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted, nextTick, watchEffect } from 'vue';
import { UploadFileInfo } from 'naive-ui';
import { IUploadRes } from '@/components/upload/type';
import { api } from '@/api';
import { $toast } from '@/common/shareContext/useToastCtx';
import { Base64 } from 'js-base64';
defineOptions({ name: 'FileUpload' });
const arr: any = ref([]);

interface IItem {
  fjMc: string;
  fjCflj: string;
  fjTywysbm: string;
}
interface Props {
  data: IItem[];
  mode?: string; // detail为展示图片 其他为上传图片
  max?: number; // 限制上传数量
  size?: number; // 限制文件大小
  accept?: string; // 文件类型
  tips?: string; // 上传提示
  disabled1?: boolean; // 上传提示
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  data: () => [],
  max: 6,
  accept: '',
  title: '',
  size: 30,
  disabled1: false,
});
const emits = defineEmits(['update', 'removee']);
const valueRef = computed<any[]>(() => props.data);
const actionURL = api.getUrl(api.type.server, api.name.file.uploadFile2);

const fileResMap: Map<any, IUploadRes> = new Map(); // 上传结果map

watchEffect(() => {
  if (props.data) {
    for (const item of valueRef.value) {
      const type = item.name.split('.');
      const typeName = type[type.length - 1];
      if (typeName == 'doc' || typeName == 'docx') {
        var b64Encoded = Base64.encode(getFileURL(item.address));
        arr.value.push({
          id: item.id,
          name: item.name,
          url: window.$SYS_CFG.apiPreviewLink + encodeURIComponent(b64Encoded),

          status: 'finished',
          res: getFileURL(item.address),
        });
      } else {
        arr.value.push({
          id: item.id,
          name: item.name,
          url: getFileURL(item.address),

          status: 'finished',
          res: getFileURL(item.address),
        });
      }

      fileResMap.set(item.address, item);
      nextTick(() => {
        const divs = document.querySelectorAll('.n-upload-file-list > div');
        divs.forEach(function (div) {
          if (div.textContent == item.name) {
            div.setAttribute('title', item.name);
          }
        });
      });
    }
  }
});

// 上传前校验(文件大小、类型)
function handleBeforeUpload(options: { file: UploadFileInfo }) {
  const { file } = options;
  if (!file.file) return false;
  const fileExt = file.name.slice(file.name.lastIndexOf('.') + 1);
  if (props.accept && !props.accept.includes(fileExt)) {
    $toast.error(`请上传 ${props.accept} 类型的文件!`);
    return false;
  } else if (props.size && file.file.size / 1024 / 1024 > props.size) {
    $toast.error(`文件不能超过 ${props.size} MB，请重新上传!`);
    return false;
  }
  return true;
}
// 删除附件
const handleRemove = (data: { file: any; arr: any }) => {
  emits('removee', data.file);
};
// 上传完成
function handleUploadFinish(options: { file: UploadFileInfo; event?: ProgressEvent }) {
  const { file, event } = options;
  console.log(file, '>>>>>>>>>.');
  const data = (<any>event?.target)?.response?.data || {};

  fileResMap.set(file.id, data);

  const divs = document.querySelectorAll('.n-upload-file-list > div');
  divs.forEach(function (div) {
    if (div.textContent == file.name) {
      div.setAttribute('title', file.name);
    }
  });
}

const title = ref('');
// 当文件数组改变时触发的回调函数
function handleUpdate(val: UploadFileInfo[]) {
  const finishedList = val.filter((item) => item.status === 'finished');

  const res: any[] = [];
  const data: any[] = [];
  finishedList.forEach((item) => {
    const _res = fileResMap.get(item.id) || null;
    res.push(_res);
    data.push(Object.assign(item, { res: _res }));
  });
  if (data) {
    data.forEach((item: any) => {
      const type = item.name.split('.');
      const typeName = type[type.length - 1];
      if (typeName == 'doc' || typeName == 'docx') {
        console.log(item, '>>>');
        var b64Encoded = Base64.encode(getFileURL(item.res));
        item.url = window.$SYS_CFG.apiPreviewLink + encodeURIComponent(b64Encoded);
      } else {
        item.url = getFileURL(item.res);
      }
    });
  }

  return emits('update', res, data);
}

function getFileURL(filePath: string) {
  console.log(window.$SYS_CFG.apiPreviewURL + filePath, 'pdf');
  return window.$SYS_CFG.apiPreviewURL + filePath;
}
</script>

<style scoped></style>
