<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-15 09:18:46
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-05-30 10:57:16
 * @FilePath: /安全生产/aqsc-rygl/apps/ehs-org-alloc-mgr/src/components/tab/ComRadioTabA.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="head_box h-40px">
    <div class="flex">
      <div
        v-for="item in tabList"
        :class="['head_tab', { w_active: current2 === item.name }]"
        @click="handleUpdateValue(item)"
        :key="item.name"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ITabItem } from './type';
import { ref } from 'vue';
interface Props {
  tab: number | string;
  tabList: ITabItem[];
}

const props = defineProps({
  current: {
    type: String,
    default: '1',
  },
  tabList: {
    type: Array,
    defautl: () => [],
  },
});
const emits = defineEmits(['change']);
const current2 = ref(props.current);
console.log(current2.value);
function handleUpdateValue(name: any) {
  current2.value = name.name;
  const data = props.tabList.find((v) => v.name === name.name);
  emits('change', name.name, data);
}

defineOptions({ name: 'ComRadioTabA2' });
</script>

<style scoped lang="scss">
.head_box {
  font-weight: 400;
  font-size: 14px;
  color: #222222;
  border-radius: 4px 4px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: center;
  cursor: pointer;

  .head_tab {
    width: 130px;
    height: 40px;
    line-height: 40px;
    background: url('./tab_untive.png') no-repeat;
    background-size: 100% 100%;
    margin-left: -10px;

    &:first-child {
      text-align: start;
      padding-left: 20px;
      background: url('./first_active.png') no-repeat;
      background-size: 100% 100%;
      margin-left: 0;
    }

    &:hover {
      // color: rgba(64, 112, 255, 1);
    }
  }

  .w_active {
    width: 130px;
    height: 40px;
    line-height: 40px;
    background: url('./active.png') no-repeat;
    background-size: 100% 100%;
    color: #ffffff;
    z-index: 99;

    // border-bottom: 3px solid #4070ff;
    &:first-child {
      background: url('./tab_active.png') no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
