/*
 * @Author: xginger <EMAIL>
 * @Date: 2025-05-09 09:14:51
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-05-09 10:09:05
 * @FilePath: \ehs-org-alloc-mgr\src\main.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'hel-iso';
import * as Vue from 'vue';
import { preFetchLib, bindVueRuntime } from 'hel-micro';
import '@tanzerfe/ifm-child';
bindVueRuntime({ Vue });

async function main() {
  // remote module git: https://github.com/hel-eco/hel-tpl-remote-vue3-comps-ts
  // module consomer project git: https://github.com/hel-eco/hel-demo-use-remote-vue3-comp
  // comp preview: https://unpkg.com/hel-tpl-remote-vue3-comps-ts/hel_dist/index.html
  // await preFetchLib('@tanzerfe/common-comps', {
  //   custom: {
  //     host: import.meta.env.DEV ? 'http://localhost:7001' : window.$SYS_CFG.commonCompsURL,
  //     enable: true,
  //   },
  // });
  // await preFetchLib('@tanzerfe/common');

  // await preFetchLib("hel-tpl-remote-vue3-comps-ts", {
  //   custom: {
  //     host: "http://localhost:7001"
  //   }
  // });

  await import('./loadApp');
}

main().catch(console.error);
