/*
 * @Author: xginger <EMAIL>
 * @Date: 2025-07-08 17:39:02
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-07-09 18:37:04
 * @FilePath: \ehs-org-alloc-mgr\vite.config.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import Components from 'unplugin-vue-components/vite';
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers';
import { resolve } from 'path';
import { manifestPlugin } from './plugins/vite/vite-plugin-manifest';
// https://vitejs.dev/config/
export default defineConfig({
  base: './',
  server: {
    port: 8395,
    open: true,
    hmr: {
      overlay: false,
    },
    // proxy: {
    //   // 内外网代理
    //   '/api/v3': {
    //     // target: 'http://**************:8080',
    //     target: 'https://test-bw.gsafetycloud.com',
    //     changeOrigin: true, // 是否改变源地址
    //     // rewrite: (path) => path.replace(/^\/api\/v3/, ''),
    //   },
    // },
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "src/css/variables.scss";`,
        api: 'modern-compiler',
      },
    },
  },
  plugins: [
    vue(),
    Components({
      resolvers: [NaiveUiResolver()],
      directoryAsNamespace: true,
    }),
    manifestPlugin({
      output: 'ehs-org-person-web',
      preload: ['com/lib.js', 'com/config.js'], // index.html 内加载的js 根据实际项⽬填写
      exclude: [], // 忽略⽂件 默认空
      enableLog: false,
    }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      src: resolve(__dirname, 'src'),
    },
  },
  build: {
    outDir: 'ehs-org-person-web',
    manifest: true,
  },
});
