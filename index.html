<!doctype html>
<html lang="zh-hans" class="light">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title id="title">安全组织与人员配备</title>
    <link rel="shortcut icon" type="image/x-icon" id="icon" >
    
    <script src="./com/lib.js"></script>
    <script src="./com/config.js?v=2025-01-14"></script>
</head>
<body>
<div id="app"></div>
<script type="module" src="/src/main.ts">
 
</script>
<!-- <script type="text/javascript">
  // 计算最小高度  
var minHeight = 1080 - (window.screen.height - document.documentElement.clientHeight);  
var minHeight2 = 1080 - (window.screen.height - document.documentElement.clientHeight) - 64;  
document.documentElement.style.setProperty('--min-height', `${minHeight2}px`);  
// 获取元素  
var app = document.getElementById('app');  

// 将最小高度设置到元素的样式  
app.style.minHeight = minHeight + 'px'; // 添加 'px' 单位 
</script> -->
</body>
</html>
