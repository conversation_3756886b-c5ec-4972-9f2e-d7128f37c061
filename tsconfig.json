{
  "compilerOptions": {
    "types": ["vue"],  // 强制包含 Vue 类型声明
    "target": "ES2020",
    "useDefineForClassFields": true,
    // "module": "ES2016",
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,
    

    /* Bundler mode */
    "moduleResolution": "bundler",
    // "moduleResolution": "Node16",
    // "allowImportingTsExtensions": true,
    "allowImportingTsExtensions": false,
    "experimentalDecorators": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",

    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": false,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"],
  "exclude": ["node_modules"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
